package kr.wayplus.wayplus_qr.exception;

import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ResponseStatus;

/**
 * 리소스 생성 시 이미 존재하는 경우 발생하는 예외 (예: 이메일 중복)
 */
@ResponseStatus(HttpStatus.CONFLICT) // 409 Conflict 상태 코드 반환
public class DuplicateResourceException extends RuntimeException {

    public DuplicateResourceException(String message) {
        super(message);
    }

    public DuplicateResourceException(String message, Throwable cause) {
        super(message, cause);
    }
}
