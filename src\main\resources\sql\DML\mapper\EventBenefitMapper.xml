<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.EventBenefitMapper">

    <resultMap id="EventBenefitResultMap" type="kr.wayplus.wayplus_qr.entity.EventBenefit">
        <id property="benefitId" column="benefit_id"/>
        <result property="eventId" column="event_id"/>
        <result property="benefitCode" column="benefit_code"/>
        <result property="benefitName" column="benefit_name"/>
        <result property="description" column="description"/>
        <result property="quantity" column="quantity"/>
        <result property="redeemedQuantity" column="redeemedQuantity"/>
        <result property="status" column="status"/>
        <result property="useYn" column="use_yn"/>
        <result property="deleteYn" column="delete_yn"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdateDate" column="last_update_date"/>
    </resultMap>

    <!-- 이벤트의 활성 혜택 조회 -->
    <select id="selectActiveBenefitsByEventId" resultMap="EventBenefitResultMap">
        SELECT
            eb.*,
            (SELECT COUNT(*) FROM attendee_benefit_redemption abr WHERE abr.benefit_id = eb.benefit_id) as redeemedQuantity
        FROM event_benefits eb
        WHERE eb.event_id = #{eventId}
          AND eb.status = 'ACTIVE'
          AND eb.delete_yn = 'N'
    </select>

    <!-- 혜택 단건 조회 -->
    <select id="selectBenefitById" resultMap="EventBenefitResultMap">
        SELECT
            eb.*,
            (SELECT COUNT(*) FROM attendee_benefit_redemption abr WHERE abr.benefit_id = eb.benefit_id) as redeemedQuantity
        FROM event_benefits eb
        WHERE eb.benefit_id = #{benefitId}
          AND eb.delete_yn = 'N'
    </select>

    <!-- 혜택 삽입 -->
    <insert id="insertEventBenefit" parameterType="kr.wayplus.wayplus_qr.entity.EventBenefit" useGeneratedKeys="true" keyProperty="benefitId">
        INSERT INTO event_benefits (
            event_id,
            benefit_code,
            benefit_name,
            description,
            quantity,
            status,
            use_yn,
            delete_yn,
            create_date,
            create_user_email
        ) VALUES (
            #{eventId},
            #{benefitCode},
            #{benefitName},
            #{description},
            #{quantity},
            #{status},
            #{useYn},
            #{deleteYn},
            NOW(),
            #{createUserEmail}
        )
    </insert>

    <!-- 혜택 수정 -->
    <update id="updateEventBenefit" parameterType="kr.wayplus.wayplus_qr.entity.EventBenefit">
        UPDATE event_benefits
        <set>
            <if test="benefitCode != null">benefit_code = #{benefitCode},</if>
            <if test="benefitName != null">benefit_name = #{benefitName},</if>
            <if test="description != null">description = #{description},</if>
            quantity = #{quantity},
            <if test="status != null">status = #{status},</if>
            last_update_date = NOW(),
            update_user_email = #{updateUserEmail}
        </set>
        WHERE benefit_id = #{benefitId}
          AND delete_yn = 'N'
    </update>

    <!-- 혜택 사용 횟수 카운트 -->
    <select id="countRedeemedByBenefitId" resultType="int">
        SELECT COUNT(*)
        FROM attendee_benefit_redemption
        WHERE benefit_id = #{benefitId}
    </select>

    <!-- benefitCode 중복 검사 -->
    <select id="selectBenefitByCodeAndEventId" resultMap="EventBenefitResultMap">
        SELECT *
        FROM event_benefits
        WHERE event_id = #{eventId}
          AND benefit_code = #{benefitCode}
          AND delete_yn = 'N'
        LIMIT 1
    </select>

    <!-- 이벤트 ID로 삭제되지 않은 모든 혜택 조회 -->
    <select id="selectNonDeletedBenefitsByEventId" resultMap="EventBenefitResultMap">
        SELECT
            eb.*,
            (SELECT COUNT(*) FROM attendee_benefit_redemption abr WHERE abr.benefit_id = eb.benefit_id) as redeemedQuantity
        FROM event_benefits eb
        WHERE eb.event_id = #{eventId}
          AND eb.delete_yn = 'N'
    </select>

    <!-- 이벤트 ID로 모든 혜택 사용 기록 삭제 -->
    <delete id="deleteRedemptionsByEventId" parameterType="long">
        DELETE FROM attendee_benefit_redemption
        WHERE benefit_id IN (SELECT benefit_id FROM event_benefits WHERE event_id = #{eventId})
    </delete>

    <!-- 이벤트 ID로 모든 혜택 삭제 -->
    <delete id="deleteBenefitsByEventId" parameterType="long">
        DELETE FROM event_benefits
        WHERE event_id = #{eventId}
    </delete>

    <!-- 특정 혜택 논리 삭제 -->
    <update id="logicalDeleteEventBenefit">
        UPDATE event_benefits
        SET delete_yn = 'Y',
            last_update_date = NOW(),
            update_user_email = #{userEmail}
        WHERE benefit_id = #{benefitId}
          AND delete_yn = 'N'
    </update>

</mapper>
