package kr.wayplus.wayplus_qr.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * event_benefits 테이블 VO.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventBenefit {
    private Long benefitId;
    private Long eventId;
    private String benefitCode;
    private String benefitName;
    private String description;
    private Integer quantity; // NULL == 무제한, 0 == 재고 없음, N>0 == 재고 N
    private Integer redeemedQuantity; // 사용된 수량 (DB 조회 시 계산)
    private String status; // ACTIVE / INACTIVE
    private String useYn;
    private String deleteYn;
    private LocalDateTime createDate;
    private LocalDateTime lastUpdateDate;
    // 수정자 정보
    private String updateUserEmail;
    // 생성자 정보
    private String createUserEmail;
}
