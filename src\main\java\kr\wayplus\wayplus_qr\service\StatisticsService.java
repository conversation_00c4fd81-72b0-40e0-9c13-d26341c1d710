package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.response.AdminChartDataDto;
import kr.wayplus.wayplus_qr.dto.response.AttendeeChartDataDto;
import kr.wayplus.wayplus_qr.dto.response.ComprehensiveQrStatsDto;
import kr.wayplus.wayplus_qr.dto.response.DailyCountDto;
import kr.wayplus.wayplus_qr.dto.response.DailyRoleCountDto;
import kr.wayplus.wayplus_qr.dto.response.EventChartDataDto;
import kr.wayplus.wayplus_qr.dto.response.QrChartDataDto;
import kr.wayplus.wayplus_qr.dto.response.QrExchangeStatsDto;
import kr.wayplus.wayplus_qr.dto.response.QrScanDeviceStatsDto;
import kr.wayplus.wayplus_qr.dto.response.QrScanTimeStatsDto;
import kr.wayplus.wayplus_qr.dto.response.QrStatusDistributionDto;
import kr.wayplus.wayplus_qr.dto.response.QrTypeDistributionDto;
import kr.wayplus.wayplus_qr.dto.response.TotalQrStatusDto;
import kr.wayplus.wayplus_qr.dto.response.UsageStatisticsResponseDto;
import kr.wayplus.wayplus_qr.dto.response.UserActivityStatsDto;
import kr.wayplus.wayplus_qr.exception.CustomStatisticsException;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.mapper.AttendeeMapper;
import kr.wayplus.wayplus_qr.mapper.EventMapper;
import kr.wayplus.wayplus_qr.mapper.QrCodeMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import kr.wayplus.wayplus_qr.mapper.WebServiceLogMapper;
import kr.wayplus.wayplus_qr.dto.request.UsageStatisticsRequestDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class StatisticsService {

    private final QrCodeMapper qrCodeMapper;
    private final UserMapper userMapper;
    private final EventMapper eventMapper;
    private final AttendeeMapper attendeeMapper;
    private final WebServiceLogMapper webServiceLogMapper;

    private static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    // 관리자 역할 목록 정의
    private static final List<String> ADMIN_ROLES = Arrays.asList("PROJECT_ADMIN", "SUB_ADMIN", "SUPER_ADMIN", "VIEWER");

    @Value("${statistics.top-n.default:5}")
    private int defaultTopN;

    /**
     * 전체 QR 코드 현황 (총 생성 수, 총 스캔 수) 조회
     * @return TotalQrStatusDto
     * @throws CustomStatisticsException 데이터 조회 중 오류 발생 시
     */
    public TotalQrStatusDto getTotalQrStatus(LocalDate startDate, LocalDate endDate) throws CustomStatisticsException {
        log.info("Fetching total QR code status statistics.");
        TotalQrStatusDto status;
        try {
            status = qrCodeMapper.selectTotalQrStatus(startDate, endDate);
        } catch (DataAccessException e) {
            log.error("Database error while fetching total QR status: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_QUERY_FAILED);
        } catch (Exception e) {
            log.error("Unexpected error while fetching total QR status: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_UNEXPECTED_ERROR);
        }

        if (status == null) {
            log.warn("qrCodeMapper.selectTotalQrStatus() returned null. Returning default zero values.");
            return TotalQrStatusDto.builder()
                    .totalQrCodes(0L)
                    .totalScans(0L)
                    .build();
        }
        if (status.getTotalQrCodes() == null) {
            log.warn("Total QR codes count is null, setting to 0.");
            status.setTotalQrCodes(0L);
        }
        if (status.getTotalScans() == null) {
            log.warn("Total scans count is null, setting to 0.");
            status.setTotalScans(0L);
        }

        log.info("Total QR status fetched: totalQrCodes={}, totalScans={}", status.getTotalQrCodes(), status.getTotalScans());
        return status;
    }

    /**
     * 기간별 QR 코드 현황 (일별 생성 수, 일별 스캔 수) 조회 - 차트용
     * @param reqStartDate 조회 시작일 (선택)
     * @param reqEndDate 조회 종료일 (선택)
     * @return QrChartDataDto 날짜 배열, 일별 생성 수 배열, 일별 스캔 수 배열을 포함하는 DTO
     * @throws CustomStatisticsException 데이터 조회 중 오류 발생 시 또는 날짜 파라미터 오류 시
     */
    public QrChartDataDto getQrCodeStatusForChart(LocalDate reqStartDate, LocalDate reqEndDate) throws CustomStatisticsException {
        log.info("Fetching QR code status for chart. Requested StartDate: {}, Requested EndDate: {}", reqStartDate, reqEndDate);

        // --- 날짜 범위 유효성 검사 및 설정 ---
        LocalDate today = LocalDate.now();
        LocalDate startDate;
        LocalDate endDate;

        if (reqStartDate == null && reqEndDate == null) {
            // 둘 다 null: 최근 30일
            endDate = today;
            startDate = endDate.minusDays(29); // 30일 포함
        } else if (reqStartDate == null) {
            // startDate만 null: endDate 기준 30일 전부터 endDate까지
            endDate = reqEndDate;
            startDate = endDate.minusDays(29);
        } else if (reqEndDate == null) {
            // endDate만 null: startDate부터 오늘까지
            startDate = reqStartDate;
            endDate = today;
        } else {
            // 둘 다 제공됨
            startDate = reqStartDate;
            endDate = reqEndDate;
        }

        // 시작일이 종료일보다 늦으면 오류
        if (startDate.isAfter(endDate)) {
            log.warn("Invalid date range: Start date {} is after end date {}.", startDate, endDate);
            throw new CustomStatisticsException(ErrorCode.INVALID_DATE_RANGE);
        }

        // (선택적) 최대 조회 기간 제한 (예: 1년)
        // if (ChronoUnit.DAYS.between(startDate, endDate) > 365) {
        //     log.warn("Date range exceeds maximum allowed limit (365 days): {} to {}", startDate, endDate);
        //     throw new CustomStatisticsException(ErrorCode.DATE_RANGE_TOO_LARGE);
        // }

        log.info("Calculated date range for chart: StartDate: {}, EndDate: {}", startDate, endDate);

        // --- 데이터 조회 ---
        String startDateStr = startDate.format(DATE_FORMATTER);
        String endDateStr = endDate.format(DATE_FORMATTER);

        List<DailyCountDto> dailyCreatedCounts;
        List<DailyCountDto> dailyScannedCounts;
        try {
            dailyCreatedCounts = qrCodeMapper.selectDailyCreatedCounts(startDateStr, endDateStr);
            dailyScannedCounts = qrCodeMapper.selectDailyScannedCounts(startDateStr, endDateStr);
        } catch (DataAccessException e) {
            log.error("Database error while fetching daily QR counts for chart: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_QUERY_FAILED);
        } catch (Exception e) {
            log.error("Unexpected error while fetching daily QR counts for chart: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_UNEXPECTED_ERROR);
        }

        // --- 데이터 가공 ---
        // Map의 키로 LocalDate 대신 'YYYY-MM-DD' 문자열 사용
        Map<String, Long> createdMap = dailyCreatedCounts.stream()
                .collect(Collectors.toMap(dto -> dto.getDate().format(DATE_FORMATTER), DailyCountDto::getCount,
                        (existing, replacement) -> existing)); // 중복 키 발생 시 기존 값 유지 (혹시 모를 상황 대비)
        Map<String, Long> scannedMap = dailyScannedCounts.stream()
                .collect(Collectors.toMap(dto -> dto.getDate().format(DATE_FORMATTER), DailyCountDto::getCount,
                        (existing, replacement) -> existing)); // 중복 키 발생 시 기존 값 유지

        List<String> dates = new ArrayList<>();
        List<Long> createdCounts = new ArrayList<>();
        List<Long> scannedCounts = new ArrayList<>();

        // startDate부터 endDate까지 모든 날짜를 순회하며 결과 리스트 생성 (while 루프로 변경)
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dates.add(currentDate.format(DATE_FORMATTER));
            createdCounts.add(createdMap.getOrDefault(currentDate.format(DATE_FORMATTER), 0L)); // 문자열 키로 조회
            scannedCounts.add(scannedMap.getOrDefault(currentDate.format(DATE_FORMATTER), 0L)); // 문자열 키로 조회
            currentDate = currentDate.plusDays(1); // 다음 날짜로 이동
        }

        QrChartDataDto chartData = new QrChartDataDto(dates, createdCounts, scannedCounts);
        log.info("QR code chart data processed for range {} to {}. Found {} dates.", startDate, endDate, dates.size());
        return chartData;
    }

    /**
     * 기간별 관리자 생성 현황 조회 - 프론트엔드 차트용
     *
     * @param startDate 조회 시작일
     * @param endDate   조회 종료일
     * @param projectId 프로젝트 ID (선택적, null인 경우 전체 관리자 통계 조회)
     * @return AdminChartDataDto 기간별 관리자 생성 수 데이터
     * @throws CustomStatisticsException 데이터 조회 실패 또는 날짜 범위 오류 시
     */
    public AdminChartDataDto getAdminCreationStatsForChart(LocalDate startDate, LocalDate endDate, Long projectId) {
        log.info("Fetching daily admin creation counts for chart between {} and {}. ProjectId: {}", startDate, endDate, projectId);

        if (startDate == null || endDate == null || startDate.isAfter(endDate)) {
            log.warn("Invalid date range provided for admin creation chart: startDate={}, endDate={}", startDate, endDate);
            throw new CustomStatisticsException(ErrorCode.INVALID_DATE_RANGE);
        }

        String startDateStr = startDate.format(DATE_FORMATTER);
        String endDateStr = endDate.format(DATE_FORMATTER);

        List<DailyRoleCountDto> dailyRoleCounts;
        try {
            // projectId 파라미터를 전달하여 통계 조회 (null이면 전체 조회)
            dailyRoleCounts = userMapper.selectDailyAdminCreationCounts(projectId, startDateStr, endDateStr);
            // === 디버깅 로그 추가 1: DB 조회 결과 확인 ===
            log.debug("Raw dailyRoleCounts from DB: {}", dailyRoleCounts.stream()
                    .map(dto -> "Date:" + dto.getDate() + ", Role:" + dto.getRoleId() + ", Count:" + dto.getCount())
                    .collect(Collectors.joining("; ")));
        } catch (DataAccessException e) {
            log.error("Database error while fetching daily admin creation counts for chart: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_QUERY_FAILED);
        }

        // 1. 날짜 목록 생성
        List<String> dates = new ArrayList<>();
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dates.add(currentDate.format(DATE_FORMATTER));
            currentDate = currentDate.plusDays(1);
        }
        // === 디버깅 로그 추가 2: 생성된 날짜 리스트 확인 ===
        log.debug("Generated dates list before creating DTO: {}", dates);

        // 2. DB 결과를 날짜-역할-카운트 맵으로 변환
        Map<String, Map<String, Long>> dateRoleCountMap = new HashMap<>();
        for (DailyRoleCountDto dto : dailyRoleCounts) {
            dateRoleCountMap
                    .computeIfAbsent(dto.getDate(), k -> new HashMap<>()) // 날짜 키가 없으면 새 Map 생성
                    .put(dto.getRoleId(), dto.getCount()); // 역할과 카운트 저장
        }

        // 3. 최종 결과 맵(역할별 카운트 리스트) 생성 및 초기화
        Map<String, List<Long>> finalCreatedCounts = new HashMap<>();
        for (String role : ADMIN_ROLES) {
            // 각 역할에 대해 날짜 수만큼 0으로 채워진 리스트 생성
            finalCreatedCounts.put(role, new ArrayList<>(Collections.nCopies(dates.size(), 0L)));
        }

        // 4. 최종 결과 맵 채우기
        for (int i = 0; i < dates.size(); i++) {
            String dateStr = dates.get(i);
            Map<String, Long> roleCountsForDate = dateRoleCountMap.getOrDefault(dateStr, Collections.emptyMap());

            for (String role : ADMIN_ROLES) {
                long count = roleCountsForDate.getOrDefault(role, 0L);
                // 해당 역할 리스트의 i번째 인덱스에 카운트 설정
                finalCreatedCounts.get(role).set(i, count);
            }
        }

        // 5. 최종 DTO 생성 및 반환 (변경된 생성자 사용)
        AdminChartDataDto chartData = new AdminChartDataDto(dates, finalCreatedCounts);
        log.info("Admin creation chart data processed for range {} to {}. Found {} dates.", startDate, endDate, dates.size());
        // === 디버깅 로그 추가 3: 최종 DTO 객체 확인 (선택 사항) ===
        // log.debug("Final AdminChartDataDto object: {}", chartData);
        return chartData;
    }

    /**
     * 기간별 일일 이벤트 생성 통계를 차트용 데이터로 반환합니다.
     *
     * @param startDate 시작 날짜
     * @param endDate   종료 날짜
     * @return EventChartDataDto
     */
    public EventChartDataDto getEventCreationStatsForChart(LocalDate startDate, LocalDate endDate) {
        log.info("Fetching event creation chart data for range {} to {}", startDate, endDate);

        String start = startDate.format(DATE_FORMATTER);
        String end = endDate.format(DATE_FORMATTER);

        // 2. DB에서 기간 내 일일 이벤트 생성 수 조회
        List<DailyCountDto> dailyCounts = eventMapper.selectDailyEventCreationCounts(start, end);
        Map<String, Long> countsMap = dailyCounts.stream()
                .collect(Collectors.toMap(
                        dto -> dto.getDate().format(DATE_FORMATTER), // LocalDate를 String으로 포맷
                        DailyCountDto::getCount,
                        (existing, replacement) -> existing) // 키 충돌 시 기존 값 유지 (이론상 발생 안 함)
                );

        log.debug("Raw daily event counts from DB: {}", dailyCounts);

        // 3. 날짜 목록 생성 및 결과 데이터 가공
        List<String> dates = new ArrayList<>();
        List<Long> createdCounts = new ArrayList<>();
        LocalDate currentDate = startDate;

        while (!currentDate.isAfter(endDate)) {
            dates.add(currentDate.format(DATE_FORMATTER));
            createdCounts.add(countsMap.getOrDefault(currentDate.format(DATE_FORMATTER), 0L)); // 해당 날짜 데이터 없으면 0
            currentDate = currentDate.plusDays(1);
        }

        log.debug("Generated dates list before creating DTO: {}", dates);
        log.debug("Generated counts list before creating DTO: {}", createdCounts);

        EventChartDataDto chartData = new EventChartDataDto(dates, createdCounts);
        log.info("Event creation chart data processed for range {} to {}. Found {} dates.", startDate, endDate, dates.size());
        return chartData;
    }

    /**
     * 기간별 일일 참가자 등록 통계를 차트용 데이터로 반환합니다.
     *
     * @param startDate 시작 날짜
     * @param endDate   종료 날짜
     * @return AttendeeChartDataDto
     */
    public AttendeeChartDataDto getAttendeeRegistrationStatsForChart(LocalDate startDate, LocalDate endDate) {
        log.info("Fetching attendee registration chart data for range {} to {}", startDate, endDate);

        String start = startDate.format(DATE_FORMATTER);
        String end = endDate.format(DATE_FORMATTER);

        // 2. DB에서 기간 내 일일 참가자 등록 수 조회
        List<DailyCountDto> dailyCounts = attendeeMapper.selectDailyAttendeeRegistrationCounts(start, end);
        Map<String, Long> countsMap = dailyCounts.stream()
                .collect(Collectors.toMap(
                        dto -> dto.getDate().format(DATE_FORMATTER),
                        DailyCountDto::getCount,
                        (existing, replacement) -> existing) // 키 충돌 시 기존 값 유지
                );

        log.debug("Raw daily attendee counts from DB: {}", dailyCounts);

        // 3. 날짜 목록 생성 및 결과 데이터 가공
        List<String> dates = new ArrayList<>();
        List<Long> registeredCounts = new ArrayList<>();
        LocalDate currentDate = startDate;

        while (!currentDate.isAfter(endDate)) {
            dates.add(currentDate.format(DATE_FORMATTER));
            registeredCounts.add(countsMap.getOrDefault(currentDate.format(DATE_FORMATTER), 0L)); // 해당 날짜 데이터 없으면 0
            currentDate = currentDate.plusDays(1);
        }

        log.debug("Generated dates list for attendee chart: {}", dates);
        log.debug("Generated counts list for attendee chart: {}", registeredCounts);

        AttendeeChartDataDto chartData = new AttendeeChartDataDto(dates, registeredCounts);
        log.info("Attendee registration chart data processed for range {} to {}. Found {} dates.", startDate, endDate, dates.size());
        return chartData;
    }

    /**
     * 관리자 현황 (역할별 인원 수, SUPER_ADMIN 제외) 조회
     * @param projectId 프로젝트 ID (선택적, null인 경우 전체 관리자 통계 조회)
     * @return Map<String, Long> 역할 이름과 해당 역할의 관리자 수를 담은 Map
     * @throws CustomStatisticsException 데이터 조회 중 오류 발생 시
     */
    public Map<String, Long> getAdminStatistics(Long projectId) throws CustomStatisticsException {
        log.info("Fetching admin statistics by role. ProjectId: {}", projectId);
        List<Map<String, Object>> roleCountsRaw;
        try {
            // 프로젝트 ID를 파라미터로 전달하여 통계 조회
            roleCountsRaw = userMapper.selectAdminRoleCounts(projectId);
        } catch (DataAccessException e) {
            log.error("Database error while fetching admin role counts: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_QUERY_FAILED);
        } catch (Exception e) {
            log.error("Unexpected error while fetching admin role counts: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_UNEXPECTED_ERROR);
        }

        // 결과가 null이거나 비어있는 경우 빈 맵 반환
        if (roleCountsRaw == null || roleCountsRaw.isEmpty()) {
            return new HashMap<>();
        }

        // MyBatis는 숫자 타입을 다양한 형태(Integer, Long, BigDecimal 등)로 반환할 수 있으므로, Long으로 변환
        Map<String, Long> adminStatistics = new HashMap<>();
        for (Map<String, Object> roleCount : roleCountsRaw) {
            String roleId = (String) roleCount.get("role_id");
            Number countNum = (Number) roleCount.get("count");

            if (roleId != null && countNum != null) {
                Long count = countNum.longValue();
                adminStatistics.put(roleId, count);
            }
        }

        log.info("Admin statistics fetched: {}", adminStatistics);
        return adminStatistics;
    }

    /**
     * 전체 이벤트 수 조회 (use_yn='Y', delete_yn='N' 조건)
     * @return Map<String, Long> "totalEventCount" 키와 전체 이벤트 수를 값으로 가지는 Map
     * @throws CustomStatisticsException 데이터 조회 중 오류 발생 시
     */
    public Map<String, Long> getTotalEventCount() throws CustomStatisticsException {
        log.info("Fetching total event count (use_yn='Y', delete_yn='N').");
        long totalCount;
        try {
            totalCount = eventMapper.selectTotalEventCount();
        } catch (DataAccessException e) {
            log.error("Database error while fetching total event count: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_QUERY_FAILED);
        } catch (Exception e) {
            log.error("Unexpected error while fetching total event count: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_UNEXPECTED_ERROR);
        }
        log.info("Total event count fetched: {}", totalCount);
        Map<String, Long> result = new HashMap<>();
        result.put("totalEventCount", totalCount);
        return result;
    }

    /**
     * 전체 참가자 수 조회 (use_yn='Y', delete_yn='N' 조건)
     * @return Map<String, Long> "totalAttendeeCount" 키와 전체 참가자 수를 값으로 가지는 Map
     * @throws CustomStatisticsException 데이터 조회 중 오류 발생 시
     */
    public Map<String, Long> getTotalAttendeeCount() throws CustomStatisticsException {
        log.info("Fetching total attendee count (use_yn='Y', delete_yn='N').");
        long totalCount;
        try {
            totalCount = attendeeMapper.selectTotalAttendeeCount();
        } catch (DataAccessException e) {
            log.error("Database error while fetching total attendee count: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_QUERY_FAILED);
        } catch (Exception e) {
            log.error("Unexpected error while fetching total attendee count: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_UNEXPECTED_ERROR);
        }
        log.info("Total attendee count fetched: {}", totalCount);
        Map<String, Long> result = new HashMap<>();
        result.put("totalAttendeeCount", totalCount);
        return result;
    }

    /**
     * 프로젝트별 QR 코드 현황 (일별 생성/스캔) 차트 데이터
     */
    public QrChartDataDto getProjectQrCodeStatusForChart(Long projectId, LocalDate startDate, LocalDate endDate) {
        // 날짜 유효성 동일 로직 재사용
        if (startDate == null || endDate == null || startDate.isAfter(endDate)) {
            throw new CustomStatisticsException(ErrorCode.INVALID_DATE_RANGE);
        }
        String start = startDate.format(DATE_FORMATTER);
        String end = endDate.format(DATE_FORMATTER);

        List<DailyCountDto> dailyCreated = qrCodeMapper.selectDailyCreatedCountsByProject(projectId, start, end);
        List<DailyCountDto> dailyScanned = qrCodeMapper.selectDailyScannedCountsByProject(projectId, start, end);

        Map<String, Long> createdMap = dailyCreated.stream()
                .collect(Collectors.toMap(dto -> dto.getDate().format(DATE_FORMATTER), DailyCountDto::getCount));
        Map<String, Long> scannedMap = dailyScanned.stream()
                .collect(Collectors.toMap(dto -> dto.getDate().format(DATE_FORMATTER), DailyCountDto::getCount));

        List<String> dates = new ArrayList<>();
        List<Long> createdCounts = new ArrayList<>();
        List<Long> scannedCounts = new ArrayList<>();
        LocalDate cur = startDate;
        while (!cur.isAfter(endDate)) {
            String key = cur.format(DATE_FORMATTER);
            dates.add(key);
            createdCounts.add(createdMap.getOrDefault(key, 0L));
            scannedCounts.add(scannedMap.getOrDefault(key, 0L));
            cur = cur.plusDays(1);
        }
        return new QrChartDataDto(dates, createdCounts, scannedCounts);
    }

    /**
     * 프로젝트별 이벤트 생성 차트 데이터
     */
    public EventChartDataDto getProjectEventCreationStatsForChart(Long projectId, LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null || startDate.isAfter(endDate)) {
            throw new CustomStatisticsException(ErrorCode.INVALID_DATE_RANGE);
        }
        String start = startDate.format(DATE_FORMATTER);
        String end = endDate.format(DATE_FORMATTER);
        List<DailyCountDto> daily = eventMapper.selectDailyEventCreationCountsByProject(projectId, start, end);
        Map<String, Long> map = daily.stream().collect(Collectors.toMap(dto -> dto.getDate().format(DATE_FORMATTER), DailyCountDto::getCount));
        List<String> dates = new ArrayList<>();
        List<Long> counts = new ArrayList<>();
        for (LocalDate d = startDate; !d.isAfter(endDate); d = d.plusDays(1)) {
            String key = d.format(DATE_FORMATTER);
            dates.add(key);
            counts.add(map.getOrDefault(key, 0L));
        }
        return new EventChartDataDto(dates, counts);
    }

    /**
     * 프로젝트별 참가자 등록 차트 데이터
     */
    public AttendeeChartDataDto getProjectAttendeeRegistrationStatsForChart(Long projectId, LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null || startDate.isAfter(endDate)) {
            throw new CustomStatisticsException(ErrorCode.INVALID_DATE_RANGE);
        }
        String start = startDate.format(DATE_FORMATTER);
        String end = endDate.format(DATE_FORMATTER);
        List<DailyCountDto> daily = attendeeMapper.selectDailyAttendeeRegistrationCountsByProject(projectId, start, end);
        Map<String, Long> map = daily.stream().collect(Collectors.toMap(dto -> dto.getDate().format(DATE_FORMATTER), DailyCountDto::getCount));
        List<String> dates = new ArrayList<>();
        List<Long> counts = new ArrayList<>();
        for (LocalDate d = startDate; !d.isAfter(endDate); d = d.plusDays(1)) {
            String key = d.format(DATE_FORMATTER);
            dates.add(key);
            counts.add(map.getOrDefault(key, 0L));
        }
        return new AttendeeChartDataDto(dates, counts);
    }

    /**
     * 프로젝트별 관리자 생성 차트 데이터 (역할별)
     */
    public AdminChartDataDto getProjectAdminCreationStatsForChart(Long projectId, LocalDate startDate, LocalDate endDate) {
        if (startDate == null || endDate == null || startDate.isAfter(endDate)) {
            throw new CustomStatisticsException(ErrorCode.INVALID_DATE_RANGE);
        }
        String start = startDate.format(DATE_FORMATTER);
        String end = endDate.format(DATE_FORMATTER);
        List<DailyRoleCountDto> daily = userMapper.selectDailyAdminCreationCounts(projectId, start, end);
        // build map similar to existing logic
        List<String> dates = new ArrayList<>();
        for (LocalDate d = startDate; !d.isAfter(endDate); d = d.plusDays(1)) {
            dates.add(d.format(DATE_FORMATTER));
        }
        Map<String, List<Long>> finalMap = new HashMap<>();
        for (String role : ADMIN_ROLES) {
            finalMap.put(role, new ArrayList<>(Collections.nCopies(dates.size(), 0L)));
        }
        // accumulate
        for (DailyRoleCountDto dto : daily) {
            int idx = dates.indexOf(dto.getDate());
            if (idx >= 0) {
                finalMap.computeIfAbsent(dto.getRoleId(), r -> new ArrayList<>(Collections.nCopies(dates.size(), 0L)))
                        .set(idx, dto.getCount());
            }
        }
        return new AdminChartDataDto(dates, finalMap);
    }

    /**
     * 프로젝트별 QR 총계 (한 달 기간의 날짜별 생성 수와 스캔 수 포함)
     */
    public TotalQrStatusDto getProjectTotalQrStatus(Long projectId, LocalDate startDate, LocalDate endDate) {
        try {
            // 기본 통계 정보 조회
            TotalQrStatusDto baseStats = qrCodeMapper.selectProjectTotalQrStatus(projectId, startDate, endDate);

            // 날짜를 형식에 맞게 변환 (null 체크 포함)
            String startDateStr = startDate != null ? startDate.format(DATE_FORMATTER) : null;
            String endDateStr = endDate != null ? endDate.format(DATE_FORMATTER) : null;

            // 날짜별 QR 코드 생성 수 및 스캔 수 조회
            List<DailyCountDto> dailyCreatedCounts = qrCodeMapper.selectDailyCreatedCountsByProject(projectId, startDateStr, endDateStr);
            List<DailyCountDto> dailyScannedCounts = qrCodeMapper.selectDailyScannedCountsByProject(projectId, startDateStr, endDateStr);

            // 결과 생성
            return TotalQrStatusDto.builder()
                    .totalQrCodes(baseStats.getTotalQrCodes())
                    .totalScans(baseStats.getTotalScans())
                    .dailyCreatedCounts(dailyCreatedCounts)
                    .dailyScannedCounts(dailyScannedCounts)
                    .build();
        } catch (DataAccessException e) {
            throw new CustomStatisticsException(ErrorCode.STATISTICS_QUERY_FAILED);
        }
    }

    public Map<String, Long> getProjectTotalEventCount(Long projectId) {
        long count = eventMapper.countEvents(projectId, null, null, null);
        Map<String, Long> map = new HashMap<>();
        map.put("totalEventCount", count);
        return map;
    }

    /**
     * 프로젝트별 참가자 총수
     */
    public Map<String, Long> getProjectTotalAttendeeCount(Long projectId) {
        long count = attendeeMapper.countAttendeesByProjectId(projectId, null, null);
        Map<String, Long> map = new HashMap<>();
        map.put("totalAttendeeCount", count);
        return map;
    }

    /**
     * QR 코드 타입별 분포 조회
     * @param projectId 프로젝트 ID (선택적)
     * @param startDate 시작일
     * @param endDate 종료일
     * @return QrTypeDistributionDto
     */
    public QrTypeDistributionDto getQrTypeDistribution(Long projectId, LocalDate startDate, LocalDate endDate) {
        List<Map<String, Object>> typeDistributionData = qrCodeMapper.selectQrTypeDistribution(projectId, startDate, endDate);

        Map<String, Long> typeDistribution = new HashMap<>();
        Map<String, Double> typePercentage = new HashMap<>();

        long total = 0;
        for (Map<String, Object> data : typeDistributionData) {
            String type = (String) data.get("type");
            Long count = ((Number) data.get("count")).longValue();
            typeDistribution.put(type, count);
            total += count;
        }

        // 비율 계산
        if (total > 0) {
            for (Map.Entry<String, Long> entry : typeDistribution.entrySet()) {
                double percentage = (entry.getValue() * 100.0) / total;
                typePercentage.put(entry.getKey(), Math.round(percentage * 100) / 100.0); // 소수점 2자리까지
            }
        }

        return new QrTypeDistributionDto(typeDistribution, typePercentage);
    }

    /**
     * QR 코드 상태별 분포 조회
     * @param projectId 프로젝트 ID (선택적)
     * @param startDate 시작일
     * @param endDate 종료일
     * @return QrStatusDistributionDto
     */
    public QrStatusDistributionDto getQrStatusDistribution(Long projectId, LocalDate startDate, LocalDate endDate) {
        List<Map<String, Object>> statusDistributionData = qrCodeMapper.selectQrStatusDistribution(projectId, startDate, endDate);

        Map<String, Long> statusCounts = new HashMap<>();
        Map<String, Double> statusPercentage = new HashMap<>();

        long total = 0;
        for (Map<String, Object> data : statusDistributionData) {
            String status = (String) data.get("status");
            Long count = ((Number) data.get("count")).longValue();
            statusCounts.put(status, count);
            total += count;
        }

        // 비율 계산
        if (total > 0) {
            for (Map.Entry<String, Long> entry : statusCounts.entrySet()) {
                double percentage = (entry.getValue() * 100.0) / total;
                statusPercentage.put(entry.getKey(), Math.round(percentage * 100) / 100.0);
            }
        }

        return new QrStatusDistributionDto(statusCounts, statusPercentage);
    }

    /**
     * QR 코드 스캔 기기 통계 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return QrScanDeviceStatsDto
     */
    public QrScanDeviceStatsDto getQrScanDeviceStats(LocalDate startDate, LocalDate endDate, Long projectId) {
        String startDateStr = startDate.format(DATE_FORMATTER);
        String endDateStr = endDate.format(DATE_FORMATTER);

        List<Map<String, Object>> deviceStats = qrCodeMapper.selectQrScanDeviceStats(startDateStr, endDateStr, projectId);
        List<Map<String, Object>> browserStats = qrCodeMapper.selectQrScanBrowserStats(startDateStr, endDateStr, projectId);
        List<Map<String, Object>> osStats = qrCodeMapper.selectQrScanOsStats(startDateStr, endDateStr, projectId);

        Map<String, Long> deviceCounts = new HashMap<>();
        Map<String, Double> devicePercentage = new HashMap<>();
        Map<String, Long> browserCounts = new HashMap<>();
        Map<String, Double> browserPercentage = new HashMap<>();
        Map<String, Long> osCounts = new HashMap<>();
        Map<String, Double> osPercentage = new HashMap<>();

        // 기기별 통계 처리
        long deviceTotal = 0;
        for (Map<String, Object> data : deviceStats) {
            String deviceType = (String) data.get("device_type");
            Long count = ((Number) data.get("count")).longValue();
            deviceCounts.put(deviceType, count);
            deviceTotal += count;
        }

        // 브라우저별 통계 처리
        long browserTotal = 0;
        for (Map<String, Object> data : browserStats) {
            String browserType = (String) data.get("browser_type");
            Long count = ((Number) data.get("count")).longValue();
            browserCounts.put(browserType, count);
            browserTotal += count;
        }

        // OS별 통계 처리
        long osTotal = 0;
        for (Map<String, Object> data : osStats) {
            String osType = (String) data.get("os_type");
            Long count = ((Number) data.get("count")).longValue();
            osCounts.put(osType, count);
            osTotal += count;
        }

        // 비율 계산
        if (deviceTotal > 0) {
            for (Map.Entry<String, Long> entry : deviceCounts.entrySet()) {
                double percentage = (entry.getValue() * 100.0) / deviceTotal;
                devicePercentage.put(entry.getKey(), Math.round(percentage * 100) / 100.0);
            }
        }

        if (browserTotal > 0) {
            for (Map.Entry<String, Long> entry : browserCounts.entrySet()) {
                double percentage = (entry.getValue() * 100.0) / browserTotal;
                browserPercentage.put(entry.getKey(), Math.round(percentage * 100) / 100.0);
            }
        }

        if (osTotal > 0) {
            for (Map.Entry<String, Long> entry : osCounts.entrySet()) {
                double percentage = (entry.getValue() * 100.0) / osTotal;
                osPercentage.put(entry.getKey(), Math.round(percentage * 100) / 100.0);
            }
        }

        return new QrScanDeviceStatsDto(deviceCounts, devicePercentage, browserCounts, browserPercentage, osCounts, osPercentage);
    }

    /**
     * QR 코드 스캔 시간 통계 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return QrScanTimeStatsDto
     */
    public QrScanTimeStatsDto getQrScanTimeStats(LocalDate startDate, LocalDate endDate, Long projectId) {
        String startDateStr = startDate.format(DATE_FORMATTER);
        String endDateStr = endDate.format(DATE_FORMATTER);

        List<Map<String, Object>> hourlyStats = qrCodeMapper.selectQrScanHourlyDistribution(startDateStr, endDateStr, projectId);
        List<Map<String, Object>> weekdayStats = qrCodeMapper.selectQrScanWeekdayDistribution(startDateStr, endDateStr, projectId);

        Map<Integer, Long> hourlyDistribution = new HashMap<>();
        Map<String, Long> weekdayDistribution = new HashMap<>();

        // 시간대별 통계 처리
        for (Map<String, Object> data : hourlyStats) {
            Integer hour = ((Number) data.get("hour")).intValue();
            Long count = ((Number) data.get("count")).longValue();
            hourlyDistribution.put(hour, count);
        }

        // 요일별 통계 처리
        for (Map<String, Object> data : weekdayStats) {
            String weekday = (String) data.get("weekday");
            Long count = ((Number) data.get("count")).longValue();
            weekdayDistribution.put(weekday, count);
        }

        return new QrScanTimeStatsDto(hourlyDistribution, weekdayDistribution);
    }

    /**
     * 교환권 QR 코드 통계 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return QrExchangeStatsDto
     */
    public QrExchangeStatsDto getQrExchangeStats(LocalDate startDate, LocalDate endDate, Long projectId) {
        String startDateStr = startDate.format(DATE_FORMATTER);
        String endDateStr = endDate.format(DATE_FORMATTER);

        Map<String, Object> exchangeStats = qrCodeMapper.selectExchangeQrStats(projectId, startDateStr, endDateStr);
        List<DailyCountDto> dailyApprovalCounts = qrCodeMapper.selectDailyExchangeApprovalCounts(startDateStr, endDateStr, projectId);
        List<Map<String, Object>> approverStats = qrCodeMapper.selectApproverExchangeCounts(startDateStr, endDateStr, projectId);
        List<Map<String, Object>> hourlyApprovalStats = qrCodeMapper.selectHourlyExchangeApprovalCounts(startDateStr, endDateStr, projectId);

        Long totalExchangeQrCodes = ((Number) exchangeStats.get("totalExchangeQrCodes")).longValue();
        Long totalExchangeCount = ((Number) exchangeStats.get("totalExchangeCount")).longValue();
        Long totalUsedCount = ((Number) exchangeStats.get("totalUsedCount")).longValue();

        // 사용률 계산
        Double usageRate = 0.0;
        if (totalExchangeCount > 0) {
            usageRate = (totalUsedCount * 100.0) / totalExchangeCount;
            usageRate = Math.round(usageRate * 100) / 100.0; // 소수점 2자리까지
        }

        // 일별 승인 횟수 처리
        List<String> dates = new ArrayList<>();
        List<Long> approvalCounts = new ArrayList<>();

        // 날짜 범위 생성
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            dates.add(currentDate.format(DATE_FORMATTER));
            currentDate = currentDate.plusDays(1);
        }

        // 일별 승인 횟수 매핑
        Map<String, Long> dailyApprovalMap = dailyApprovalCounts.stream()
                .collect(Collectors.toMap(dto -> dto.getDate().format(DATE_FORMATTER), DailyCountDto::getCount));

        // 결과 리스트 생성
        for (String date : dates) {
            approvalCounts.add(dailyApprovalMap.getOrDefault(date, 0L));
        }

        // 승인자별 통계 처리
        Map<String, Long> approverCounts = new HashMap<>();
        Map<String, Double> approverPercentage = new HashMap<>();

        long approverTotal = 0;
        for (Map<String, Object> data : approverStats) {
            String approver = (String) data.get("approver");
            Long count = ((Number) data.get("count")).longValue();
            approverCounts.put(approver, count);
            approverTotal += count;
        }

        // 승인자별 비율 계산
        if (approverTotal > 0) {
            for (Map.Entry<String, Long> entry : approverCounts.entrySet()) {
                double percentage = (entry.getValue() * 100.0) / approverTotal;
                approverPercentage.put(entry.getKey(), Math.round(percentage * 100) / 100.0);
            }
        }

        // 시간대별 승인 횟수 처리
        Map<String, Long> hourlyApprovalCounts = new HashMap<>();
        for (Map<String, Object> data : hourlyApprovalStats) {
            Integer hour = ((Number) data.get("hour")).intValue();
            Long count = ((Number) data.get("count")).longValue();
            hourlyApprovalCounts.put(hour.toString(), count);
        }

        return new QrExchangeStatsDto(
                totalExchangeQrCodes,
                totalExchangeCount,
                totalUsedCount,
                usageRate,
                dates,
                approvalCounts,
                approverCounts,
                approverPercentage,
                hourlyApprovalCounts
        );
    }

    /**
     * 통합 QR 코드 통계 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @param includeTypes 포함할 통계 유형 (쉼표로 구분된 문자열, 기본값: 모든 유형)
     * @return ComprehensiveQrStatsDto
     */
    public ComprehensiveQrStatsDto getComprehensiveQrStats(LocalDate startDate, LocalDate endDate, Long projectId, String includeTypes) {
        log.info("Fetching comprehensive QR statistics. StartDate: {}, EndDate: {}, ProjectId: {}, IncludeTypes: {}",
                startDate, endDate, projectId, includeTypes);

        // 포함할 통계 유형 파싱
        Set<String> types = new HashSet<>();
        if (includeTypes != null && !includeTypes.trim().isEmpty()) {
            types.addAll(Arrays.asList(includeTypes.split(",")));
        } else {
            // 기본값: 모든 유형 포함
            types.addAll(Arrays.asList("basic", "daily", "type", "status", "device", "time", "exchange", "scannedQrCodes"));
        }

        ComprehensiveQrStatsDto.ComprehensiveQrStatsDtoBuilder builder = ComprehensiveQrStatsDto.builder();

        // 1. 기본 통계
        if (types.contains("basic")) {
            TotalQrStatusDto totalStatus = projectId == null ?
                    getTotalQrStatus(startDate, endDate) : getProjectTotalQrStatus(projectId, startDate, endDate);

            builder.basic(new ComprehensiveQrStatsDto.BasicStats(
                    totalStatus.getTotalQrCodes(),
                    totalStatus.getTotalScans()
            ));
        }

        // 1-1. 일별 QR 코드 생성 및 스캔 통계
        if (types.contains("daily") || types.contains("basic")) {
            // 데이터 조회 시작일과 종료일 설정 (파라미터가 없으면 한 달 기간 사용)
            LocalDate queryEndDate = endDate != null ? endDate : LocalDate.now();
            LocalDate queryStartDate = startDate != null ? startDate : queryEndDate.minusMonths(1);

            String queryStartDateStr = queryStartDate.format(DATE_FORMATTER);
            String queryEndDateStr = queryEndDate.format(DATE_FORMATTER);

            // 일별 QR 코드 생성 수 및 스캔 수 조회
            // projectId가 null이면 전체 프로젝트를 대상으로 조회
            List<DailyCountDto> dailyCreatedCounts;
            List<DailyCountDto> dailyScannedCounts;

            if (projectId == null) {
                // 전체 프로젝트 대상 조회
                dailyCreatedCounts = qrCodeMapper.selectDailyCreatedCounts(queryStartDateStr, queryEndDateStr);
                dailyScannedCounts = qrCodeMapper.selectDailyScannedCounts(queryStartDateStr, queryEndDateStr);
                log.info("Fetching daily QR stats for ALL projects from {} to {}", queryStartDateStr, queryEndDateStr);
            } else {
                // 특정 프로젝트 대상 조회
                dailyCreatedCounts = qrCodeMapper.selectDailyCreatedCountsByProject(projectId, queryStartDateStr, queryEndDateStr);
                dailyScannedCounts = qrCodeMapper.selectDailyScannedCountsByProject(projectId, queryStartDateStr, queryEndDateStr);
                log.info("Fetching daily QR stats for project ID {} from {} to {}", projectId, queryStartDateStr, queryEndDateStr);
            }

            // 데이터 가공
            Map<String, Long> createdMap = dailyCreatedCounts.stream()
                    .collect(Collectors.toMap(dto -> dto.getDate().format(DATE_FORMATTER), DailyCountDto::getCount,
                            (existing, replacement) -> existing)); // 중복 키 발생 시 기존 값 유지 (혹시 모를 상황 대비)
            Map<String, Long> scannedMap = dailyScannedCounts.stream()
                    .collect(Collectors.toMap(dto -> dto.getDate().format(DATE_FORMATTER), DailyCountDto::getCount,
                            (existing, replacement) -> existing)); // 중복 키 발생 시 기존 값 유지

            // 결과 리스트 생성
            List<String> dates = new ArrayList<>();
            List<Long> createdCounts = new ArrayList<>();
            List<Long> scannedCounts = new ArrayList<>();

            LocalDate currentDate = queryStartDate;
            while (!currentDate.isAfter(queryEndDate)) {
                dates.add(currentDate.format(DATE_FORMATTER));
                createdCounts.add(createdMap.getOrDefault(currentDate.format(DATE_FORMATTER), 0L)); // 문자열 키로 조회
                scannedCounts.add(scannedMap.getOrDefault(currentDate.format(DATE_FORMATTER), 0L)); // 문자열 키로 조회
                currentDate = currentDate.plusDays(1);
            }

            builder.dailyQrStats(new ComprehensiveQrStatsDto.DailyQrStats(dates, createdCounts, scannedCounts));
        }

        // 2. 타입별 분포
        if (types.contains("type")) {
            QrTypeDistributionDto typeDistribution = getQrTypeDistribution(projectId, startDate, endDate);

            builder.typeDistribution(new ComprehensiveQrStatsDto.TypeDistribution(
                    typeDistribution.getTypeDistribution(),
                    typeDistribution.getTypePercentage()
            ));
        }

        // 3. 상태별 분포
        if (types.contains("status")) {
            QrStatusDistributionDto statusDistribution = getQrStatusDistribution(projectId, startDate, endDate);

            builder.statusDistribution(new ComprehensiveQrStatsDto.StatusDistribution(
                    statusDistribution.getStatusCounts(),
                    statusDistribution.getStatusPercentage()
            ));
        }

        // 4. 기기별 통계
        if (types.contains("device")) {
            QrScanDeviceStatsDto deviceStats = getQrScanDeviceStats(startDate, endDate, projectId);

            ComprehensiveQrStatsDto.DeviceStats.DeviceTypeStats deviceTypeStats =
                    new ComprehensiveQrStatsDto.DeviceStats.DeviceTypeStats(
                            deviceStats.getDeviceCounts(),
                            deviceStats.getDevicePercentage()
                    );

            ComprehensiveQrStatsDto.DeviceStats.DeviceTypeStats browserTypeStats =
                    new ComprehensiveQrStatsDto.DeviceStats.DeviceTypeStats(
                            deviceStats.getBrowserCounts(),
                            deviceStats.getBrowserPercentage()
                    );

            ComprehensiveQrStatsDto.DeviceStats.DeviceTypeStats osTypeStats =
                    new ComprehensiveQrStatsDto.DeviceStats.DeviceTypeStats(
                            deviceStats.getOsCounts(),
                            deviceStats.getOsPercentage()
                    );

            builder.deviceStats(new ComprehensiveQrStatsDto.DeviceStats(
                    deviceTypeStats,
                    browserTypeStats,
                    osTypeStats
            ));
        }

        // 5. 시간별 통계
        if (types.contains("time")) {
            QrScanTimeStatsDto timeStats = getQrScanTimeStats(startDate, endDate, projectId);

            builder.timeStats(new ComprehensiveQrStatsDto.TimeStats(
                    timeStats.getHourlyDistribution(),
                    timeStats.getWeekdayDistribution()
            ));
        }

        // 6. 교환권 통계
        if (types.contains("exchange")) {
            QrExchangeStatsDto exchangeStats = getQrExchangeStats(startDate, endDate, projectId);

            ComprehensiveQrStatsDto.ExchangeStats.ExchangeTotal total =
                    new ComprehensiveQrStatsDto.ExchangeStats.ExchangeTotal(
                            exchangeStats.getTotalExchangeQrCodes(),
                            exchangeStats.getTotalExchangeCount(),
                            exchangeStats.getTotalUsedCount(),
                            exchangeStats.getUsageRate()
                    );

            ComprehensiveQrStatsDto.ExchangeStats.ExchangeDaily daily =
                    new ComprehensiveQrStatsDto.ExchangeStats.ExchangeDaily(
                            exchangeStats.getDates(),
                            exchangeStats.getApprovalCounts()
                    );

            ComprehensiveQrStatsDto.ExchangeStats.ExchangeApprover approver =
                    new ComprehensiveQrStatsDto.ExchangeStats.ExchangeApprover(
                            exchangeStats.getApproverCounts(),
                            exchangeStats.getApproverPercentage()
                    );

            builder.exchangeStats(new ComprehensiveQrStatsDto.ExchangeStats(
                    total,
                    daily,
                    approver,
                    exchangeStats.getHourlyApprovalCounts()
            ));
        }

        // 7. 스캔된 QR 코드 목록 (최대 5개)
        if (types.contains("scannedQrCodes")) {
            log.info("Fetching scanned QR codes for project: {}", projectId);
            List<Map<String, Object>> scannedQrCodesData = qrCodeMapper.selectScannedQrCodes(projectId, startDate, endDate);

            if (scannedQrCodesData != null && !scannedQrCodesData.isEmpty()) {
                List<ComprehensiveQrStatsDto.ScannedQrCodes.ScannedQrInfo> scannedQrInfoList = new ArrayList<>();

                for (Map<String, Object> data : scannedQrCodesData) {
                    // 맵에서 데이터 추출 및 null 체크
                    Number qrCodeIdNum = (Number) data.get("qr_code_id");
                    String qrName = (String) data.get("qr_name");
                    String qrType = (String) data.get("qr_type");
                    String location = (String) data.get("location");

                    // BigDecimal을 Float으로 안전하게 변환
                    Float installationLocationLat = null;
                    Float installationLocationLng = null;

                    if (data.get("installation_location_lat") instanceof Number) {
                        installationLocationLat = ((Number) data.get("installation_location_lat")).floatValue();
                    }

                    if (data.get("installation_location_lng") instanceof Number) {
                        installationLocationLng = ((Number) data.get("installation_location_lng")).floatValue();
                    }

                    Number scanCountNum = (Number) data.get("scan_count");
                    String createdAt = (String) data.get("created_at");

                    Long qrCodeId = qrCodeIdNum != null ? qrCodeIdNum.longValue() : null;
                    Long scanCount = scanCountNum != null ? scanCountNum.longValue() : 0L;

                    // 필수 데이터 확인
                    if (qrCodeId != null && qrName != null) {
                        ComprehensiveQrStatsDto.ScannedQrCodes.ScannedQrInfo qrInfo =
                                new ComprehensiveQrStatsDto.ScannedQrCodes.ScannedQrInfo(
                                        qrCodeId, qrName, qrType, location, installationLocationLat, installationLocationLng, scanCount, createdAt);
                        scannedQrInfoList.add(qrInfo);
                    }
                }

                if (!scannedQrInfoList.isEmpty()) {
                    ComprehensiveQrStatsDto.ScannedQrCodes scannedQrCodes =
                            new ComprehensiveQrStatsDto.ScannedQrCodes(scannedQrInfoList);
                    builder.scannedQrCodes(scannedQrCodes);
                }
            }
        }

        ComprehensiveQrStatsDto result = builder.build();
        log.info("Successfully retrieved comprehensive QR statistics.");
        return result;
    }

    /**
     * 사용자 활동 통계 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @param includeTypes 포함할 통계 유형 (쉼표로 구분된 문자열, 기본값: 모든 유형)
     * @param topN 상위 몇 명의 사용자 통계를 조회할지 (기본값: 5)
     * @return UserActivityStatsDto
     */
    public UserActivityStatsDto getUserActivityStats(LocalDate startDate, LocalDate endDate, Long projectId, String includeTypes, int topN) {
        log.info("Fetching user activity statistics. StartDate: {}, EndDate: {}, ProjectId: {}, IncludeTypes: {}, TopN: {}",
                startDate, endDate, projectId, includeTypes, topN);

        // 포함할 통계 유형 파싱
        Set<String> types = new HashSet<>();
        if (includeTypes != null && !includeTypes.trim().isEmpty()) {
            types.addAll(Arrays.asList(includeTypes.split(",")));
        } else {
            // 기본값: 사용자 기본 통계만 포함
            types.add("basic");
        }

        UserActivityStatsDto.UserActivityStatsDtoBuilder builder = UserActivityStatsDto.builder();

        // 1. 기본 사용자 통계
        if (types.contains("basic")) {
            Map<String, Object> basicStats = userMapper.selectUserBasicStats(projectId, startDate, endDate);
            List<Map<String, Object>> roleStats = userMapper.selectUserCountByRole(projectId, startDate, endDate);

            Map<String, Long> usersByRole = new HashMap<>();
            for (Map<String, Object> roleStat : roleStats) {
                String role = (String) roleStat.get("role");
                Number countNum = (Number) roleStat.get("count");

                if (role != null && countNum != null) {
                    Long count = countNum.longValue();
                    usersByRole.put(role, count);
                }
            }

            // null 체크 추가
            Number totalUsersNum = (Number) basicStats.get("totalUsers");
            Number activeUsersNum = (Number) basicStats.get("activeUsers");
            Number inactiveUsersNum = (Number) basicStats.get("inactiveUsers");

            Long totalUsers = (totalUsersNum != null) ? totalUsersNum.longValue() : 0L;
            Long activeUsers = (activeUsersNum != null) ? activeUsersNum.longValue() : 0L;
            Long inactiveUsers = (inactiveUsersNum != null) ? inactiveUsersNum.longValue() : 0L;

            builder.basic(new UserActivityStatsDto.BasicStats(
                    totalUsers,
                    activeUsers,
                    inactiveUsers,
                    usersByRole
            ));

            // 관리자별 가장 많이 방문한 URI 통계
            List<Map<String, Object>> adminUriStats = userMapper.selectAdminMostVisitedUri(projectId, startDate, endDate);
            Map<String, UserActivityStatsDto.AdminStat> adminStatMap = new HashMap<>();
            for (Map<String, Object> row : adminUriStats) {
                String userEmail = (String) row.get("userEmail");
                String requestUri = (String) row.get("requestUri");
                Number cntNum = (Number) row.get("count");
                Long cnt = cntNum != null ? cntNum.longValue() : 0L;
                adminStatMap.put(userEmail, new UserActivityStatsDto.AdminStat(requestUri, cnt));
            }

            builder.adminStat(adminStatMap);
        }

        UserActivityStatsDto result = builder.build();
        log.info("Successfully retrieved user activity statistics.");
        return result;
    }

    public UsageStatisticsResponseDto getUsageStatistics(UsageStatisticsRequestDto requestDto) {
        log.info("Fetching usage statistics for startDate: {}, endDate: {}, projectId: {}", 
                 requestDto.getStartDate(), requestDto.getEndDate(), requestDto.getProjectId());

        LocalDateTime startDateTime = LocalDate.parse(requestDto.getStartDate(), DATE_FORMATTER).atStartOfDay();
        LocalDateTime endDateTime = LocalDate.parse(requestDto.getEndDate(), DATE_FORMATTER).atTime(LocalTime.MAX);
        Long projectId = requestDto.getProjectId();
        int topNLimit = defaultTopN; // 설정값 또는 기본값 사용

        try {
            long totalRequests = webServiceLogMapper.selectTotalRequestsInPeriod(startDateTime, endDateTime, projectId);
            long adminRequests = webServiceLogMapper.selectAdminRequestsInPeriod(startDateTime, endDateTime, projectId);
            long generalUserRequests = webServiceLogMapper.selectGeneralUserRequestsInPeriod(startDateTime, endDateTime, projectId);
            long uniqueAdminUsers = webServiceLogMapper.selectUniqueAdminUsersInPeriod(startDateTime, endDateTime, projectId);
            long uniqueAnonymousSessions = webServiceLogMapper.selectUniqueAnonymousSessionsInPeriod(startDateTime, endDateTime, projectId);
            List<UsageStatisticsResponseDto.HourlyUsageDto> hourlyUsage = webServiceLogMapper.selectHourlyUsageInPeriod(startDateTime, endDateTime, projectId);
            List<UsageStatisticsResponseDto.TopUrlDto> topUrls = webServiceLogMapper.selectTopUrlsInPeriod(startDateTime, endDateTime, topNLimit, projectId);

            // TopUserActivity는 admin과 anonymous를 합칠 수도 있고, 별도로 DTO에 추가할 수도 있습니다.
            // 여기서는 일단 admin 사용자 활동만 가져오도록 하고, 필요시 DTO 구조 변경 또는 로직 추가를 고려합니다.
            List<UsageStatisticsResponseDto.TopUserActivityDto> topUserActivity = webServiceLogMapper.selectTopAdminUserActivityInPeriod(startDateTime, endDateTime, topNLimit, projectId);
            // 만약 익명 사용자 활동도 필요하다면:
            // List<UsageStatisticsResponseDto.TopUserActivityDto> topAnonymousActivity = webServiceLogMapper.selectTopAnonymousUserActivityInPeriod(startDateTime, endDateTime, topNLimit, projectId);
            // topUserActivity.addAll(topAnonymousActivity); // 또는 DTO에 별도 필드

            List<UsageStatisticsResponseDto.TopRefererDto> topReferers = webServiceLogMapper.selectTopReferersInPeriod(startDateTime, endDateTime, topNLimit, projectId);
            List<UsageStatisticsResponseDto.TopUserAgentDto> topUserAgents = webServiceLogMapper.selectTopUserAgentsInPeriod(startDateTime, endDateTime, topNLimit, projectId);

            return UsageStatisticsResponseDto.builder()
                    .periodStartDate(requestDto.getStartDate())
                    .periodEndDate(requestDto.getEndDate())
                    .totalRequests(totalRequests)
                    .adminRequests(adminRequests)
                    .generalUserRequests(generalUserRequests)
                    .uniqueAdminUsers(uniqueAdminUsers)
                    .uniqueAnonymousSessions(uniqueAnonymousSessions)
                    .hourlyUsage(hourlyUsage)
                    .topUrls(topUrls)
                    .topUserActivity(topUserActivity)
                    .topReferers(topReferers)
                    .topUserAgents(topUserAgents)
                    .build();

        } catch (DataAccessException e) {
            log.error("Database error while fetching usage statistics: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_QUERY_FAILED);
        } catch (Exception e) {
            log.error("Unexpected error while fetching usage statistics: {}", e.getMessage(), e);
            throw new CustomStatisticsException(ErrorCode.STATISTICS_UNEXPECTED_ERROR);
        }
    }
}
