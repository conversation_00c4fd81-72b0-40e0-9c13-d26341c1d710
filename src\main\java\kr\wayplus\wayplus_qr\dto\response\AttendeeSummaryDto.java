package kr.wayplus.wayplus_qr.dto.response;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendeeSummaryDto {
    private Long attendeeId;
    private Long formId; // 신청서 양식 ID 추가
    private String attendeeName;
    private String teamName;
    private String attendeeContact;
    private String attendeeEmail;
    private LocalDateTime registrationDate;
    private String attendedYn;
    private String attendedConfirmYn;
    private String confirmationCode;

    // 이벤트 정보
    private Long eventId;
    private String eventName;
    private LocalDateTime eventStartDate;
    private LocalDateTime eventEndDate;

    private Map<String, Object> submissionData; // 최종적으로 사용할 Map

    @JsonIgnore // DB에서 읽어온 JSON 문자열 임시 저장용 (직렬화 시 제외)
    private String submissionDataJson;

    // Jackson ObjectMapper를 주입받거나 static으로 사용하여 파싱 로직 추가 필요
    // 예시: Setter를 이용한 파싱
    public void setSubmissionDataJson(String submissionDataJson) {
        this.submissionDataJson = submissionDataJson;
        if (submissionDataJson != null && !submissionDataJson.isEmpty()) {
            try {
                // ObjectMapper 인스턴스 생성 (실제로는 Bean 주입 권장)
                ObjectMapper objectMapper = new ObjectMapper();
                this.submissionData = objectMapper.readValue(submissionDataJson, new TypeReference<Map<String, Object>>() {});
            } catch (Exception e) {
                // JSON 파싱 오류 처리 (로그 남기기 등)
                // 실제 운영 코드에서는 로깅 프레임워크(e.g., SLF4J) 사용 권장
                System.err.println("Failed to parse submissionDataJson for attendeeId " + attendeeId + ": " + e.getMessage());
                this.submissionData = Collections.emptyMap(); // 오류 시 빈 맵 할당
            }
        } else {
            this.submissionData = Collections.emptyMap(); // JSON 문자열 없으면 빈 맵 할당
        }
    }
}
