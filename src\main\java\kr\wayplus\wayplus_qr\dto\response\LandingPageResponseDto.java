package kr.wayplus.wayplus_qr.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import kr.wayplus.wayplus_qr.entity.LandingPage;
import kr.wayplus.wayplus_qr.entity.LandingPageStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.Map;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LandingPageResponseDto {
    private Long landingPageId;
    private Long projectId;
    private String projectName;
    private String pageTitle;
    private String description;
    private Map<String, Object> contentJson; 
    private LandingPageStatus status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime validFromDate; 

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime validToDate;   

    private String createUserEmail;
    private String updateUserEmail;
    private LocalDateTime createDate;
    private LocalDateTime lastUpdateDate;

    private static final ObjectMapper objectMapper = new ObjectMapper();

    public static LandingPageResponseDto fromEntity(LandingPage landingPage) {
        if (landingPage == null) {
            return null;
        }

        Map<String, Object> contentMap = null;
        if (landingPage.getContentJson() != null && !landingPage.getContentJson().isEmpty()) {
            try {
                // JSON 문자열을 Map<String, Object>으로 파싱 (TypeReference 사용)
                contentMap = objectMapper.readValue(landingPage.getContentJson(), new TypeReference<Map<String, Object>>() {});
            } catch (JsonProcessingException e) {
                System.err.println("Error parsing contentJson for landingPageId " + landingPage.getLandingPageId() + ": " + e.getMessage());
            }
        }

        return LandingPageResponseDto.builder()
                .landingPageId(landingPage.getLandingPageId())
                .projectId(landingPage.getProjectId())
                .projectName(landingPage.getProjectName())
                .pageTitle(landingPage.getPageTitle())
                .description(landingPage.getDescription())
                .contentJson(contentMap)
                .status(landingPage.getStatus())
                .validFromDate(landingPage.getValidFromDate())
                .validToDate(landingPage.getValidToDate())
                .createUserEmail(landingPage.getCreateUserEmail())
                .updateUserEmail(landingPage.getUpdateUserEmail())
                .createDate(landingPage.getCreateDate())
                .lastUpdateDate(landingPage.getLastUpdateDate())
                .build();
    }
}
