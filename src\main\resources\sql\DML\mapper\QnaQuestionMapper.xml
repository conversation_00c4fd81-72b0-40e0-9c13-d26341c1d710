<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.QnaQuestionMapper">

    <!-- 질문 DTO를 위한 결과 맵 (답변 포함) -->
    <resultMap id="qnaQuestionWithAnswerMap" type="kr.wayplus.wayplus_qr.dto.response.QnaQuestionResponseDto">
        <id property="qnaQuestionId" column="qna_question_id"/>
        <result property="projectId" column="project_id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="createUserEmail" column="create_user_email"/>
        <result property="createDate" column="create_date"/>
        <result property="updateUserEmail" column="update_user_email"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteUserEmail" column="delete_user_email"/>
        <result property="deleteDate" column="delete_date"/>
        <result property="useYn" column="use_yn"/>
        <result property="deleteYn" column="delete_yn"/>
        <result property="viewCount" column="view_count"/>
        
        <!-- 답변 정보를 포함 -->
        <association property="answer" javaType="kr.wayplus.wayplus_qr.dto.response.QnaAnswerResponseDto">
            <id property="qnaAnswerId" column="answer_id"/>
            <result property="qnaQuestionId" column="qna_question_id"/>
            <result property="answerContent" column="answer_content"/>
            <result property="createUserEmail" column="answer_create_user_email"/>
            <result property="createDate" column="answer_create_date"/>
            <result property="updateUserEmail" column="answer_update_user_email"/>
            <result property="lastUpdateDate" column="answer_last_update_date"/>
            <result property="deleteUserEmail" column="answer_delete_user_email"/>
            <result property="deleteDate" column="answer_delete_date"/>
            <result property="useYn" column="answer_use_yn"/>
            <result property="deleteYn" column="answer_delete_yn"/>
        </association>
    </resultMap>

    <!-- 질문 등록 -->
    <insert id="insertQuestion">
        INSERT INTO qna_questions (
            project_id,
            title,
            content,
            create_user_email,
            use_yn,
            delete_yn
        ) VALUES (
            #{projectId},
            #{title},
            #{content},
            #{createUserEmail},
            'Y',
            'N'
        )
    </insert>
    
    <!-- 최근 등록된 ID 조회 -->
    <select id="selectLastInsertId" resultType="java.lang.Long">
        SELECT LAST_INSERT_ID()
    </select>
    
    <!-- 특정 ID의 질문 조회 (답변 포함) -->
    <select id="selectQuestionById" resultMap="qnaQuestionWithAnswerMap">
        SELECT 
            q.*,
            a.qna_answer_id AS answer_id,
            a.qna_question_id,
            a.answer_content,
            a.create_user_email AS answer_create_user_email,
            a.create_date AS answer_create_date,
            a.update_user_email AS answer_update_user_email,
            a.last_update_date AS answer_last_update_date,
            a.delete_user_email AS answer_delete_user_email,
            a.delete_date AS answer_delete_date,
            a.use_yn AS answer_use_yn,
            a.delete_yn AS answer_delete_yn
        FROM 
            qna_questions q
        LEFT JOIN 
            qna_answers a ON q.qna_question_id = a.qna_question_id AND a.delete_yn = 'N'
        WHERE 
            q.qna_question_id = #{qnaQuestionId}
            AND q.delete_yn = 'N'
    </select>
    
    <!-- 질문 목록 조회 (페이징, 검색 기능, 답변 포함) -->
    <select id="selectQuestionsList" resultMap="qnaQuestionWithAnswerMap">
        SELECT 
            q.*,
            a.qna_answer_id AS answer_id,
            a.qna_question_id,
            a.answer_content,
            a.create_user_email AS answer_create_user_email,
            a.create_date AS answer_create_date,
            a.update_user_email AS answer_update_user_email,
            a.last_update_date AS answer_last_update_date,
            a.delete_user_email AS answer_delete_user_email,
            a.delete_date AS answer_delete_date,
            a.use_yn AS answer_use_yn,
            a.delete_yn AS answer_delete_yn
        FROM 
            qna_questions q
        LEFT JOIN 
            qna_answers a ON q.qna_question_id = a.qna_question_id AND a.delete_yn = 'N'
        WHERE 
            q.delete_yn = 'N'
            <if test="answerType != null and answerType != ''">
                <choose>
                    <when test="answerType == 'COMPLETED'">
                        AND a.qna_answer_id IS NOT NULL
                    </when>
                    <when test="answerType == 'ALL'">
                        <!-- 모든 데이터 반환 (추가 조건 없음) -->
                    </when>
                    <otherwise>
                        AND a.qna_answer_id IS NULL
                    </otherwise>
                </choose>
            </if>
            <if test="searchKeyword != null and searchKeyword != ''">
                <choose>
                    <when test="searchType == 'title'">
                        AND q.title LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="searchType == 'content'">
                        AND q.content LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="searchType == 'createdBy'">
                        AND q.create_user_email LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <otherwise>
                        AND (
                            q.title LIKE CONCAT('%', #{searchKeyword}, '%') OR
                            q.content LIKE CONCAT('%', #{searchKeyword}, '%') OR
                            q.create_user_email LIKE CONCAT('%', #{searchKeyword}, '%')
                        )
                    </otherwise>
                </choose>
            </if>
        ORDER BY q.create_date DESC
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 질문 총 개수 조회 (페이징용) -->
    <select id="selectQuestionsCount" resultType="int">
        SELECT COUNT(*)
        FROM qna_questions q
        LEFT JOIN 
            qna_answers a ON q.qna_question_id = a.qna_question_id AND a.delete_yn = 'N'
        WHERE 
            q.delete_yn = 'N'
            <if test="answerType != null and answerType != ''">
                <choose>
                    <when test="answerType == 'COMPLETED'">
                        AND a.qna_answer_id IS NOT NULL
                    </when>
                    <when test="answerType == 'ALL'">
                        <!-- 모든 데이터 반환 (추가 조건 없음) -->
                    </when>
                    <otherwise>
                        AND a.qna_answer_id IS NULL
                    </otherwise>
                </choose>
            </if>
            <if test="searchKeyword != null and searchKeyword != ''">
                <choose>
                    <when test="searchType == 'title'">
                        AND q.title LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="searchType == 'content'">
                        AND q.content LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="searchType == 'createdBy'">
                        AND q.create_user_email LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <otherwise>
                        AND (
                            q.title LIKE CONCAT('%', #{searchKeyword}, '%') OR
                            q.content LIKE CONCAT('%', #{searchKeyword}, '%') OR
                            q.create_user_email LIKE CONCAT('%', #{searchKeyword}, '%')
                        )
                    </otherwise>
                </choose>
            </if>
    </select>
    
    <!-- 질문 수정 -->
    <update id="updateQuestion">
        UPDATE qna_questions
        SET 
            <if test="title != null and title != ''">
                title = #{title},
            </if>
            <if test="content != null and content != ''">
                content = #{content},
            </if>
            update_user_email = #{updateUserEmail},
            last_update_date = CURRENT_TIMESTAMP
        WHERE 
            qna_question_id = #{qnaQuestionId}
            AND delete_yn = 'N'
    </update>
    
    <!-- 조회수 증가 -->
    <update id="updateViewCount">
        UPDATE qna_questions
        SET 
            view_count = view_count + 1
        WHERE 
            qna_question_id = #{qnaQuestionId}
            AND delete_yn = 'N'
    </update>
    
    <!-- 질문 삭제 (논리적 삭제) -->
    <update id="deleteQuestion">
        UPDATE qna_questions
        SET 
            delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = CURRENT_TIMESTAMP
        WHERE 
            qna_question_id = #{qnaQuestionId}
            AND delete_yn = 'N'
    </update>
    
</mapper>
