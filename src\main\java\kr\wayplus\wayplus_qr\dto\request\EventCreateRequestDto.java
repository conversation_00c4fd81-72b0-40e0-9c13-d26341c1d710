package kr.wayplus.wayplus_qr.dto.request;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import com.fasterxml.jackson.annotation.JsonFormat;

import kr.wayplus.wayplus_qr.entity.EventStatus;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventCreateRequestDto {
    private Long projectId;
    private Long teamId;
    private String eventName;
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime startDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime endDate;

    private String location;
    private Integer participantLimit;
    private Long preRegistrationFormId;
    private Long linkedQrCodeId;
    private EventStatus status;
    private MultipartFile eventImageFile;

    // 이벤트에 포함될 혜택 목록(JSON 문자열)
    private String benefitsJson;
}
