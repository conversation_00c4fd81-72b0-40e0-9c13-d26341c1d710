package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.request.ChangePasswordRequestDto;
import kr.wayplus.wayplus_qr.dto.request.PasswordChangeRequestDto;
import kr.wayplus.wayplus_qr.dto.request.UserCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.UserUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.UserListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.UserResponseDto;
import kr.wayplus.wayplus_qr.dto.response.AvailableProjectAdminDto;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.entity.WebServiceLog;
import kr.wayplus.wayplus_qr.entity.enums.UserRole;
import kr.wayplus.wayplus_qr.entity.enums.UserStatus;
import kr.wayplus.wayplus_qr.exception.InvalidCurrentPasswordException;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Value;

/**
 * 사용자 관련 비즈니스 로직을 처리하는 서비스 클래스
 */
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(readOnly = true)
public class UserService {

    @Value("${wayplus.initialPassword}")
    private String wayplusInitialPassword;

    private final MailService mailService;
    
    private final UserMapper userMapper;
    private final PasswordEncoder passwordEncoder;

    /**
     * 이메일로 사용자 정보를 조회하고, 없으면 예외를 발생시킵니다.
     *
     * @param userEmail 사용자 이메일
     * @return User 엔티티
     * @throws UsernameNotFoundException 사용자를 찾을 수 없을 때
     */
    public User getUserByUserEmail(String userEmail) {
        return userMapper.selectUserByEmail(userEmail)
                .orElseThrow(() -> new UsernameNotFoundException("사용자를 찾을 수 없습니다: " + userEmail));
    }

    /**
     * 이메일로 사용자 정보를 조회하고 DTO로 변환하여 반환합니다.
     *
     * @param userEmail 조회할 사용자의 이메일
     * @return 사용자 정보 DTO
     * @throws UsernameNotFoundException 해당 이메일의 사용자를 찾을 수 없을 때 발생
     */
    public UserResponseDto selectUserByEmail(String userEmail) {
        User user = userMapper.selectUserByEmail(userEmail)
                .orElseThrow(() -> new UsernameNotFoundException("사용자를 찾을 수 없습니다: " + userEmail));
        return UserResponseDto.fromEntity(user);
    }

    /**
     * 사용자 목록을 조회합니다. (권한에 따라 필터링)
     * - SUPER_ADMIN: 모든 활성 사용자
     * - PROJECT_ADMIN: 자신 및 자신이 속한 프로젝트의 모든 활성 사용자
     *
     * @param projectId 프로젝트 ID (null일 경우 전체 사용자 목록 조회)
     * @param pageable  페이징 정보
     * @return 사용자 목록 DTO 페이지
     */
    public Page<UserListResponseDto> getUserList(Long projectId, String searchType,String searchKeyword, Boolean onlyProjectSearch, Pageable pageable) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new AccessDeniedException("인증 정보가 없어 사용자 목록을 조회할 수 없습니다.");
        }
        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "userEmail": searchColumn = "user_email"; break;
                case "name": searchColumn = "name"; break;
                case "roleId": searchColumn = "role_id"; break;
                case "status": searchColumn = "status"; break;
                case "createDate": searchColumn = "create_date"; break;
            }
        }
        
        String currentUserEmail = authentication.getName();
        User currentUser = getUserByUserEmail(currentUserEmail);

        List<UserListResponseDto> userList;
        long totalCount;

        if (projectId != null) {
            // projectId가 제공된 경우: 해당 프로젝트 사용자 목록 조회 (페이징 적용)
            log.info("User {} requested user list for project ID: {} with pagination: {}", currentUserEmail, projectId, pageable);
            // 권한 확인: SUPER_ADMIN 또는 해당 프로젝트의 PROJECT_ADMIN만 조회 가능
            // UserRole enum과 roleId 비교로 수정
            if (!UserRole.SUPER_ADMIN.name().equals(currentUser.getRoleId())) {
                if (!UserRole.PROJECT_ADMIN.name().equals(currentUser.getRoleId()) || !isUserAssignedToProject(currentUserEmail, projectId)) {
                    log.warn("Access denied for user {} to view user list for project ID: {}", currentUserEmail, projectId);
                    throw new AccessDeniedException("해당 프로젝트의 사용자 목록을 조회할 권한이 없습니다.");
                }
            }
            // Mapper 호출 (페이징용 메서드, 아직 없음)
            userList = userMapper.selectProjectUserListWithPaging(projectId, searchColumn, searchKeyword, pageable);
            totalCount = userMapper.countProjectUsers(projectId, searchColumn, searchKeyword);
        } else {
            // projectId가 없는 경우: 전체 사용자 목록 조회 (페이징 적용, SUPER_ADMIN만 가능)
            log.info("User {} requested all user list with pagination: {}", currentUserEmail, pageable);
            // UserRole enum과 roleId 비교로 수정
            if (!UserRole.SUPER_ADMIN.name().equals(currentUser.getRoleId())) {
                log.warn("Access denied for user {} to view all user list. SUPER_ADMIN role required.", currentUserEmail);
                throw new AccessDeniedException("전체 사용자 목록을 조회할 권한이 없습니다. SUPER_ADMIN 역할이 필요합니다.");
            }
            // Mapper 호출 (페이징용 메서드, 아직 없음)
            Pageable onlyProjectSearchPageable = null;
            if (onlyProjectSearch != null && onlyProjectSearch) {
                onlyProjectSearchPageable = null;
            } else {
                onlyProjectSearchPageable = pageable;
            }
            userList = userMapper.selectAllUsersWithPaging(searchColumn, searchKeyword, onlyProjectSearchPageable);
            totalCount = userMapper.countAllUsers(searchColumn, searchKeyword);
        }

        // Page 객체 생성 및 반환
        return new PageImpl<>(userList, pageable, totalCount);
    }

    /**
     * 새로운 사용자를 생성합니다.
     * PROJECT_ADMIN이 생성 시 자신의 프로젝트에 자동으로 멤버 추가합니다.
     *
     * @param requestDto 사용자 생성 요청 정보
     * @return 생성된 사용자 정보 DTO
     * @throws DuplicateKeyException 이미 존재하는 이메일일 경우
     * @throws AccessDeniedException 권한이 없을 경우 (하위 관리자가 상위 관리자 생성 시도 등)
     */
    @Transactional //사용자 생성과 프로젝트 매핑을 하나의 트랜잭션으로 묶음
    public UserResponseDto createUser(UserCreateRequestDto requestDto) {
        log.info("Attempting to create user: {}", requestDto.getUserEmail());

        // 0. 요청자 정보 가져오기
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new AccessDeniedException("인증 정보가 없어 사용자 생성을 진행할 수 없습니다.");
        }
        String creatorEmail = authentication.getName();
        User creator = getUserByUserEmail(creatorEmail);

        // 요청된 역할 검증 (SUPER_ADMIN은 SUPER_ADMIN만 생성 가능)
        if (UserRole.SUPER_ADMIN.name().equals(requestDto.getRoleId()) && !UserRole.SUPER_ADMIN.name().equals(creator.getRoleId())) {
             log.warn("Unauthorized attempt to create SUPER_ADMIN by user: {}", creatorEmail);
             throw new AccessDeniedException("SUPER_ADMIN 계정은 SUPER_ADMIN만 생성할 수 있습니다.");
        }
        // PROJECT_ADMIN은 SUPER_ADMIN 또는 PROJECT_ADMIN만 생성 가능
        if (UserRole.PROJECT_ADMIN.name().equals(requestDto.getRoleId()) &&
                !(UserRole.SUPER_ADMIN.name().equals(creator.getRoleId()) || UserRole.PROJECT_ADMIN.name().equals(creator.getRoleId()))) {
            log.warn("Unauthorized attempt to create PROJECT_ADMIN by user: {} with role: {}", creatorEmail, creator.getRoleId());
            throw new AccessDeniedException("PROJECT_ADMIN 계정은 SUPER_ADMIN 또는 PROJECT_ADMIN만 생성할 수 있습니다.");
        }

        // 1. 이메일 중복 확인
        if (userMapper.selectUserByEmail(requestDto.getUserEmail()).isPresent()) {
            log.warn("User creation failed - Duplicate email: {}", requestDto.getUserEmail());
            throw new DuplicateKeyException("이미 사용 중인 이메일입니다: " + requestDto.getUserEmail());
        }

        // 2. 비밀번호 암호화
        String initialPassword = passwordEncoder.encode(wayplusInitialPassword);

        // 3. 사용자 엔티티 생성
        User user = User.builder()
                .userEmail(requestDto.getUserEmail())
                .name(requestDto.getName())
                .password(initialPassword)
                .roleId(requestDto.getRoleId())
                .description(requestDto.getDescription())
                // status 값이 있으면 사용, 없으면 기본값 ACTIVE
                .status(StringUtils.hasText(requestDto.getStatus()) ? requestDto.getStatus().toUpperCase() : UserStatus.ACTIVE.name())
                .initialPasswordYn("Y") // 초기 비밀번호 변경 필요 상태
                .createUserEmail(creatorEmail) 
                .useYn("Y")
                .deleteYn("N")
                .loginFailCount(0)
                .build();

        // 4. 사용자 정보 저장 (users 테이블)
        int insertedRows = userMapper.insertUser(user);
        if (insertedRows == 0) {
            log.error("Failed to insert user: {}", requestDto.getUserEmail());
            throw new RuntimeException("사용자 생성 중 오류가 발생했습니다.");
        }
        log.info("Successfully inserted user: {}", requestDto.getUserEmail());

        // 5. 프로젝트 할당 (DTO에 명시된 프로젝트 ID 기준)
        if (requestDto.getProjectId() != null) {
            Long targetProjectId = requestDto.getProjectId();
            boolean creatorIsSuperAdmin = UserRole.SUPER_ADMIN.name().equals(creator.getRoleId());

            // SUPER_ADMIN이 아닌 경우, 생성자가 대상 프로젝트의 멤버인지 확인
            if (!creatorIsSuperAdmin) {
                int membershipCount = userMapper.countUserProjectMembership(creatorEmail, targetProjectId);
                if (membershipCount == 0) {
                    log.warn("User creation failed: Creator {} is not a member of the target project {}.", creatorEmail, targetProjectId);
                    // 예외를 발생시켜 롤백을 유도. Transactional 어노테이션 필요.
                    throw new AccessDeniedException("사용자를 생성하려는 프로젝트에 대한 권한이 없습니다.");
                }
                log.info("Creator {} is a member of project {}. Proceeding with assignment.", creatorEmail, targetProjectId);
            } else {
                log.info("Creator {} is SUPER_ADMIN. Skipping membership check for project {}.", creatorEmail, targetProjectId);
            }

            // 사용자-프로젝트 매핑 정보 삽입
            Map<String, Object> mappingParams = new HashMap<>();
            mappingParams.put("userEmail", requestDto.getUserEmail());
            mappingParams.put("projectId", targetProjectId);
            mappingParams.put("userRoleId", requestDto.getRoleId());
            mappingParams.put("createUserEmail", creatorEmail);
            int mappingInserted = userMapper.insertUserProjectMapping(mappingParams);

            if (mappingInserted == 0) {
                log.error("Failed to insert user-project mapping for user {} and project {}.", requestDto.getUserEmail(), targetProjectId);
                // 예외를 발생시켜 롤백을 유도. Transactional 어노테이션 필요.
                throw new RuntimeException("사용자-프로젝트 매핑 정보 저장 중 오류가 발생했습니다.");
            }
            log.info("Successfully assigned user {} to project {} with role {}", requestDto.getUserEmail(), targetProjectId, requestDto.getRoleId());
        }
    

        try {
            log.info("사용자 [{}] 계정 생성 안내 메일 발송을 시도합니다.", user.getUserEmail());

            mailService.sendMailFromSet("account", 1, user.getUserEmail(), wayplusInitialPassword);

            log.info("사용자 [{}]에게 계정 생성 안내 메일을 성공적으로 발송했습니다.", user.getUserEmail());
        } catch (Exception e) { // 기타 모든 예외 (메일 발송 로직에서 발생할 수 있는)
            log.error("사용자 [{}]에게 계정 생성 안내 메일 발송 중 예상치 못한 오류 발생: {}", user.getUserEmail(), e.getMessage(), e);
            throw new RuntimeException("안내 메일 발송 중 예상치 못한 오류가 발생하여 사용자 생성에 관한 모든 사항이 취소됩니다.", e);
        }
        
        // 6. 생성된 사용자 정보 다시 조회 후 DTO 변환하여 반환
        User createdUser = userMapper.selectUserByEmail(requestDto.getUserEmail())
                .orElseThrow(() -> new RuntimeException("생성된 사용자 정보를 가져오는 데 실패했습니다: " + requestDto.getUserEmail()));

        log.info("User {} created successfully by {}.", requestDto.getUserEmail(), creatorEmail);
        return UserResponseDto.fromEntity(createdUser);
    }

    /**
     * 사용자 정보를 수정합니다.
     *
     * @param userEmail 수정할 사용자의 이메일
     * @param requestDto 수정할 사용자 정보
     * @return 수정된 사용자 정보 DTO
     */
    @Transactional
    public UserResponseDto modifyUser(String userEmail, UserUpdateRequestDto requestDto) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String modifierEmail = authentication.getName();

        // 1. 대상 사용자 조회
        User targetUser = userMapper.selectUserByEmail(userEmail)
                .orElseThrow(() -> new UsernameNotFoundException("수정할 사용자를 찾을 수 없습니다. Email: " + userEmail));

        // 2. 수정 권한 검증
        String modifierRole = userMapper.selectUserRoleByEmail(modifierEmail)
                .orElseThrow(() -> new UsernameNotFoundException("수정 권한 확인 중 오류 발생: 사용자 정보를 찾을 수 없습니다."));

        if (!modifierRole.equals(UserRole.SUPER_ADMIN.name()) && !modifierRole.equals(UserRole.PROJECT_ADMIN.name())) {
            throw new AccessDeniedException("사용자 정보를 수정할 권한이 없습니다.");
        }

        // 3. 사용자 정보 업데이트 (요청 DTO에 값이 있는 경우에만 업데이트)
        boolean updated = false;
        if (requestDto.getName() != null && !requestDto.getName().equals(targetUser.getName())) {
            targetUser.setName(requestDto.getName());
            updated = true;
        }
        if (requestDto.getRoleId() != null && !requestDto.getRoleId().equals(targetUser.getRoleId())) {
            validateRoleChangePermission(modifierRole, targetUser.getRoleId(), requestDto.getRoleId());
            targetUser.setRoleId(requestDto.getRoleId());
            updated = true;
        }
        if (requestDto.getDescription() != null && !requestDto.getDescription().equals(targetUser.getDescription())) {
            targetUser.setDescription(requestDto.getDescription());
            updated = true;
        }
        if (requestDto.getStatus() != null && !requestDto.getStatus().equals(targetUser.getStatus())) {
            targetUser.setStatus(requestDto.getStatus());
            updated = true;
        }

        // 4. 변경 사항이 있을 경우 DB 업데이트
        if (updated) {
            targetUser.setUpdateUserEmail(modifierEmail);
            int updateCount = userMapper.updateUser(targetUser);
            if (updateCount == 0) {
                throw new UsernameNotFoundException("사용자 정보 업데이트 중 오류가 발생했습니다. Email: " + userEmail);
            }
            log.info("User updated successfully. Email: {}, Modifier: {}", userEmail, modifierEmail);
        } else {
            log.info("No changes detected for user Email: {}. Update skipped.", userEmail);
        }

        // 5. 수정된 사용자 정보 반환
        return UserResponseDto.fromEntity(targetUser);
    }

    /**
     * 사용자 상세 정보를 이메일로 조회합니다.
     *
     * @param userEmail 조회할 사용자의 이메일
     * @return 프로필 정보 DTO
     * @throws GlobalException 사용자를 찾을 수 없을 때
     */
    @Transactional(readOnly = true)
    public UserResponseDto getUserProfileByEmail(String userEmail) {
        log.info("Fetching profile for user: {}", userEmail);

        User user = userMapper.selectUserByEmail(userEmail)
                .orElseThrow(() -> new UsernameNotFoundException("사용자를 찾을 수 없습니다: " + userEmail));

        UserResponseDto profileDto = UserResponseDto.fromEntity(user);

        log.info("Successfully fetched profile for user: {}", userEmail);
        return profileDto;
    }

    private void validateRoleChangePermission(String modifierRole, String currentRole, String requestedRole) {
        if (!modifierRole.equals(UserRole.SUPER_ADMIN.name()) && !modifierRole.equals(UserRole.PROJECT_ADMIN.name())) {
            throw new AccessDeniedException("역할을 변경할 권한이 없습니다.");
        }
        log.debug("Role change permission validated for modifier: {}, current: {}, requested: {}", modifierRole, currentRole, requestedRole);
    }

    /**
     * 사용자를 논리적으로 삭제합니다.
     *
     * @param userEmail 삭제할 사용자의 이메일
     */
    @Transactional // 데이터 일관성을 위해 트랜잭션 처리
    public void deleteUser(String userEmail) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        String deleterEmail = authentication.getName();

        // 0. 자기 자신 삭제 불가
        if (deleterEmail.equals(userEmail)) {
            throw new IllegalArgumentException("자기 자신을 삭제할 수 없습니다.");
        }

        // 1. 삭제 대상 사용자 존재 및 상태 확인
        User targetUser = userMapper.selectUserByEmail(userEmail)
                .orElseThrow(() -> new UsernameNotFoundException("삭제할 사용자를 찾을 수 없습니다. Email: " + userEmail));

        if ("Y".equals(targetUser.getDeleteYn())) {
            log.info("User already deleted logically. Email: {}", userEmail);
            return; // 이미 삭제된 경우 중복 실행 방지
        }

        // 2. 삭제 권한 검증
        String deleterRole = userMapper.selectUserRoleByEmail(deleterEmail)
                .orElseThrow(() -> new UsernameNotFoundException("삭제 권한 확인 중 오류 발생: 요청자 정보를 찾을 수 없습니다."));

        // SUPER_ADMIN은 모든 사용자 삭제 가능
        if (!deleterRole.equals(UserRole.SUPER_ADMIN.name())) {
            // PROJECT_ADMIN만 삭제 가능하도록 제한 (정책에 따라 변경 가능)
            if (!deleterRole.equals(UserRole.PROJECT_ADMIN.name())) {
                throw new AccessDeniedException("사용자를 삭제할 권한이 없습니다.");
            }

            // PROJECT_ADMIN은 다른 PROJECT_ADMIN을 삭제할 수 없음
            String targetUserRole = userMapper.selectUserRoleByEmail(userEmail)
                    .orElseThrow(() -> new UsernameNotFoundException("삭제 대상 사용자의 역할을 찾을 수 없습니다. Email: " + userEmail));
            if (targetUserRole.equals(UserRole.PROJECT_ADMIN.name())) {
                log.warn("Access Denied: PROJECT_ADMIN {} tried to delete another PROJECT_ADMIN {}.", deleterEmail, userEmail);
                throw new AccessDeniedException("프로젝트 관리자는 다른 프로젝트 관리자를 삭제할 수 없습니다.");
            }

            // PROJECT_ADMIN인 경우, 같은 프로젝트 소속인지 확인
            List<Long> deleterProjectIds = userMapper.selectProjectIdsByUserEmail(deleterEmail);
            List<Long> targetUserProjectIds = userMapper.selectProjectIdsByUserEmail(userEmail);

            // 두 리스트에서 공통된 프로젝트 ID가 하나라도 있는지 확인
            boolean hasCommonProject = deleterProjectIds.stream().anyMatch(targetUserProjectIds::contains);

            if (!hasCommonProject) {
                log.warn("Access Denied: PROJECT_ADMIN {} tried to delete user {} from a different project.", deleterEmail, userEmail);
                throw new AccessDeniedException("다른 프로젝트에 속한 사용자는 삭제할 수 없습니다.");
            }
            log.info("PROJECT_ADMIN {} is authorized to delete user {} (common project found).", deleterEmail, userEmail);
        }

        // 3. 논리적 삭제 수행
        int deleteCount = userMapper.deleteUserLogically(userEmail, deleterEmail);

        if (deleteCount == 0) {
            throw new UsernameNotFoundException("사용자 삭제 처리 중 오류가 발생했습니다. Email: " + userEmail);
        }

        log.info("User logically deleted successfully. Email: {}, Deleter: {}", userEmail, deleterEmail);
    }

    /**
     * 사용자 비밀번호 변경
     *
     * @param userEmail 비밀번호를 변경할 사용자의 이메일
     * @param passwordChangeRequestDto 새 비밀번호 정보 DTO
     */
    @Transactional
    public void changePassword(String userEmail, PasswordChangeRequestDto passwordChangeRequestDto) {
        // 1. 사용자 정보 조회 (삭제되지 않은 사용자)
        userMapper.selectUserByEmailForUpdate(userEmail)
                .orElseThrow(() -> new UsernameNotFoundException("해당 사용자를 찾을 수 없습니다. Email: " + userEmail));

        // 2. 새 비밀번호 암호화
        String encodedNewPassword = passwordEncoder.encode(passwordChangeRequestDto.getNewPassword());

        // 3. 비밀번호 업데이트 (Mapper 호출)
        int updatedRows = userMapper.updateUserPassword(userEmail, encodedNewPassword);

        if (updatedRows == 0) {
            // 업데이트가 실제로 이루어지지 않은 경우 (예: userIdx가 잘못되었거나 동시에 삭제된 경우)
            log.warn("Password update attempt failed for userEmail: {}. No rows affected.", userEmail);
        } else {
            log.info("Password updated successfully for userEmail: {}. Rows affected: {}", userEmail, updatedRows);
        }
    }

    /**
     * 최초 로그인 사용자의 비밀번호를 변경합니다.
     *
     * @param requestDto 비밀번호 변경 요청 DTO
     */
    @Transactional
    public void changeInitialPassword(ChangePasswordRequestDto requestDto) {
        log.info("Attempting to change initial password for user: {}", requestDto.getUserEmail());

        // 1. 사용자 정보 조회 및 Optional 처리
        User user = userMapper.selectUserByEmail(requestDto.getUserEmail())
                .orElseThrow(() -> new UsernameNotFoundException("사용자를 찾을 수 없습니다: " + requestDto.getUserEmail()));

        // 2. 현재 비밀번호 확인
        if (!passwordEncoder.matches(requestDto.getCurrentPassword(), user.getPassword())) {
            log.warn("Initial password change failed - Invalid current password for user: {}", requestDto.getUserEmail());
            throw new InvalidCurrentPasswordException("현재 비밀번호가 일치하지 않습니다."); // 예외 변경
        }

        if (!"Y".equals(user.getInitialPasswordYn())) {
            throw new IllegalStateException("최초 비밀번호 변경 대상이 아닙니다.");
        }

        // 4. 새 비밀번호 유효성 검사 (필요 시 추가적인 규칙 적용)
        if (passwordEncoder.matches(requestDto.getNewPassword(), user.getPassword())) {
            throw new IllegalArgumentException("새 비밀번호는 현재 비밀번호와 달라야 합니다.");
        }

        // 5. 새 비밀번호 암호화 및 업데이트 (Mapper 인터페이스에 맞게 파라미터 전달)
        String encodedNewPassword = passwordEncoder.encode(requestDto.getNewPassword());
        int updatedRows = userMapper.updateUserPassword(user.getUserEmail(), encodedNewPassword);
        if (updatedRows == 0) {
            // 업데이트 실패 시 예외 처리
            throw new RuntimeException("비밀번호 업데이트 중 오류가 발생했습니다.");
        }

        log.info("사용자 [{}]의 최초 비밀번호가 성공적으로 변경되었습니다.", requestDto.getUserEmail());
    }

    // 할당 가능한 프로젝트 관리자 목록 조회
    public List<UserResponseDto> getAvailableProjectAdmins() {
        return userMapper.selectAvailableProjectAdmins();
    }

    // 특정 프로젝트 수정 시 할당 가능한 프로젝트 관리자 목록 조회
    public List<AvailableProjectAdminDto> getAvailableProjectAdminsForProject(Long projectId) {
        log.info("Fetching available project admins for project ID: {}", projectId);

        List<AvailableProjectAdminDto> admins = userMapper.selectAvailableProjectAdminsForProject(projectId);
        log.info("Found {} available project admins for project ID: {}", admins.size(), projectId);
        return admins;
    }

    // 사용자가 특정 프로젝트에 할당되었는지 확인하는 헬퍼 메서드 (예시)
    private boolean isUserAssignedToProject(String userEmail, Long projectId) {
        // 실제 구현: UserMapper 등을 통해 사용자의 프로젝트 할당 정보 확인
        // 여기서는 예시로 항상 true를 반환합니다. 실제 로직으로 교체해야 합니다.
        Integer count = userMapper.checkUserProjectAssignment(userEmail, projectId);
        return count != null && count > 0;
         // return true; // 실제 로직 구현 필요
    }

    /**
     * 사용자 웹 로그 작성
     * @param webLog 웹 로그 정보
     */
    @Transactional
    public void writeUserWebLog(WebServiceLog webLog) {
        userMapper.insertUserWebLog(webLog);
    }

    /**
     * 사용자 토큰 ID 업데이트
     * @param user 사용자 정보
     */
    @Transactional
    public void updateUserNewTokenId(User user) {
        userMapper.updateUserNewTokenId(user);
    }
}
