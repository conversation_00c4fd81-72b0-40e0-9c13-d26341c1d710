package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.annotation.RequireMenuPermission;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.dto.QuizLinkRequestDto;
import kr.wayplus.wayplus_qr.dto.request.QrCodeCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.QrCodeUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.QrCodeResponseDto;
import kr.wayplus.wayplus_qr.dto.response.SearchTypeInfo;
import org.springframework.format.annotation.DateTimeFormat;
import kr.wayplus.wayplus_qr.entity.QrCodeStatus;
import kr.wayplus.wayplus_qr.service.QrCodeService;
import kr.wayplus.wayplus_qr.config.SearchTypeRegistry;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.view.RedirectView;

import io.swagger.v3.oas.annotations.Operation;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;

@RestController
@RequestMapping("/api/way")
@RequiredArgsConstructor
@Slf4j
public class QrCodeController {

    private final QrCodeService qrCodeService;
    private final SearchTypeRegistry searchTypeRegistry;

    /**
     * 특정 QR 코드 정보 조회
     * @param qrCodeId 조회할 QR 코드 ID
     * @return QR 코드 정보
     */
    @GetMapping("/qr-codes/{qrCodeId}")
    @RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = RequireMenuPermission.PermissionType.READ)
    public ResponseEntity<ApiResponseDto<QrCodeResponseDto>> getQrCodeById(@PathVariable("qrCodeId") Long qrCodeId) {
        QrCodeResponseDto qrCodeDto = qrCodeService.getQrCodeById(qrCodeId);
        return ResponseEntity.ok(ApiResponseDto.success(qrCodeDto));
    }

    /**
     * 특정 프로젝트의 모든 QR 코드 목록 조회
     * @param projectId 프로젝트 ID
     * @return 해당 프로젝트의 QR 코드 목록
     */
    @GetMapping("/qr-codes/list")
    @RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = RequireMenuPermission.PermissionType.READ)
    public ResponseEntity<ApiResponseDto<ListResponseDto<QrCodeResponseDto>>> getQrCodesByProject(
            @RequestParam("projectId") Long projectId,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "createDate,desc") Pageable pageable) {
        if (searchType != null && !searchTypeRegistry.isValidSearchType("qrcode", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }
        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("qrcode");
        Page<QrCodeResponseDto> qrCodePage = qrCodeService.getQrCodesByProjectId(projectId, status, searchType, searchKeyword, pageable);
        ListResponseDto<QrCodeResponseDto> responseDto = new ListResponseDto<>(qrCodePage, availableSearchTypes);
        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    /**
     * 새로운 QR 코드 생성
     * @param projectId 프로젝트 ID
     * @param qrName QR 코드 이름
     * @param qrType QR 코드 타입
     * @param targetContent QR 코드 대상 콘텐츠
     * @param description QR 코드 설명
     * @param validFromDate QR 코드 유효 시작일
     * @param validToDate QR 코드 유효 종료일
     * @param designOptions QR 코드 디자인 옵션
     * @param status QR 코드 상태
     * @param logoImageFile QR 코드 이미지 파일
     * @param user 인증된 사용자 정보
     * @return 생성된 QR 코드 정보
     */
    @PostMapping(value = "/qr-codes", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = RequireMenuPermission.PermissionType.WRITE)
    public ResponseEntity<ApiResponseDto<QrCodeResponseDto>> createQrCode(
            @RequestParam(value = "projectId") Long projectId,
            @RequestParam(value = "qrName") String qrName,
            @RequestParam(value = "qrType") String qrType,
            @RequestParam(value = "targetContent") String targetContent,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "validFromDate", required = false) String validFromDateStr,
            @RequestParam(value = "validToDate", required = false) String validToDateStr,
            @RequestParam(value = "installationLocation", required = false) String InstallationLocation,
            @RequestParam(value = "installationLocationLat", required = false) String installationLocationLat,
            @RequestParam(value = "installationLocationLng", required = false) String installationLocationLng,
            @RequestParam(value = "designOptions", required = false) String designOptions,
            @RequestParam(value = "status", required = false) String statusStr,
            @RequestPart(value = "logoImageFile", required = false) MultipartFile logoImageFile,
            @RequestPart(value = "qrInstalledImageFile", required = false) MultipartFile qrInstalledImageFile,
            @RequestPart(value = "backgroundImageFile", required = false) MultipartFile backgroundImageFile,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        log.info("Creating QR code request received from user: {}", userEmail);
        
        // A4 캔버스 이미지 관련 로그 추가
        if (designOptions != null && !designOptions.trim().isEmpty()) {
            log.info("Design options received: {}", designOptions.length() > 500 ? 
                    designOptions.substring(0, 500) + "..." : designOptions);
        }
        
        // qrInstalledImageFile 로그 추가
        if (qrInstalledImageFile != null) {
            log.info("qrInstalledImageFile received: fileName={}, size={}, contentType={}", 
                     qrInstalledImageFile.getOriginalFilename(), 
                     qrInstalledImageFile.getSize(), 
                     qrInstalledImageFile.getContentType());
        } else {
            log.warn("qrInstalledImageFile is NULL");
        }

        QrCodeCreateRequestDto requestDto = QrCodeCreateRequestDto.builder()
                .projectId(projectId)
                .qrName(qrName)
                .qrType(qrType)
                .targetContent(targetContent)
                .description(description)
                .designOptions(designOptions != null ? designOptions : "{}")
                .logoImageFile(logoImageFile)
                .qrInstalledImageFile(qrInstalledImageFile)
                .backgroundImageFile(backgroundImageFile)
                .installationLocation(InstallationLocation)
                .installationLocationLat(installationLocationLat)
                .installationLocationLng(installationLocationLng)
                .build();

        if (statusStr != null && !statusStr.isBlank()) {
            try {
                requestDto.setStatus(QrCodeStatus.valueOf(statusStr.toUpperCase()));
            } catch (IllegalArgumentException e) {
                log.warn("Invalid status value provided in create request: '{}'", statusStr);
            }
        }

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (validFromDateStr != null && !validFromDateStr.isBlank()) {
            try {
                requestDto.setValidFromDate(LocalDateTime.parse(validFromDateStr, formatter));
            } catch (DateTimeParseException e) {
                log.warn("Invalid validFromDate format in create request: '{}'", validFromDateStr);
                return ResponseEntity.badRequest()
                        .body(ApiResponseDto.error("INVALID_DATE_FORMAT", "Invalid validFromDate format. Expected yyyy-MM-dd HH:mm:ss"));
            }
        }
        if (validToDateStr != null && !validToDateStr.isBlank()) {
            try {
                requestDto.setValidToDate(LocalDateTime.parse(validToDateStr, formatter));
            } catch (DateTimeParseException e) {
                log.warn("Invalid validToDate format in create request: '{}'", validToDateStr);
                return ResponseEntity.badRequest()
                        .body(ApiResponseDto.error("INVALID_DATE_FORMAT", "Invalid validToDate format. Expected yyyy-MM-dd HH:mm:ss"));
            }
        }

        QrCodeResponseDto createdQrCode = qrCodeService.createQrCode(requestDto, userEmail);
        // Return 201 Created status
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponseDto.success(createdQrCode));
    }

    /**
     * 기존 QR 코드 정보 수정
     * @param qrCodeId 수정할 QR 코드 ID
     * @param qrName QR 코드 이름
     * @param qrType QR 코드 타입
     * @param targetContent QR 코드 대상 콘텐츠
     * @param description QR 코드 설명
     * @param validFromDate QR 코드 유효 시작일
     * @param validToDate QR 코드 유효 종료일
     * @param designOptions QR 코드 디자인 옵션
     * @param status QR 코드 상태
     * @param projectId 프로젝트 ID
     * @param linkedLandingPageId 연결된 랜딩 페이지 ID
     * @param linkedEventId 연결된 이벤트 ID
     * @param logoImageFile 로고 이미지 파일
     * @param user 인증된 사용자 정보
     * @return 수정된 QR 코드 정보
     * @throws IOException 
     * @throws IllegalStateException 
     */
    @PutMapping(value = "/qr-codes/{qrCodeId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = RequireMenuPermission.PermissionType.UPDATE)
    public ResponseEntity<ApiResponseDto<QrCodeResponseDto>> updateQrCode(
            @PathVariable("qrCodeId") Long qrCodeId,
            @RequestParam(value = "qrName", required = false) String qrName,
            @RequestParam(value = "qrType", required = false) String qrType,
            @RequestParam(value = "targetContent", required = false) String targetContent,
            @RequestParam(value = "description", required = false) String description,
            @RequestParam(value = "validFromDate", required = false) String validFromDateStr,
            @RequestParam(value = "validToDate", required = false) String validToDateStr,
            @RequestParam(value = "designOptions", required = false) String designOptions,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "linkedLandingPageId", required = false) Long linkedLandingPageId,
            @RequestParam(value = "linkedEventId", required = false) Long linkedEventId,
            @RequestParam(value = "installationLocation", required = false) String installationLocation,
            @RequestParam(value = "installationLocationLat", required = false) String installationLocationLat,
            @RequestParam(value = "installationLocationLng", required = false) String installationLocationLng,
            @RequestPart(value = "logoImageFile", required = false) MultipartFile logoImageFile,
            @RequestPart(value = "qrInstalledImageFile", required = false) MultipartFile qrInstalledImageFile,
            @AuthenticationPrincipal User userDetails) throws IllegalStateException, IOException {
        
        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        
        log.info("Updating QR code ID: {} by user: {}", qrCodeId, userEmail);
        
        QrCodeUpdateRequestDto requestDto = QrCodeUpdateRequestDto.builder()
                .qrName(qrName)
                .qrType(qrType)
                .targetContent(targetContent)
                .description(description)
                .projectId(projectId)
                .designOptions(designOptions)
                .linkedLandingPageId(linkedLandingPageId)
                .linkedEventId(linkedEventId)
                .installationLocation(installationLocation)
                .installationLocationLat(installationLocationLat)
                .installationLocationLng(installationLocationLng)
                .qrInstalledImageFile(qrInstalledImageFile)
                .build();
        
        // 문자열 상태를 Enum으로 변환
        if (status != null && !status.isEmpty()) {
            try {
                requestDto.setStatus(QrCodeStatus.valueOf(status));
            } catch (IllegalArgumentException e) {
                log.error("Invalid status value: {}", status, e);
                return ResponseEntity.badRequest()
                        .body(ApiResponseDto.error("INVALID_STATUS_VALUE", "Invalid status value. Valid values are: ACTIVE, INACTIVE"));
            }
        }
        
        // 날짜 문자열 파싱
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        if (validFromDateStr != null && !validFromDateStr.isEmpty()) {
            try {
                requestDto.setValidFromDate(LocalDateTime.parse(validFromDateStr, formatter));
            } catch (DateTimeParseException e) {
                log.error("Invalid validFromDate format: {}", validFromDateStr, e);
                return ResponseEntity.badRequest()
                        .body(ApiResponseDto.error("INVALID_DATE_FORMAT", "Invalid validFromDate format. Expected yyyy-MM-dd HH:mm:ss"));
            }
        }
        
        if (validToDateStr != null && !validToDateStr.isEmpty()) {
            try {
                requestDto.setValidToDate(LocalDateTime.parse(validToDateStr, formatter));
            } catch (DateTimeParseException e) {
                log.error("Invalid validToDate format: {}", validToDateStr, e);
                return ResponseEntity.badRequest()
                        .body(ApiResponseDto.error("INVALID_DATE_FORMAT", "Invalid validToDate format. Expected yyyy-MM-dd HH:mm:ss"));
            }
        }

        QrCodeResponseDto updatedQrCode = qrCodeService.updateQrCode(qrCodeId, requestDto, userEmail);
        return ResponseEntity.ok(ApiResponseDto.success(updatedQrCode));
    }

    /**
     * QR 코드 삭제 (논리적 삭제)
     * @param qrCodeId 삭제할 QR 코드 ID
     * @param user 인증된 사용자 정보
     * @return 성공 응답 (데이터 없음)
     */
    @DeleteMapping("/qr-codes/remove/{qrCodeId}")
    @RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = RequireMenuPermission.PermissionType.DELETE)
    public ResponseEntity<ApiResponseDto<Void>> deleteQrCode(
            @PathVariable("qrCodeId") Long qrCodeId,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        qrCodeService.deleteQrCode(qrCodeId, userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(null));
    }

    /**
     * 여러 QR 코드 일괄 삭제 (논리적 삭제)
     * @param qrCodeIds 삭제할 QR 코드 ID 목록
     * @param userDetails 인증된 사용자 정보
     * @return 성공 응답 (데이터 없음)
     */
    @DeleteMapping("/qr-codes/all/remove")
    public ResponseEntity<ApiResponseDto<Void>> deleteQrCodes(
            @RequestBody List<Long> qrCodeIds,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        
        qrCodeService.deleteQrCodes(qrCodeIds, userEmail);
        
        return ResponseEntity.ok(ApiResponseDto.success(null));
    }

    /**
     * 특정 QR 코드의 통계 정보 조회
     * 
     * @param qrCodeId 통계를 조회할 QR 코드 ID
     * @param startDate 통계 조회 시작일 (YYYY-MM-DD 형식)
     * @param endDate 통계 조회 종료일 (YYYY-MM-DD 형식)
     * @return 해당 QR 코드의 기간별 스캔 통계 정보
     */
    @GetMapping("/qrcodes/{qrCodeId}/statistics")
    @RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = RequireMenuPermission.PermissionType.READ)
    public ResponseEntity<ApiResponseDto<Map<String, Object>>> getQrCodeStatistics(
            @PathVariable("qrCodeId") Long qrCodeId,
            @RequestParam("startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam("endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate
    ) {
        // 파라미터 유효성 검증
        if (startDate.isAfter(endDate)) {
            return ResponseEntity.badRequest()
                    .body(ApiResponseDto.error("INVALID_DATE_RANGE", "시작일은 종료일보다 이전이어야 합니다."));
        }
        
        Map<String, Object> statistics = qrCodeService.getQrCodeStatistics(qrCodeId, startDate, endDate);
        return ResponseEntity.ok(ApiResponseDto.success(statistics));
    }

    /**
     * QR 코드 스캔 시 처리 - QR 코드 타입에 따라 다르게 반응
     * @param qrUuid 스캔된 QR 코드 UUID
     * @return 'URL' 타입은 RedirectView, 'TEXT' 타입은 ResponseEntity로 반환
     */
    @GetMapping("/qr/redirect/{qrUuid}")
    public Object handleQrCode(@PathVariable("qrUuid") String qrUuid, HttpServletRequest request) {
        log.info("QR scan request received for UUID: {}", qrUuid);
        
        // QR 코드 타입과 내용 가져오기
        Map<String, String> qrInfo = qrCodeService.getQrCodeTypeAndContent(qrUuid, request);

        if (qrInfo.get("isDayExpired") != null && qrInfo.get("isDayExpired").equals("true")) {
            // 유효기간 에러.
                
            String htmlContent = String.format("""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>QR 코드 내용</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 100vh;
                            margin: 0;
                            background-color: #f5f5f5;
                            padding: 20px;
                            text-align: center;
                        }
                        .content-box {
                            max-width: 90%%;
                            background-color: white;
                            padding: 30px;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }
                        h1 {
                            font-size: 24px;
                            color: #333;
                            margin-bottom: 20px;
                        }
                        .text-content {
                            font-size: 20px;
                            line-height: 1.5;
                            color: #444;
                            word-break: break-word;
                        }
                    </style>
                </head>
                <body>
                    <div class="content-box">
                        <h1>유효기간이 지난 QR 코드입니다.</h1>
                    </div>
                </body>
                </html>
                """);
            
            // Content-Type을 text/html로 설정하여 HTML 응답 반환
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(htmlContent);
        }

        String qrType = qrInfo.get("qrType");
        String targetContent = qrInfo.get("targetContent");
        
        log.info("QR code type: {}, handling accordingly", qrType);
        
        // TEXT 타입은 리디렉션 대신 스타일이 적용된 HTML로 반환
        if ("TEXT".equalsIgnoreCase(qrType)) {
            log.info("TEXT type QR code, returning content as styled HTML: {}", targetContent);
            
            // HTML 템플릿 생성 - 텍스트를 크고 가운데 정렬된 형태로 표시
            String htmlContent = String.format("""
                <!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>QR 코드 내용</title>
                    <style>
                        body {
                            font-family: Arial, sans-serif;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                            height: 100vh;
                            margin: 0;
                            background-color: #f5f5f5;
                            padding: 20px;
                            text-align: center;
                        }
                        .content-box {
                            max-width: 90%%;
                            background-color: white;
                            padding: 30px;
                            border-radius: 8px;
                            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                        }
                        h1 {
                            font-size: 24px;
                            color: #333;
                            margin-bottom: 20px;
                        }
                        .text-content {
                            font-size: 20px;
                            line-height: 1.5;
                            color: #444;
                            word-break: break-word;
                        }
                    </style>
                </head>
                <body>
                    <div class="content-box">
                        <h1>QR 코드 내용</h1>
                        <div class="text-content">%s</div>
                    </div>
                </body>
                </html>
                """, targetContent);
            
            // Content-Type을 text/html로 설정하여 HTML 응답 반환
            return ResponseEntity
                    .ok()
                    .contentType(MediaType.TEXT_HTML)
                    .body(htmlContent);
        }
        
        // URL 등 다른 타입은 기존처럼 리디렉션
        log.info("Redirecting to URL: {}", targetContent);
        RedirectView redirectView = new RedirectView();
        redirectView.setUrl(targetContent);
        redirectView.setStatusCode(HttpStatus.FOUND);
        return redirectView;
    }

    /**
     * QR 코드에 퀴즈 연결
     * @param qrCodeId QR 코드 ID
     * @param requestDto 퀴즈 연결 요청 DTO
     * @param userDetails 인증된 사용자 정보
     * @return 연결 결과
     */
    @PostMapping("/qr-codes/{qrCodeId}/link-quiz")
    @Operation(summary = "QR 코드에 퀴즈 연결", description = "특정 QR 코드에 퀴즈를 연결합니다.")
    public ResponseEntity<ApiResponseDto<Map<String, Object>>> linkQuizToQrCode(
            @PathVariable Long qrCodeId,
            @Valid @RequestBody QuizLinkRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {
        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        Map<String, Object> result = qrCodeService.linkQuizToQrCode(qrCodeId, requestDto.getQuizId(), userEmail);
        result.put("message", "퀴즈가 성공적으로 연결되었습니다.");
        return ResponseEntity.ok(ApiResponseDto.success(result));
    }

    @PostMapping("/qr-codes/unlink-quiz")
    @Operation(summary = "QR코드-퀴즈 연결 해제", description = "특정 QR코드에 연결된 퀴즈를 해제합니다.")
    public ResponseEntity<Map<String, Object>> unlinkQuizFromQrCode(@RequestBody Map<String, Long> payload, Authentication authentication) {
        String userEmail = authentication.getName();
        Long qrCodeId = payload.get("qrCodeId");
        Long quizId = payload.get("quizId");

        if (qrCodeId == null || quizId == null) {
            throw new IllegalArgumentException("qrCodeId와 quizId는 필수입니다.");
        }

        Map<String, Object> response = qrCodeService.unlinkQuizFromQrCode(qrCodeId, quizId, userEmail);
        return ResponseEntity.ok(response);
    }

}
