package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.request.TeamRequestDto;
import kr.wayplus.wayplus_qr.dto.response.TeamResponseDto;
import kr.wayplus.wayplus_qr.entity.Team;
import kr.wayplus.wayplus_qr.entity.enums.UserRole;
import kr.wayplus.wayplus_qr.exception.CustomException;
import kr.wayplus.wayplus_qr.exception.CustomException;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.mapper.AttendeeMapper;
import kr.wayplus.wayplus_qr.mapper.ProjectMapper;
import kr.wayplus.wayplus_qr.mapper.TeamMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Optional;

@Service
@Slf4j
@RequiredArgsConstructor
public class TeamService {
    
    @Value("${server.base-url}")
    private String baseUrl;
    
    private final AttendeeMapper attendeeMapper;
    private final ProjectMapper projectMapper;
    private final UserMapper userMapper;
    private final TeamMapper teamMapper;
    private final FileStorageService fileStorageService;
    
    /**
     * 팀 등록
     * 
     * @param requestDto 팀 등록 요청 데이터
     * @param createUserEmail 생성자 이메일
     * @return 등록된 팀 정보
     */
    @Transactional
    public TeamResponseDto createTeam(TeamRequestDto requestDto, String createUserEmail) {
        log.info("[TeamService] Creating team with name: {}, eventId: {}", requestDto.getTeamName(), requestDto.getEventId());
        
        // 이벤트 내에서 팀명 중복 체크
        int duplicateCount = teamMapper.countTeamsByEventIdAndName(requestDto.getEventId(), requestDto.getTeamName(), null);
        if (duplicateCount > 0) {
            log.warn("[TeamService] Team name already exists in event {}: {}", requestDto.getEventId(), requestDto.getTeamName());
            throw new CustomException(ErrorCode.DUPLICATE_TEAM_NAME);
        }
        
        // 팀 코드 생성 또는 검증
        String teamCode = requestDto.getTeamCode();
        if (teamCode == null || teamCode.trim().isEmpty()) {
            // 팀 코드가 비어있으면 자동 생성 (팀이름-4자리숫자)
            teamCode = generateUniqueTeamCode(requestDto.getTeamName());
            log.info("[TeamService] Generated team code: {}", teamCode);
        } else {
            // 기존 팀 코드 중복 체크
            int duplicateCodeCount = teamMapper.countTeamsByEventIdAndCode(teamCode, null);
            if (duplicateCodeCount > 0) {
                log.warn("[TeamService] Team code already exists in event {}: {}", requestDto.getEventId(), teamCode);
                throw new CustomException(ErrorCode.DUPLICATE_TEAM_CODE);
            }
        }
        
        // 이미지 파일 처리
        String profileImagePath = null;
        MultipartFile profileImageFile = requestDto.getProfileImageFile();
        if (profileImageFile != null && !profileImageFile.isEmpty()) {
            try {
                String storedFileName = fileStorageService.storeFile(profileImageFile);
                // DB에 저장될 경로 생성 (/uploads/ 접두사 추가)
                profileImagePath = "/uploads/" + storedFileName;
                log.info("[TeamService] Stored team image file, path: {}", profileImagePath);
            } catch (Exception e) {
                log.error("[TeamService] Failed to save team image file", e);
            }
        }
        
        // Team 엔티티 생성
        Team team = Team.builder()
                .eventId(requestDto.getEventId())
                .teamName(requestDto.getTeamName())
                .teamCode(teamCode)
                .teamStatus(requestDto.getTeamStatus())
                .description(requestDto.getDescription())
                .maxMembers(requestDto.getMaxMembers())
                .profileImagePath(profileImagePath != null ? baseUrl + profileImagePath : null)
                .joinQrCodeId(requestDto.getJoinQrCodeId())
                .leaderAttendeeId(requestDto.getLeaderAttendeeId())
                .leaderName(requestDto.getLeaderName())
                .leaderPhone(requestDto.getLeaderPhone())
                .createUserEmail(createUserEmail)
                .build();
        
        // 팀 등록
        int result = teamMapper.insertTeam(team);
        if (result == 0) {
            log.error("[TeamService] Failed to create team: {}", requestDto.getTeamName());
            throw new CustomException(ErrorCode.TEAM_CREATE_FAILED);
        }
        
        log.info("[TeamService] Successfully created team with ID: {}", team.getTeamId());
        return TeamResponseDto.builder()
                .teamId(team.getTeamId())
                .eventId(team.getEventId())
                .teamName(team.getTeamName())
                .teamCode(team.getTeamCode())
                .teamStatus(team.getTeamStatus())
                .description(team.getDescription())
                .maxMembers(team.getMaxMembers())
                .profileImagePath(team.getProfileImagePath() != null ? baseUrl + team.getProfileImagePath() : null)
                .joinQrCodeId(team.getJoinQrCodeId())
                .leaderAttendeeId(team.getLeaderAttendeeId())
                .leaderName(team.getLeaderName())
                .leaderPhone(team.getLeaderPhone())
                .createUserEmail(team.getCreateUserEmail())
                .build();
    }
    
    /**
     * 팀 수정
     * 
     * @param teamId 수정할 팀 ID
     * @param requestDto 팀 수정 요청 데이터
     * @param updateUserEmail 수정자 이메일
     * @return 수정된 팀 정보
     */
    @Transactional
    public TeamResponseDto updateTeam(Long teamId, TeamRequestDto requestDto, String updateUserEmail) {
        log.info("[TeamService] Updating team ID: {} with name: {}", teamId, requestDto.getTeamName());
        
        // 팀 존재 여부 확인
        Team existingTeam = teamMapper.selectTeamById(teamId).orElse(null);
        if (existingTeam == null) {
            log.warn("[TeamService] Team not found with ID: {}", teamId);
            throw new CustomException(ErrorCode.TEAM_NOT_FOUND);
        }
        
        // 이벤트 내에서 팀명 중복 체크 (자기 자신 제외)
        int duplicateCount = teamMapper.countTeamsByEventIdAndName(existingTeam.getEventId(), requestDto.getTeamName(), teamId);
        if (duplicateCount > 0) {
            log.warn("[TeamService] Team name already exists in event {}: {}", existingTeam.getEventId(), requestDto.getTeamName());
            throw new CustomException(ErrorCode.DUPLICATE_TEAM_NAME);
        }
        
        // 이미지 파일 처리
        String profileImagePath = existingTeam.getProfileImagePath();
        MultipartFile teamImageFile = requestDto.getProfileImageFile();
        
        System.out.println(profileImagePath);
        System.out.println("teamImageFileteamImageFile " + teamImageFile);
        // 이미지 파일이 전송되었는지 확인
        if (teamImageFile != null) {
            if (!teamImageFile.isEmpty()) {
                // 새로운 이미지 파일이 있는 경우
                try {
                    // 기존 이미지 파일 삭제
                    if (profileImagePath != null && !profileImagePath.isEmpty()) {
                        String filenameToDelete = profileImagePath.replace("/uploads/", "");
                        fileStorageService.deleteFile(filenameToDelete);
                    }
                    
                    String storedFileName = fileStorageService.storeFile(teamImageFile);
                    // DB에 저장될 경로 생성 (/uploads/ 접두사 추가)
                    profileImagePath = "/uploads/" + storedFileName;
                    log.info("[TeamService] Updated team image file, path: {}", profileImagePath);
                } catch (Exception e) {
                    log.error("[TeamService] Failed to save team image file", e);
                    // 이미지 저장 실패는 중대한 오류가 아니므로 진행
                }
            } else {
                // 빈 파일이 전송된 경우 - DB에 빈값으로 처리
                if (profileImagePath != null && !profileImagePath.isEmpty()) {
                    String filenameToDelete = profileImagePath.replace("/uploads/", "");
                    fileStorageService.deleteFile(filenameToDelete);
                }
                profileImagePath = null;
                log.info("[TeamService] Profile image cleared for team ID: {}", teamId);
            }
        } else if (profileImagePath != null && !profileImagePath.isEmpty()) {
            if (profileImagePath != null && !profileImagePath.isEmpty()) {
                String filenameToDelete = profileImagePath.replace("/uploads/", "");
                fileStorageService.deleteFile(filenameToDelete);
            }
            profileImagePath = null;
            log.info("[TeamService] Profile image cleared via profile_img_path for team ID: {}", teamId);
        }
        // teamImageFile과 profileImgPath가 모두 null인 경우는 기존 이미지 경로 유지
        
        // Team 엔티티 생성
        Team team = Team.builder()
                .teamId(teamId)
                .teamName(requestDto.getTeamName())
                .teamCode(requestDto.getTeamCode())
                .teamStatus(requestDto.getTeamStatus())
                .description(requestDto.getDescription())
                .maxMembers(requestDto.getMaxMembers())
                .profileImagePath(profileImagePath)
                .joinQrCodeId(requestDto.getJoinQrCodeId())
                .leaderAttendeeId(requestDto.getLeaderAttendeeId())
                .leaderName(requestDto.getLeaderName())
                .leaderPhone(requestDto.getLeaderPhone())
                .updateUserEmail(updateUserEmail)
                .build();
        
        // 팀 수정
        int result = teamMapper.updateTeam(team);
        if (result == 0) {
            log.error("[TeamService] Failed to update team ID: {}", teamId);
            throw new CustomException(ErrorCode.TEAM_UPDATE_FAILED);
        }
        
        log.info("[TeamService] Successfully updated team ID: {}", teamId);
        return TeamResponseDto.builder()
                .teamId(teamId)
                .teamName(requestDto.getTeamName())
                .teamCode(requestDto.getTeamCode())
                .teamStatus(requestDto.getTeamStatus())
                .description(requestDto.getDescription())
                .maxMembers(requestDto.getMaxMembers())
                .profileImagePath(profileImagePath)
                .joinQrCodeId(requestDto.getJoinQrCodeId())
                .leaderAttendeeId(requestDto.getLeaderAttendeeId())
                .leaderName(requestDto.getLeaderName())
                .leaderPhone(requestDto.getLeaderPhone())
                .updateUserEmail(updateUserEmail)
                .build();
    }
    
    /**
     * 팀 삭제 (논리 삭제)
     * 
     * @param teamId 삭제할 팀 ID
     * @param deleteUserEmail 삭제자 이메일
     */
    @Transactional
    public void deleteTeam(Long teamId, String deleteUserEmail) {
        log.info("[TeamService] Deleting team ID: {}", teamId);
        
        // 팀 존재 여부 확인
        Optional<Team> existingTeam = teamMapper.selectTeamById(teamId);
        if (existingTeam == null) {
            log.warn("[TeamService] Team not found with ID: {}", teamId);
            throw new CustomException(ErrorCode.TEAM_NOT_FOUND);
        }
        
        // 팀 삭제
        int result = teamMapper.deleteTeam(teamId, deleteUserEmail);
        if (result == 0) {
            log.error("[TeamService] Failed to delete team ID: {}", teamId);
            throw new CustomException(ErrorCode.TEAM_DELETE_FAILED);
        }
        
        log.info("[TeamService] Successfully deleted team ID: {}", teamId);
    }
    
    /**
     * 팀 ID로 팀 조회
     * 
     * @param teamId 조회할 팀 ID
     * @return 팀 정보
     */
    public TeamResponseDto getTeamById(Long teamId, String userEmail, Long projectId) {
        log.info("[TeamService] Getting team by ID: {}", teamId);

        //팀 존재 확인
        Team team = teamMapper.selectTeamById(teamId)
                .orElseThrow(() -> new CustomException(ErrorCode.TEAM_NOT_FOUND));

        // 프로젝트 권한 확인
        if (userEmail != null) {
            String userRole = userMapper.selectUserRoleByEmail(userEmail)
                    .orElseThrow(() -> new CustomException(
                            ErrorCode.USER_NOT_FOUND,
                            "사용자를 찾을 수 없습니다: " + userEmail));
            if (!UserRole.SUPER_ADMIN.name().equals(userRole)) {
                int membershipCount = userMapper.countUserProjectMembership(userEmail, projectId);
                if (membershipCount == 0) {
                    log.warn("Access denied for user {} trying to access team {}", userEmail, projectId);
                    throw new CustomException(ErrorCode.ACCESS_DENIED, "해당 팀에 대한 접근 권한이 없습니다.");
                }
            }
        }
        
        if (team == null) {
            log.warn("[TeamService] Team not found with ID: {}", teamId);
            throw new CustomException(ErrorCode.TEAM_NOT_FOUND);
        }

        return TeamResponseDto.builder()
        .teamId(team.getTeamId())
        .projectId(team.getProjectId())
        .eventId(team.getEventId())
        .teamName(team.getTeamName())
        .description(team.getDescription())
        .teamCode(team.getTeamCode())
        .teamStatus(team.getTeamStatus())
        .maxMembers(team.getMaxMembers())
        .profileImagePath(team.getProfileImagePath() != null ? baseUrl + team.getProfileImagePath() : null)
        .joinQrCodeId(team.getJoinQrCodeId())
        .leaderAttendeeId(team.getLeaderAttendeeId())
        .leaderName(team.getLeaderName())
        .leaderPhone(team.getLeaderPhone())
        .createUserEmail(team.getCreateUserEmail())
        .createDate(team.getCreateDate())
        .updateUserEmail(team.getUpdateUserEmail())
        .lastUpdateDate(team.getLastUpdateDate())
        .deleteUserEmail(team.getDeleteUserEmail())
        .deleteDate(team.getDeleteDate())
        .build();

    }
    
    /**
     * 이벤트별 팀 목록 조회
     * 
     * @param eventId 이벤트 ID
     * @return 팀 목록
     */
    public List<TeamResponseDto> getTeamsByEventId(Long eventId) {
        log.info("[TeamService] Getting teams by event ID: {}", eventId);
        return teamMapper.selectTeamsByEventId(eventId);
    }
    
    /**
     * 팀 목록 조회 (페이징, 필터링)
     * 
     * @param eventId 이벤트 ID (선택)
     * @param teamType 팀 유형 (선택)
     * @param teamStatus 팀 상태 (선택)
     * @param searchType 검색 타입 (선택)
     * @param searchKeyword 검색 키워드 (선택)
     * @param userEmail 사용자 이메일
     * @param pageable 페이징 정보
     * @return 팀 목록 (페이징)
     */
    public Page<TeamResponseDto> getTeamsByProjectId(
            Long projectId, Long eventId, 
            String searchType, String searchKeyword, String userEmail, Pageable pageable) {
        
        log.info("[TeamService] Getting teams with filters - eventId: {}, searchType: {}, searchKeyword: {}", 
                eventId, searchType, searchKeyword);
        // 프로젝트 존재 및 권한 확인
        if (projectId != null) {
            projectMapper.selectProjectById(projectId)
                    .orElseThrow(() -> new CustomException(ErrorCode.PROJECT_NOT_FOUND, "프로젝트를 찾을 수 없습니다: ID=" + projectId));
        }

        // 권한 확인
        String userRole = userMapper.selectUserRoleByEmail(userEmail)
                .orElseThrow(() -> new CustomException(ErrorCode.USER_NOT_FOUND, "사용자를 찾을 수 없습니다: " + userEmail));
        if (!userRole.equals(UserRole.SUPER_ADMIN.name())) {
            int membershipCount = userMapper.countUserProjectMembership(userEmail, projectId);
            if (membershipCount == 0) {
                log.warn("Access denied for user {} on project {}", userEmail, projectId);
                throw new CustomException(ErrorCode.ACCESS_DENIED, "해당 프로젝트에 대한 접근 권한이 없습니다.");
            }
        }

        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "teamName": searchColumn = "team_name"; break;
                case "teamCode": searchColumn = "team_code"; break;
                case "leaderName": searchColumn = "leader_name"; break;
                case "teamStatus": searchColumn = "team_status"; break;
                case "createDate": searchColumn = "create_date"; break;
            }
        }
        
        // 전체 개수 조회
        int totalCount = teamMapper.countTeamsWithFilters(projectId, eventId, searchColumn, searchKeyword);
        
        // 페이징된 데이터 조회
        List<TeamResponseDto> teams = teamMapper.selectTeamsWithFilters(
                projectId, eventId, searchColumn, searchKeyword, pageable);
        
        return new PageImpl<>(teams, pageable, totalCount);
    }
    
    /**
     * 모든 팀 목록 조회
     * 
     * @return 팀 목록
     */
    public List<TeamResponseDto> getAllTeams() {
        log.info("[TeamService] Getting all teams");
        return teamMapper.selectAllTeams();
    }
    
    /**
     * 이벤트 내에서 팀명으로 팀 검색
     * 
     * @param eventId 이벤트 ID
     * @param teamName 검색할 팀명
     * @return 검색된 팀 목록
     */
    public List<TeamResponseDto> searchTeamsByEventIdAndName(Long eventId, String teamName) {
        log.info("[TeamService] Searching teams by event ID: {} and name: {}", eventId, teamName);
        return teamMapper.selectTeamsByEventIdAndName(eventId, teamName);
    }
    
    /**
     * 팀 리더 참가자 ID로 팀 검색
     * 
     * @param leaderAttendeeId 팀 리더 참가자 ID
     * @return 검색된 팀 목록
     */
    public List<TeamResponseDto> getTeamsByLeaderAttendeeId(Long leaderAttendeeId) {
        log.info("[TeamService] Getting teams by leader attendee ID: {}", leaderAttendeeId);
        return teamMapper.selectTeamsByLeaderAttendeeId(leaderAttendeeId);
    }
    
    /**
     * 팀 멤버 수 제한 검증
     * 
     * @param teamId 팀 ID
     * @param userEmail 사용자 이메일 (권한 확인용)
     * @param projectId 프로젝트 ID
     * @throws CustomException 팀이 가득 찬 경우
     */
    public void validateTeamCapacity(Long teamId, String userEmail, Long projectId) {
        log.info("[TeamService] Validating team capacity for team ID: {}", teamId);
        
        // 팀 정보 조회
        TeamResponseDto team = getTeamById(teamId, userEmail, projectId);
        if (team == null) {
            log.error("[TeamService] Team not found with ID: {}", teamId);
            throw new CustomException(ErrorCode.TEAM_NOT_FOUND);
        }

        if (team.getMaxMembers() != null) {
            // 현재 팀 멤버 수 조회
            long currentMemberCount = attendeeMapper.countAttendeesByTeamId(teamId);
                    
            // 최대 멤버 수와 비교
            if (currentMemberCount >= team.getMaxMembers()) {
                log.warn("[TeamService] Team is full. Current: {}, Max: {}, Team ID: {}", 
                        currentMemberCount, team.getMaxMembers(), teamId);
                String detailedMessage = String.format("팀 정원이 가득 찼습니다. (등록된 인원: %d명, 최대 정원: %d명, 초과 인원: %d명)", 
                        currentMemberCount, team.getMaxMembers(), currentMemberCount - team.getMaxMembers() + 1);
                throw new CustomException(ErrorCode.TEAM_FULL, detailedMessage);
            }

            log.info("[TeamService] Team capacity validation passed. Current: {}, Max: {}, Team ID: {}", 
            currentMemberCount, team.getMaxMembers(), teamId);
        }
        
        
    }
    
    /**
     * 고유한 팀 코드 생성 (팀이름-4자리숫자)
     * 
     * @param eventId 이벤트 ID
     * @param teamName 팀 이름
     * @return 생성된 고유 팀 코드
     */
    private String generateUniqueTeamCode(String teamName) {
        String baseCode = teamName + "-";
        int attempts = 0;
        int maxAttempts = 100; // 최대 시도 횟수
        
        while (attempts < maxAttempts) {
            // 4자리 랜덤 숫자 생성 (1000-9999)
            int randomNumber = (int) (Math.random() * 9000) + 1000;
            String teamCode = baseCode + randomNumber;
            
            // 중복 체크
            int duplicateCount = teamMapper.countTeamsByEventIdAndCode(teamCode, null);
            if (duplicateCount == 0) {
                return teamCode;
            }
            
            attempts++;
        }
        
        // 최대 시도 횟수를 초과한 경우 예외 발생
        log.error("[TeamService] Failed to generate unique team code after {} attempts for team: {}", maxAttempts, teamName);
        throw new CustomException(ErrorCode.TEAM_CREATE_FAILED);
    }
}