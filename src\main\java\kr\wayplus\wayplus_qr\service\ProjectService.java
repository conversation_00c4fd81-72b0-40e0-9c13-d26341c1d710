package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.request.ProjectCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.ProjectUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ProjectResponseDto;
import kr.wayplus.wayplus_qr.entity.Project;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.entity.enums.UserRole;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.exception.CustomProjectException;
import kr.wayplus.wayplus_qr.mapper.ProjectMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ProjectService {

    private final ProjectMapper projectMapper;
    private final UserMapper userMapper;
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final String PROJECT_ADMIN_ROLE_NAME = UserRole.PROJECT_ADMIN.name();

    // 허용되는 정렬 컬럼 정의 (DB 컬럼명 기준)
    private static final Set<String> ALLOWED_SORT_COLUMNS = Set.of("project_id", "project_name", "create_date", "last_update_date");

    // 프로젝트 생성
    @Transactional
    public ApiResponseDto<ProjectResponseDto> createProject(ProjectCreateRequestDto requestDto, String creatorEmail) {
        // 1. 프로젝트 이름 중복 검사
        projectMapper.selectProjectByName(requestDto.getProjectName()).ifPresent(p -> {
            throw new CustomProjectException(ErrorCode.PROJECT_NAME_DUPLICATION);
        });

        // 2. 프로젝트 관리자 유효성 검사 (선택 사항)
        String adminEmail = requestDto.getProjectAdminUserEmail();
        if (StringUtils.hasText(adminEmail)) {
            validateProjectAdminRole(adminEmail);
        }

        // 4. Project 엔티티 생성 및 저장
        Project project = Project.builder()
                .projectName(requestDto.getProjectName())
                .description(requestDto.getDescription())
                .projectAdminUserEmail(StringUtils.hasText(adminEmail) ? adminEmail : null)
                .createUserEmail(creatorEmail)
                .updateUserEmail(creatorEmail)
                .createDate(LocalDateTime.now())
                .lastUpdateDate(LocalDateTime.now())
                .deleteYn("N")
                .build();

        projectMapper.insertProject(project);

        // 5. 프로젝트 관리자 지정 시, user_project_mapping 테이블에 매핑 정보 추가
        if (StringUtils.hasText(adminEmail)) {
            try {
                Map<String, Object> mappingParams = new HashMap<>();
                mappingParams.put("userEmail", adminEmail);
                mappingParams.put("projectId", project.getProjectId());
                mappingParams.put("membershipRole", PROJECT_ADMIN_ROLE_NAME);
                mappingParams.put("createUserEmail", creatorEmail);

                int mappingInsertedRows = userMapper.insertUserProjectMapping(mappingParams);
                if (mappingInsertedRows > 0) {
                    log.info("Successfully mapped project admin {} to project ID {}", adminEmail, project.getProjectId());
                } else {
                    log.warn("Failed to insert user-project mapping for admin {} and project ID {}", adminEmail, project.getProjectId());
                    throw new CustomProjectException(ErrorCode.INTERNAL_SERVER_ERROR, "프로젝트 관리자 매핑 정보 저장 실패");
                }
            } catch (Exception e) {
                // 데이터베이스 오류 또는 기타 예외 처리
                log.error("Error occurred while inserting user-project mapping for admin {} and project ID {}: {}",
                        adminEmail, project.getProjectId(), e.getMessage(), e);
                // 예외를 다시 던져 트랜잭션 롤백 유도
                throw new CustomProjectException(ErrorCode.INTERNAL_SERVER_ERROR, "프로젝트 관리자 매핑 정보 저장 중 오류 발생");
            }
        }

        // 6. 생성된 프로젝트 정보 반환
        ProjectResponseDto responseDto = convertToDto(project);
        return ApiResponseDto.success(responseDto);
    }

    // 프로젝트 전체 조회
    @Transactional(readOnly = true)
    public Page<ProjectResponseDto> getAllProjects(String status, String searchType, String searchKeyword, Pageable pageable) {
        // 검색용 컬럼 매핑
        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "projectName": searchColumn = "project_name"; break;
                case "description": searchColumn = "description"; break;
                case "projectAdminUserEmail": searchColumn = "project_admin_user_email"; break;
                case "status": searchColumn = "status"; break;
                case "createUserEmail": searchColumn = "create_user_email"; break;
                case "createDate": searchColumn = "create_date"; break;
                case "lastUpdateDate": searchColumn = "last_update_date"; break;
            }
        }
        // 1. 페이징된 프로젝트 목록 조회
        List<Project> projectList = projectMapper.selectProjectListWithPaging(status, searchColumn, searchKeyword, pageable);
        List<ProjectResponseDto> projectDtos = projectList.stream()
                .map(this::convertToDto)
                .collect(Collectors.toList());

        // 2. 전체 프로젝트 개수 조회
        int total = projectMapper.countAllProjects(status, searchColumn, searchKeyword);

        // 3. Page 객체 생성 및 반환
        return new PageImpl<>(projectDtos, pageable, total);
    }

    // SUPER_ADMIN: 모든 프로젝트 목록 조회 (페이징)
    @Transactional(readOnly = true)
    public Page<ProjectResponseDto> getAllProjectsForSuperAdmin(String searchType, String searchKeyword, Pageable pageable) {
        //검색타입 설정. 실제 DB 컬럼과 연동.
        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "projectName": searchColumn = "project_name"; break;
                case "description": searchColumn = "description"; break;
                case "projectAdminUserEmail": searchColumn = "project_admin_user_email"; break;
                case "status": searchColumn = "status"; break;
                case "createUserEmail": searchColumn = "create_user_email"; break;
                case "createDate": searchColumn = "create_date"; break;
                case "lastUpdateDate": searchColumn = "last_update_date"; break;
            }
        }

        List<Project> projects = projectMapper.selectAllProjectsForSuperAdmin(searchColumn, searchKeyword, pageable);
        long total = projectMapper.countAllProjectsForSuperAdmin(searchColumn, searchKeyword);

        if (projects.isEmpty()) {
            return new PageImpl<>(Collections.emptyList(), pageable, total);
        }

        List<ProjectResponseDto> dtoList = projects.stream()
                .map(this::convertToDto) // 엔티티 -> DTO 변환
                .collect(Collectors.toList());

        return new PageImpl<>(dtoList, pageable, total);
    }

    // 프로젝트 단건 조회
    @Transactional(readOnly = true)
    public ApiResponseDto<ProjectResponseDto> getProjectById(Long projectId) {
        Project project = projectMapper.selectProjectById(projectId)
                .orElseThrow(() -> new CustomProjectException(ErrorCode.PROJECT_NOT_FOUND, "프로젝트를 찾을 수 없습니다: ID " + projectId));
        if ("Y".equals(project.getDeleteYn())) {
            throw new CustomProjectException(ErrorCode.PROJECT_NOT_FOUND, "프로젝트를 찾을 수 없습니다: ID " + projectId);
        }
        return ApiResponseDto.success(convertToDto(project));
    }

    // 프로젝트 수정
    @Transactional
    public ApiResponseDto<ProjectResponseDto> updateProject(Long projectId, ProjectUpdateRequestDto requestDto, String updateUserEmail) {
        // 1. 프로젝트 존재 확인
        Project existingProject = projectMapper.selectProjectById(projectId)
                .orElseThrow(() -> new CustomProjectException(ErrorCode.PROJECT_NOT_FOUND, "수정할 프로젝트를 찾을 수 없습니다: ID " + projectId));

        // 2. 프로젝트 이름 중복 검사 (수정하려는 이름이 현재 프로젝트 이름과 다르고, 다른 프로젝트에서 사용 중인 경우)
        String newProjectName = requestDto.getProjectName();
        if (StringUtils.hasText(newProjectName) && !newProjectName.equals(existingProject.getProjectName())) {
            Optional<Project> projectWithNewName = projectMapper.selectProjectByName(newProjectName);
            // 조회된 프로젝트가 존재하고, 그 ID가 현재 수정 중인 프로젝트의 ID와 다를 경우에만 중복으로 처리
            if (projectWithNewName.isPresent() && !projectWithNewName.get().getProjectId().equals(existingProject.getProjectId())) {
                throw new CustomProjectException(ErrorCode.PROJECT_NAME_DUPLICATION);
            }
        }

        // 3. 프로젝트 관리자 유효성 검사 (선택 사항)
        String oldAdminEmail = existingProject.getProjectAdminUserEmail();
        String newAdminEmail = requestDto.getProjectAdminUserEmail(); // null일 수도 있음

        // 4. 새로운 프로젝트 관리자 유효성 검사 (제공된 경우)
        if (StringUtils.hasText(newAdminEmail)) {
            validateProjectAdminRole(newAdminEmail);
        } else {
            // 명시적으로 null이나 빈 문자열이 오면 관리자 제거로 간주
            newAdminEmail = null;
        }

        // 6. 관리자 변경 여부 확인 및 매핑 테이블 업데이트
        if (!Objects.equals(oldAdminEmail, newAdminEmail)) {
            log.info("Project admin change detected for project ID {}. Old: '{}', New: '{}'", projectId, oldAdminEmail, newAdminEmail);

            // 6.1. 기존 관리자 매핑 제거 (논리적 삭제)
            if (StringUtils.hasText(oldAdminEmail)) {
                try {
                    Map<String, Object> deleteParams = new HashMap<>();
                    deleteParams.put("userEmail", oldAdminEmail);
                    deleteParams.put("projectId", projectId);
                    deleteParams.put("updateUserEmail", updateUserEmail); // 삭제 요청자
                    int deletedRows = userMapper.deleteUserProjectMappingLogically(deleteParams);
                    if (deletedRows > 0) {
                        log.info("Successfully removed old project admin mapping for user {} and project ID {}", oldAdminEmail, projectId);
                    } else {
                        log.warn("Could not find or remove old project admin mapping for user {} and project ID {}. It might have been removed already.", oldAdminEmail, projectId);
                    }
                } catch (Exception e) {
                    log.error("Error removing old project admin mapping for user {} and project ID {}: {}", oldAdminEmail, projectId, e.getMessage(), e);
                    throw new CustomProjectException(ErrorCode.INTERNAL_SERVER_ERROR, "기존 프로젝트 관리자 매핑 제거 중 오류 발생");
                }
            }

            // 6.2. 새로운 관리자 매핑 추가
            if (StringUtils.hasText(newAdminEmail)) {
                try {
                    Map<String, Object> insertParams = new HashMap<>();
                    insertParams.put("userEmail", newAdminEmail);
                    insertParams.put("projectId", projectId);
                    insertParams.put("membershipRole", PROJECT_ADMIN_ROLE_NAME);
                    insertParams.put("createUserEmail", updateUserEmail); // 매핑 추가 요청자

                    int insertedRows = userMapper.insertUserProjectMapping(insertParams);
                    if (insertedRows > 0) {
                        log.info("Successfully added new project admin mapping for user {} and project ID {}", newAdminEmail, projectId);
                    } else {
                        log.warn("Failed to insert new project admin mapping for user {} and project ID {}", newAdminEmail, projectId);
                        // 이미 존재하는 경우 unique 제약 조건 위반 발생 가능성 있음 - Mapper에서 INSERT IGNORE 또는 ON DUPLICATE KEY UPDATE 고려 혹은 여기서 예외처리
                        // 또는 여기서 특정 예외를 던져 롤백
                        throw new CustomProjectException(ErrorCode.INTERNAL_SERVER_ERROR, "신규 프로젝트 관리자 매핑 정보 저장 실패");
                    }
                } catch (Exception e) {
                    log.error("Error adding new project admin mapping for user {} and project ID {}: {}", newAdminEmail, projectId, e.getMessage(), e);
                    throw new CustomProjectException(ErrorCode.INTERNAL_SERVER_ERROR, "신규 프로젝트 관리자 매핑 정보 저장 중 오류 발생");
                }
            }
            // 6.3. 프로젝트 엔티티에 새 관리자 이메일 설정
            existingProject.setProjectAdminUserEmail(newAdminEmail);
            log.debug("Project admin email updated in project entity for ID: {}", projectId);
        }

        // 7. 최종 업데이트 정보 설정 및 저장 (기존 4, 5번)
        existingProject.setProjectName(requestDto.getProjectName());
        existingProject.setDescription(requestDto.getDescription());
        existingProject.setUpdateUserEmail(updateUserEmail);
        existingProject.setLastUpdateDate(LocalDateTime.now());

        int updatedRows = projectMapper.updateProject(existingProject);

        // 8. 업데이트 결과 확인 (기존 6번)
        if (updatedRows == 0) {
            throw new CustomProjectException(ErrorCode.INTERNAL_SERVER_ERROR, "프로젝트 업데이트 실패");
        }

        // 9. 업데이트된 프로젝트 정보 반환 (기존 7번)
        ProjectResponseDto responseDto = convertToDto(existingProject); // 업데이트된 엔티티 사용
        return ApiResponseDto.success(responseDto);
    }

    // 프로젝트 삭제 (Soft Delete)
    @Transactional
    public ApiResponseDto<Void> deleteProject(Long projectId, String updateUserEmail) {
        // 1. 프로젝트 존재 확인 (삭제되지 않은 프로젝트만 대상으로)
        projectMapper.selectProjectById(projectId)
                .orElseThrow(() -> new CustomProjectException(ErrorCode.PROJECT_NOT_FOUND, "삭제할 프로젝트를 찾을 수 없습니다: ID " + projectId));

        // 2. 프로젝트 삭제 처리 (delete_yn = 'Y')
        int deletedRows = projectMapper.deleteProjectById(projectId, updateUserEmail);

        // 3. 삭제 결과 확인 (실제로 행이 업데이트되었는지 확인)
        if (deletedRows == 0) {
            // 이미 삭제되었거나 다른 이유로 업데이트 실패
            log.warn("Project deletion failed or already deleted. Project ID: {}", projectId);
            // 이미 삭제된 경우 등을 고려하여 NotFound 또는 별도 에러코드 반환 가능
            throw new CustomProjectException(ErrorCode.PROJECT_NOT_FOUND, "프로젝트 삭제 중 오류가 발생했거나 이미 삭제된 상태일 수 있습니다.");
        }

        log.info("Project soft deleted successfully. Project ID: {}, Deleted by: {}", projectId, updateUserEmail);
        return ApiResponseDto.success(null); // 성공 시 data는 null
    }

    /**
     * 사용자가 특정 프로젝트에 접근할 권한이 있는지 확인합니다.
     * user_project_mapping 테이블을 조회하여 확인합니다.
     *
     * @param userEmail 확인할 사용자의 이메일
     * @param projectId 접근하려는 프로젝트 ID
     * @throws CustomProjectException 사용자가 프로젝트에 접근 권한이 없는 경우 (ACCESS_DENIED)
     */
    @Transactional(readOnly = true) // 읽기 전용 트랜잭션
    public void checkUserAccessToProject(String userEmail, Long projectId) {
        if (projectId == null) {
            log.warn("checkUserAccessToProject called with null projectId for user: {}", userEmail);
            return;
        }

        String userRole = userMapper.selectUserRoleByEmail(userEmail)
                .orElseThrow(() -> new CustomProjectException(ErrorCode.USER_NOT_FOUND, "사용자를 찾을 수 없습니다: " + userEmail));
        if (!UserRole.SUPER_ADMIN.name().equals(userRole)) {
            int membershipCount = userMapper.countUserProjectMembership(userEmail, projectId);
            if (membershipCount == 0) {
                throw new CustomProjectException(ErrorCode.ACCESS_DENIED, "해당 프로젝트에 대한 접근 권한이 없습니다.");
            }
        }

        // 접근 권한 있음 (membershipCount > 0)
        log.debug("Access granted for user '{}' to project ID '{}'", userEmail, projectId);
    }

    private ProjectResponseDto convertToDto(Project project) {
        return ProjectResponseDto.builder()
                .projectId(project.getProjectId())
                .projectName(project.getProjectName())
                .description(project.getDescription())
                .projectAdminUserEmail(project.getProjectAdminUserEmail())
                .createDate(project.getCreateDate() != null ? project.getCreateDate().format(DATE_TIME_FORMATTER) : null)
                .createUserEmail(project.getCreateUserEmail())
                .lastUpdateDate(project.getLastUpdateDate() != null ? project.getLastUpdateDate().format(DATE_TIME_FORMATTER) : null)
                .updateUserEmail(project.getUpdateUserEmail())
                .status("Y".equals(project.getDeleteYn()) ? "INACTIVE" : "ACTIVE") // deleteYn -> status
                .build();
    }

    // 요청된 속성 이름을 실제 DB 컬럼 이름으로 매핑하는 헬퍼 메서드
    private String mapToDbColumn(String propertyName) {
        // DTO 필드명 또는 요청 파라미터 이름을 DB 컬럼명으로 변환
        switch (propertyName) {
            case "projectId": return "project_id";
            case "projectName": return "project_name";
            case "createDate": return "create_date";
            case "lastUpdateDate": return "last_update_date";
            // 필요에 따라 다른 필드 매핑 추가
            default: return propertyName; // 매핑되지 않으면 원래 이름 사용 (주의: ALLOWED_SORT_COLUMNS와 일치해야 함)
        }
    }

    // 사용자 존재 및 프로젝트 관리자 권한 확인 메서드
    private void validateProjectAdminRole(String adminEmail) {
        if (!StringUtils.hasText(adminEmail)) {
            throw new CustomProjectException(ErrorCode.INVALID_INPUT_VALUE, "프로젝트 관리자 이메일은 필수입니다.");
        }
        User adminUser = userMapper.selectUserByEmail(adminEmail)
                .orElseThrow(() -> new CustomProjectException(ErrorCode.USER_NOT_FOUND, "지정된 프로젝트 관리자를 찾을 수 없습니다: " + adminEmail));

        if (!adminUser.getRoleId().equals(PROJECT_ADMIN_ROLE_NAME)) {
            throw new CustomProjectException(ErrorCode.ACCESS_DENIED, "지정된 사용자는 프로젝트 관리자 권한이 없습니다: " + adminEmail);
        }
    }
}
