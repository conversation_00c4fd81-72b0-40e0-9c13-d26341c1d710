package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * QR 코드 스캔 기기 통계 DTO
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QrScanDeviceStatsDto {
    private Map<String, Long> deviceCounts; // 기기별 스캔 횟수
    private Map<String, Double> devicePercentage; // 기기별 스캔 비율(%)
    private Map<String, Long> browserCounts; // 브라우저별 스캔 횟수
    private Map<String, Double> browserPercentage; // 브라우저별 스캔 비율(%)
    private Map<String, Long> osCounts; // OS별 스캔 횟수
    private Map<String, Double> osPercentage; // OS별 스캔 비율(%)
}
