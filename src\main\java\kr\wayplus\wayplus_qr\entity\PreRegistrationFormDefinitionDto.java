package kr.wayplus.wayplus_qr.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreRegistrationFormDefinitionDto {
    private Long formId; // 폼 ID
    private String formName; // 폼 이름
    private List<FormFieldDefinitionDto> fieldDefinitions; // 폼 필드 정의 목록
    // 필요에 따라 추가 폼 레벨 정보
}
