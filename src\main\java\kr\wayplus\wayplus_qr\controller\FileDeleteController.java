package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.service.FileStorageService;
import kr.wayplus.wayplus_qr.service.InquiryService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/way/remove")
@Slf4j
public class FileDeleteController {

    @Autowired
    private FileStorageService fileStorageService;
    
    @Autowired
    private InquiryService inquiryService;
    
    /**
     * 문의사항 첨부파일 삭제 (ID 기준)
     * 해당 문의사항에 대한 권한이 있는 사용자만 삭제 가능
     * @param attachmentId 첨부파일 ID
     * @param userDetails 인증된 사용자 정보
     * @return 삭제 결과
     */
    @DeleteMapping("/inquiry-attachment/{attachmentId}")
    public ResponseEntity<?> deleteInquiryAttachment(
            @PathVariable("attachmentId") Long attachmentId,
            @AuthenticationPrincipal User userDetails) {
        
        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        
        try {
            // InquiryService를 통해 첨부파일 삭제
            inquiryService.deleteAttachment(attachmentId, userEmail);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "첨부파일이 성공적으로 삭제되었습니다.");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error deleting inquiry attachment: {}", e.getMessage(), e);
            
            Map<String, String> response = new HashMap<>();
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
    
    /**
     * 파일 삭제 (파일명 기준)
     * 관리자 권한을 가진 사용자만 삭제 가능
     * @param filename 파일명
     * @return 삭제 결과
     */
    @DeleteMapping("/{filename:.+}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteFile(
            @PathVariable("filename") String filename) {
        
        try {
            fileStorageService.deleteFile(filename);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "파일이 성공적으로 삭제되었습니다.");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error deleting file: {}", e.getMessage(), e);
            
            Map<String, String> response = new HashMap<>();
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
}
