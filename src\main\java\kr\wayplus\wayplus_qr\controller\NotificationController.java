package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.NotificationCountResponseDto;
import kr.wayplus.wayplus_qr.dto.response.NotificationResponseDto;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.service.NotificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/way/notifications")
@RequiredArgsConstructor
@Slf4j
public class NotificationController {

    private final NotificationService notificationService;

    /**
     * 사용자별 알림 목록 조회
     * @param pageable 페이징 정보
     * @param userDetails 인증된 사용자 정보
     * @return 알림 목록 (페이징)
     */
    @GetMapping("/list")
    public ResponseEntity<ApiResponseDto<ListResponseDto<NotificationResponseDto>>> getNotifications(
            @PageableDefault(size = 10, sort = "createdAt,desc") Pageable pageable,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        Page<NotificationResponseDto> notificationPage = notificationService.getNotifications(userEmail, pageable);
        ListResponseDto<NotificationResponseDto> responseDto = new ListResponseDto<>(notificationPage, null);

        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    /**
     * 읽지 않은 알림 개수 조회
     * @param userDetails 인증된 사용자 정보
     * @return 읽지 않은 알림 개수
     */
    @GetMapping("/noread-count")
    public ResponseEntity<ApiResponseDto<NotificationCountResponseDto>> getUnreadNotificationCount(
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        NotificationCountResponseDto count = notificationService.getUnreadNotificationCount(userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(count));
    }

    /**
     * 알림 읽음 처리
     * @param notificationId 알림 ID
     * @param userDetails 인증된 사용자 정보
     * @return 성공 응답 (데이터 없음)
     */
    @PutMapping("/{notificationId}/read")
    public ResponseEntity<ApiResponseDto<Void>> markNotificationAsRead(
            @PathVariable("notificationId") Long notificationId,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        notificationService.markNotificationAsRead(notificationId, userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(null));
    }

    /**
     * 모든 알림 읽음 처리
     * @param userDetails 인증된 사용자 정보
     * @return 처리된 알림 수
     */
    @PutMapping("/read-all")
    public ResponseEntity<ApiResponseDto<Integer>> markAllNotificationsAsRead(
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        int updatedCount = notificationService.markAllNotificationsAsRead(userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(updatedCount));
    }
}
