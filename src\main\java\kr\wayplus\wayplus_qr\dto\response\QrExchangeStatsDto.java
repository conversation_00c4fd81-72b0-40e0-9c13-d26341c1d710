package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * QR 코드 교환권 통계 DTO
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QrExchangeStatsDto {
    private Long totalExchangeQrCodes; // 총 교환권 QR 코드 수
    private Long totalExchangeCount; // 총 교환 가능 횟수
    private Long totalUsedCount; // 총 사용 횟수
    private Double usageRate; // 사용률(%)
    private List<String> dates; // 날짜 배열
    private List<Long> approvalCounts; // 일별 승인 횟수
    private Map<String, Long> approverCounts; // 승인자별 승인 횟수
    private Map<String, Double> approverPercentage; // 승인자별 비율(%)
    private Map<String, Long> hourlyApprovalCounts; // 시간대별 승인 횟수
}
