package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class TotalQrStatusDto {

    /**
     * 삭제되지 않은 총 QR 코드 개수
     */
    private Long totalQrCodes;

    /**
     * 모든 QR 코드의 총 스캔 횟수 합계
     */
    private Long totalScans;
    
    /**
     * 날짜별 QR 코드 생성 수
     */
    private List<DailyCountDto> dailyCreatedCounts;
    
    /**
     * 날짜별 QR 코드 스캔 수
     */
    private List<DailyCountDto> dailyScannedCounts;
}
