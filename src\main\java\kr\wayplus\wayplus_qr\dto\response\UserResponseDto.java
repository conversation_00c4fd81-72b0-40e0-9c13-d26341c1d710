package kr.wayplus.wayplus_qr.dto.response;

import kr.wayplus.wayplus_qr.entity.User;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@NoArgsConstructor
public class UserResponseDto {
    private Long userIdx;
    private String userEmail;
    private String name;
    private String roleId;
    private String status;
    private String description;         // 추가
    private LocalDateTime createDate;
    private LocalDateTime lastLoginDate; // 추가
    private LocalDateTime updateDate;
    private List<ProjectResponseDto> projects;      // 추가

    @Builder
    public UserResponseDto(Long userIdx, String userEmail, String name, String roleId, String status,
                           String description, LocalDateTime createDate, LocalDateTime lastLoginDate,
                           LocalDateTime updateDate, List<ProjectResponseDto> projects) {
        this.userIdx = userIdx;
        this.userEmail = userEmail;
        this.name = name;
        this.roleId = roleId;
        this.status = status;
        this.description = description;
        this.createDate = createDate;
        this.lastLoginDate = lastLoginDate;
        this.updateDate = updateDate;
        this.projects = projects;
    }

    public static UserResponseDto fromEntity(User user) {
        return UserResponseDto.builder()
                .userIdx(user.getUserIdx())
                .userEmail(user.getUserEmail())
                .name(user.getName())
                .roleId(user.getRoleId())
                .status(user.getStatus())
                .description(user.getDescription()) // 추가
                .createDate(user.getCreateDate())
                .lastLoginDate(user.getLastLoginDate()) // 추가
                .updateDate(user.getUpdateDate())
                .projects(user.getProjects())      // 추가
                .build();
    }
}
