package kr.wayplus.wayplus_qr.entity;

import lombok.AccessLevel;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Getter
@NoArgsConstructor(access = AccessLevel.PROTECTED) // MyBatis가 프록시 객체를 생성하기 위해 기본 생성자가 필요할 수 있음
public class RefreshToken {

    private Long tokenId; // DB의 token_id (BIGINT AUTO_INCREMENT PK)
    private String userEmail; // DB의 user_email (VARCHAR, FK to users.user_email)
    private String token; // DB의 token (VARCHAR, UNIQUE)
    private LocalDateTime expiryDate; // DB의 expiry_date (DATETIME)
    private LocalDateTime createDate; // DB의 create_date (DATETIME) - MyBatis가 자동 관리하지 않으므로 필요시 Mapper에서 처리
    private LocalDateTime lastUpdateDate; // DB의 last_update_date (DATETIME) - MyBatis가 자동 관리하지 않으므로 필요시 Mapper에서 처리

    @Builder
    public RefreshToken(Long tokenId, String userEmail, String token, LocalDateTime expiryDate) {
        this.tokenId = tokenId; // 업데이트 시 필요할 수 있음
        this.userEmail = userEmail;
        this.token = token;
        this.expiryDate = expiryDate;
    }

    // Refresh Token 업데이트 메서드 (토큰 값, 만료일만 변경 가정)
    public void updateToken(String token, LocalDateTime expiryDate) {
        this.token = token;
        this.expiryDate = expiryDate;
        // updateDate은 Mapper에서 CURRENT_TIMESTAMP 등으로 처리
    }
}
