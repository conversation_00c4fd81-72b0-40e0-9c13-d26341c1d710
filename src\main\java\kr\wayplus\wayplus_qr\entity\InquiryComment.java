package kr.wayplus.wayplus_qr.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 문의 댓글 Entity - inquiry_comments 테이블 매핑.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryComment {
    private Long commentId;
    private Long inquiryId;
    private String userEmail;
    private String commentContent;
    private LocalDateTime createdAt;
}
