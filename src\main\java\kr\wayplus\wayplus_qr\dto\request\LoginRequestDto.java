package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.NotBlank;
import kr.wayplus.wayplus_qr.util.validation.AdminOrEmail;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter // Controller에서 @RequestBody로 받기 위해 Setter 또는 생성자 필요
@NoArgsConstructor
@AllArgsConstructor
@Builder // Builder 어노테이션 추가
public class LoginRequestDto {

    @NotBlank(message = "이메일은 필수 입력값입니다.")
    @AdminOrEmail(message = "이메일 형식이 올바르지 않습니다.")
    private String userEmail;

    @NotBlank(message = "비밀번호는 필수 입력값입니다.")
    private String password;
}
