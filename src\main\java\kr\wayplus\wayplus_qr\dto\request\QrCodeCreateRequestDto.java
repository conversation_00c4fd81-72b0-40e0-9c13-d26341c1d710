package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import kr.wayplus.wayplus_qr.entity.QrCode;
import kr.wayplus.wayplus_qr.entity.QrCodeStatus;
import lombok.Data;
import lombok.Builder;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;

@Data
@Builder
public class QrCodeCreateRequestDto {

    @NotNull(message = "Project ID는 필수입니다.")
    private Long projectId;

    @NotBlank(message = "QR 코드 이름은 필수입니다.")
    @Size(max = 100, message = "QR 코드 이름은 100자를 초과할 수 없습니다.")
    private String qrName;

    @NotBlank(message = "QR 코드 타입은 필수입니다.")
    @Size(max = 50, message = "QR 코드 타입은 50자를 초과할 수 없습니다.")
    private String qrType;

    @NotBlank(message = "대상 콘텐츠는 필수입니다.")
    @Size(max = 2000, message = "대상 콘텐츠는 2000자를 초과할 수 없습니다.")
    private String targetContent;

    @Size(max = 500, message = "설명은 500자를 초과할 수 없습니다.")
    private String description;

    private LocalDateTime validFromDate;

    private LocalDateTime validToDate;

    private String installationLocation;

    private String installationLocationLat;

    private String installationLocationLng;

    private String qrInstalledImagePath;

    private String designOptions;

    private QrCodeStatus status;

    private String imagePath;
    private MultipartFile logoImageFile;
    private MultipartFile qrInstalledImageFile;
    private MultipartFile backgroundImageFile;

    public QrCode toEntity(String userEmail) {
        QrCode qrCode = new QrCode();
        qrCode.setProjectId(this.projectId);
        qrCode.setQrName(this.qrName);
        qrCode.setQrType(this.qrType);
        qrCode.setTargetContent(this.targetContent);
        qrCode.setDescription(this.description);
        qrCode.setValidFromDate(this.validFromDate);
        qrCode.setValidToDate(this.validToDate);
        qrCode.setInstallationLocation(this.installationLocation);
        qrCode.setInstallationLocationLat(this.installationLocationLat);
        qrCode.setInstallationLocationLng(this.installationLocationLng);
        qrCode.setQrInstalledImagePath(this.qrInstalledImagePath);
        // designOptions가 null이거나 비어 있으면 기본값 '{}' 설정
        qrCode.setDesignOptions(this.designOptions != null && !this.designOptions.isBlank() ? this.designOptions : "{}");
        qrCode.setStatus(this.status != null ? this.status : QrCodeStatus.ACTIVE);
        qrCode.setImagePath(this.imagePath);
        qrCode.setCreateUserEmail(userEmail);
        qrCode.setUseYn("Y");
        qrCode.setDeleteYn("N");
        return qrCode;
    }

    

}
