package kr.wayplus.wayplus_qr.exception;

import lombok.Getter;

@Getter
public class CustomAttendeeException extends RuntimeException {
    private final ErrorCode errorCode;

    public CustomAttendeeException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public CustomAttendeeException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}
