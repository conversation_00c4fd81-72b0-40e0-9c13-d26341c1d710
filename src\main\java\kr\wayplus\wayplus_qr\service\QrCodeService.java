package kr.wayplus.wayplus_qr.service;

import java.util.Map;
import java.util.HashMap;

import com.aventrix.jnanoid.jnanoid.NanoIdUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import javax.imageio.ImageIO;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import jakarta.servlet.http.HttpServletRequest;

import kr.wayplus.wayplus_qr.dto.request.QrCodeCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.QrCodeUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.QrCodeDesignOptionsDto;
import kr.wayplus.wayplus_qr.dto.response.QrCodeResponseDto;
import kr.wayplus.wayplus_qr.entity.QrCode;
import kr.wayplus.wayplus_qr.entity.QrCodeStatus;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.entity.enums.UserRole;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import kr.wayplus.wayplus_qr.external.quizServerConnecter;
import kr.wayplus.wayplus_qr.mapper.QrCodeMapper;
import kr.wayplus.wayplus_qr.mapper.QrScanLogMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import kr.wayplus.wayplus_qr.util.A4CanvasUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import static kr.wayplus.wayplus_qr.entity.enums.UserRole.PROJECT_ADMIN;
import static kr.wayplus.wayplus_qr.entity.enums.UserRole.SUB_ADMIN;
import static kr.wayplus.wayplus_qr.entity.enums.UserRole.SUPER_ADMIN;

@Service
@Slf4j
@RequiredArgsConstructor
public class QrCodeService {

    @Value("${upload.qr-file.path}")
    private String qrCodeImagePath;

    private final QrCodeMapper qrCodeMapper;
    private final QrCodeGeneratorService qrCodeGeneratorService;
    private final QrImageComposerService qrImageComposerService;
    private final UserMapper userMapper;
    private final ObjectMapper objectMapper;
    private final QrScanLogService qrScanLogService;
    private final QrScanLogMapper qrScanLogMapper;
    private final quizServerConnecter thymeleafClient;

    private final int QR_CODE_WIDTH = 300;
    private final int QR_CODE_HEIGHT = 300;
    private final String DEFAULT_DESIGN_OPTIONS_JSON = "{}";
    private final String DEFAULT_FOREGROUND_COLOR = "#000000";
    private final String DEFAULT_BACKGROUND_COLOR = "#FFFFFF";
    private final String DEFAULT_EYE_COLOR = "#000000";
    private final String DEFAULT_EYE_TYPE = "square";
    private final double DEFAULT_LOGO_RATIO = 0.2;
    private final String DEFAULT_ERROR_CORRECTION_LEVEL = "L"; // 기본 오류 복원 수준

    

    public QrCodeResponseDto getQrCodeById(Long qrCodeId) {
        QrCode qrCode = qrCodeMapper.selectQrCodeById(qrCodeId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND, "QR 코드를 찾을 수 없습니다: " + qrCodeId));
        return QrCodeResponseDto.fromEntity(qrCode);
    }

    @Transactional(readOnly = true)
    public Page<QrCodeResponseDto> getQrCodesByProjectId(
            Long projectId,
            String status,
            String searchType,
            String searchKeyword,
            Pageable pageable
    ) {
        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "qrCodeName": searchColumn = "qr_name"; break;
                case "qrType": searchColumn = "qr_type"; break;
                case "targetContent": searchColumn = "target_content"; break;
                case "status": searchColumn = "status"; break;
                case "validFromDate": searchColumn = "valid_from_date"; break;
                case "validToDate": searchColumn = "valid_to_date"; break;
            }
        }
        List<QrCode> qrCodes = qrCodeMapper.selectQrCodesByProjectIdWithPaging(
                projectId, status, searchColumn, searchKeyword, pageable
        );
        long total = qrCodeMapper.countQrCodesByProjectId(
                projectId, status, searchColumn, searchKeyword
        );

        List<QrCodeResponseDto> dtos = qrCodes.stream()
                .map(QrCodeResponseDto::fromEntity)
                .collect(Collectors.toList());

        return new PageImpl<>(dtos, pageable, total);
    }

    public QrCodeResponseDto getQrCodeForRedirect(Long qrCodeId) {
        QrCode qrCode = qrCodeMapper.selectQrCodeById(qrCodeId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND, "QR 코드를 찾을 수 없습니다: " + qrCodeId));

        if (qrCode.getStatus() != QrCodeStatus.ACTIVE) {
            log.warn("Attempted to redirect non-active QR code (ID: {}, Status: {})", qrCodeId, qrCode.getStatus());
            throw new QRcodeException(ErrorCode.QR_CODE_NOT_ACTIVE, "QR 코드가 활성 상태가 아닙니다.");
        }

        LocalDateTime now = LocalDateTime.now();
        if ((qrCode.getValidFromDate() != null && now.isBefore(qrCode.getValidFromDate())) ||
            (qrCode.getValidToDate() != null && now.isAfter(qrCode.getValidToDate()))) {
            log.warn("Attempted to redirect expired or not yet valid QR code (ID: {}, ValidFrom: {}, ValidTo: {})",
                     qrCodeId, qrCode.getValidFromDate(), qrCode.getValidToDate());
            throw new QRcodeException(ErrorCode.QR_CODE_EXPIRED, "QR 코드의 유효 기간이 아닙니다.");
        }

        return QrCodeResponseDto.fromEntity(qrCode);
    }

    public QrCodeResponseDto createQrCode(QrCodeCreateRequestDto requestDto, String userEmail) {
        log.info("Creating new QR code for project ID: {} by user: {}", requestDto.getProjectId(), userEmail);

        if (!checkUserProjectRoles(requestDto.getProjectId(), userEmail, SUPER_ADMIN, PROJECT_ADMIN)) {
            log.warn("Permission denied for user {} to create QR code in project ID {}.", userEmail, requestDto.getProjectId());
            throw new QRcodeException(ErrorCode.ACCESS_DENIED, "프로젝트에 QR 코드를 생성할 권한이 없습니다.");
        }

        if (qrCodeMapper.existsByProjectIdAndQrName(requestDto.getProjectId(), requestDto.getQrName())) {
            log.warn("Attempted to create QR code with duplicate name '{}' in project ID {}.", requestDto.getQrName(), requestDto.getProjectId());
            throw new QRcodeException(ErrorCode.DUPLICATE_QR_NAME);
        }

        QrCode qrCode = requestDto.toEntity(userEmail);
        qrCode.setQrUuid(NanoIdUtils.randomNanoId());

        int insertedCount = qrCodeMapper.insertQrCode(qrCode);
        if (insertedCount == 0 || qrCode.getQrCodeId() == null) {
            log.error("Failed to insert QR code data or retrieve generated ID for project ID: {}", requestDto.getProjectId());
            throw new QRcodeException(ErrorCode.QR_CODE_SAVE_FAILED, "QR 코드 데이터 저장 또는 ID 생성에 실패했습니다.");
        }
        log.info("Successfully inserted initial QR code data with ID: {}", qrCode.getQrCodeId());

        InputStream logoInputStream = null;
        Path logoDestinationPath = null;
        String relativeLogoPath = null;
        Path installedImageDestinationPath = null;
        String relativeInstalledImagePath = null;

        // 변수 초기화
        String relativeSvgPath = null;
        String relativePngPath = null;
        String fullPngPathString = null;
        boolean isPngGenerated = false;
        String a4CanvasImagePath = null;
        boolean hasA4CanvasImage = false;
        QrCodeDesignOptionsDto designOptions = null;
        
        // 경로 설정
        Path qrCodeDir = Paths.get(qrCodeImagePath);
        Path logoDir = Paths.get(qrCodeImagePath, "logos");
        Path installedImageDir = Paths.get(qrCodeImagePath, "installedLocation");
        Path backgroundImagesDir = Paths.get(qrCodeImagePath, "backgrounds");
        
        // 파일 추출
        MultipartFile uploadedLogoFile = requestDto.getLogoImageFile();
        MultipartFile uploadedInstalledImageFile = requestDto.getQrInstalledImageFile();
        MultipartFile uploadedBackgroundImageFile = requestDto.getBackgroundImageFile();

        try {
            try {
                Files.createDirectories(qrCodeDir);
            } catch (IOException e) {
                log.error("Failed to create QR code directory: {}", qrCodeDir, e);
                throw e;
            }
            try {
                Files.createDirectories(logoDir);
            } catch (IOException e) {
                log.error("Failed to create logo directory: {}", logoDir, e);
                throw e;
            }
            try {
                Files.createDirectories(installedImageDir);
            } catch (IOException e) {
                log.error("Failed to create installed image directory: {}", installedImageDir, e);
                throw e;
            }
            try {
                Files.createDirectories(backgroundImagesDir);
            } catch (IOException e) {
                log.error("Failed to create background images directory: {}", backgroundImagesDir, e);
                throw e;
            }

            // 배경 이미지 파일 처리
            Path backgroundImageDestinationPath = null;
            String relativeBackgroundImagePath = null;
            
            if (uploadedBackgroundImageFile != null && !uploadedBackgroundImageFile.isEmpty()) {
                log.info("User uploaded a background image file for QR code ID: {}", qrCode.getQrCodeId());
                String originalFilename = uploadedBackgroundImageFile.getOriginalFilename();
                String extension = "";
                if (originalFilename != null && originalFilename.contains(".")) {
                    extension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
                }
                String backgroundImageFileName = "bg_" + qrCode.getQrCodeId() + extension;
                backgroundImageDestinationPath = backgroundImagesDir.resolve(backgroundImageFileName);
                relativeBackgroundImagePath = "/backgrounds/" + backgroundImageFileName;
                
                try {
                    uploadedBackgroundImageFile.transferTo(backgroundImageDestinationPath);
                } catch (IOException e) {
                    log.error("Failed to save uploaded background image file to: {}", backgroundImageDestinationPath, e);
                    throw new QRcodeException(ErrorCode.QR_CODE_FILE_SAVE_FAILED, "배경 이미지 파일 저장 실패", e);
                }
                log.info("Uploaded background image file saved to: {}", backgroundImageDestinationPath);
            } else {
                log.info("No background image file uploaded.");
            }
            
            // 로고 이미지 파일 처리
            if (uploadedLogoFile != null && !uploadedLogoFile.isEmpty()) {
                log.info("User uploaded a logo file for QR code ID: {}", qrCode.getQrCodeId());

                String originalFilename = uploadedLogoFile.getOriginalFilename();
                String extension = "";
                if (originalFilename != null && originalFilename.contains(".")) {
                    extension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
                }
                String logoFileName = qrCode.getQrCodeId() + extension;
                logoDestinationPath = logoDir.resolve(logoFileName);
                relativeLogoPath = "/logos/" + logoFileName;

                try {
                    uploadedLogoFile.transferTo(logoDestinationPath);
                } catch (IOException e) {
                    log.error("Failed to save uploaded logo file to: {}", logoDestinationPath, e);
                    throw new QRcodeException(ErrorCode.QR_CODE_FILE_SAVE_FAILED, "로고 파일 저장 실패", e);
                }
                log.info("Uploaded logo file saved to: {}", logoDestinationPath);

                try {
                    logoInputStream = Files.newInputStream(logoDestinationPath);
                } catch (IOException e) {
                    log.error("Failed to read logo file: {}", logoDestinationPath, e);
                    throw e;
                }
            } else {
                log.info("No logo file uploaded.");
            }
            
            // 설치위치사진 처리
            log.info("Checking qrInstalledImageFile in Service: {}", 
                   uploadedInstalledImageFile != null ? "File exists" : "NULL");
            
            if (uploadedInstalledImageFile != null) {
                log.info("qrInstalledImageFile details: isEmpty={}, name={}, size={}, contentType={}", 
                       uploadedInstalledImageFile.isEmpty(),
                       uploadedInstalledImageFile.getOriginalFilename(),
                       uploadedInstalledImageFile.getSize(),
                       uploadedInstalledImageFile.getContentType());
            }
            
            if (uploadedInstalledImageFile != null && !uploadedInstalledImageFile.isEmpty()) {
                log.info("User uploaded an installed location image file for QR code ID: {}", qrCode.getQrCodeId());

                String originalFilename = uploadedInstalledImageFile.getOriginalFilename();
                String extension = "";
                if (originalFilename != null && originalFilename.contains(".")) {
                    extension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
                }
                String installedImageFileName = "installed_" + qrCode.getQrCodeId() + extension;
                installedImageDestinationPath = installedImageDir.resolve(installedImageFileName);
                relativeInstalledImagePath = "/installedLocation/" + installedImageFileName;

                try {
                    uploadedInstalledImageFile.transferTo(installedImageDestinationPath);
                } catch (IOException e) {
                    log.error("Failed to save uploaded installed location image file to: {}", installedImageDestinationPath, e);
                    throw new QRcodeException(ErrorCode.QR_CODE_FILE_SAVE_FAILED, "설치위치사진 파일 저장 실패", e);
                }
                log.info("Uploaded installed location image file saved to: {}", installedImageDestinationPath);
            } else {
                log.info("No installed location image file uploaded.");
            }

            // QR 코드 콘텐츠 결정
            String contentToEncode;
            if ("WIFI".equalsIgnoreCase(qrCode.getQrType())) {
                contentToEncode = qrCode.getTargetContent(); // WIFI 타입인 경우 직접 내용 사용
                log.info("Encoding direct WIFI content for QR code ID {}: {}", qrCode.getQrCodeId(), contentToEncode);
            } else {
                // 다른 타입의 경우 리디렉션 URL 인코딩
                contentToEncode = ServletUriComponentsBuilder.fromCurrentContextPath()
                        .path("/api/way/qr/redirect/{qrUuid}")
                        .buildAndExpand(qrCode.getQrUuid())
                        .toUriString();
                log.info("Encoding redirect URL for QR code ID {}: {}", qrCode.getQrCodeId(), contentToEncode);
            }
            
            // 디자인 옵션 파싱
            designOptions = parseDesignOptions(requestDto.getDesignOptions());
            
            // A4 캔버스 이미지 처리 확인
            boolean useA4Canvas = designOptions.getUseA4Canvas() != null && designOptions.getUseA4Canvas();
            String a4CanvasImage = designOptions.getA4CanvasImage();
            hasA4CanvasImage = useA4Canvas && a4CanvasImage != null && !a4CanvasImage.trim().isEmpty();
            
            log.info("A4 Canvas processing - useA4Canvas: {}, hasA4CanvasImage: {}", useA4Canvas, hasA4CanvasImage);
            
            // A4 캔버스 이미지가 있는 경우 별도 파일로 저장
            if (hasA4CanvasImage) {
                try {
                    // Base64 데이터에서 헤더 제거 (data:image/png;base64, 부분)
                    String base64Data = a4CanvasImage;
                    if (base64Data.contains(",")) {
                        base64Data = base64Data.substring(base64Data.indexOf(",") + 1);
                    }
                    
                    // Base64 디코딩
                    byte[] imageBytes = Base64.getDecoder().decode(base64Data);
                    
                    // 원본 이미지 로드
                    BufferedImage originalImage = ImageIO.read(new ByteArrayInputStream(imageBytes));
                    log.info("Original A4 canvas image size: {}x{}", originalImage.getWidth(), originalImage.getHeight());
                    
                    // a4Size 정보가 있는 경우 해당 크기로 리사이징
                     BufferedImage finalImage = originalImage;
                     if (designOptions.getA4Size() != null) {
                         double widthPx = designOptions.getA4Size().getWidth();
                         double heightPx = designOptions.getA4Size().getHeight();
                         
                         if (widthPx > 0 && heightPx > 0) {
                             // 프론트엔드에서 전송된 px 값을 cm로 변환 후 다시 300 DPI 기준 픽셀로 변환
                             double widthCm = A4CanvasUtils.pixelsToMm(widthPx) / 10.0; // mm를 cm로 변환
                             double heightCm = A4CanvasUtils.pixelsToMm(heightPx) / 10.0; // mm를 cm로 변환
                             
                             int[] targetSize = A4CanvasUtils.cmSizeToPixels(widthCm, heightCm);
                             int targetWidth = targetSize[0];
                             int targetHeight = targetSize[1];
                             
                             log.info("Resizing A4 canvas image from {}x{} to {}x{} (frontend: {}x{} px -> {}x{} cm)", 
                                     originalImage.getWidth(), originalImage.getHeight(), 
                                     targetWidth, targetHeight, widthPx, heightPx, widthCm, heightCm);
                            
                            // 이미지 리사이징
                            finalImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_ARGB);
                            Graphics2D g2d = finalImage.createGraphics();
                            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                            g2d.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
                            g2d.dispose();
                        }
                    }
                    
                    // A4 캔버스 이미지를 별도 파일로 저장
                    String a4CanvasFileName = qrCode.getQrCodeId() + ".png";
                    Path a4CanvasFilePath = qrCodeDir.resolve(a4CanvasFileName);
                    a4CanvasImagePath = "/qrcode/" + a4CanvasFileName;
                    
                    // BufferedImage를 byte array로 변환 후 파일 저장
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    ImageIO.write(finalImage, "PNG", baos);
                    Files.write(a4CanvasFilePath, baos.toByteArray());
                    
                    log.info("Successfully saved A4 canvas image (final size: {}x{}) at: {}", 
                            finalImage.getWidth(), finalImage.getHeight(), a4CanvasFilePath);
                    
                    // design_options의 a4CanvasImage 필드에 파일 경로 저장 (Base64 데이터 대신)
                    designOptions.setA4CanvasImage(a4CanvasImagePath);
                    
                    // QR 코드 이미지는 A4 캔버스 이미지로 대체되므로 별도 생성하지 않음
                    relativePngPath = a4CanvasFileName;
                    fullPngPathString = a4CanvasFilePath.toString();
                    isPngGenerated = true;
                    
                } catch (Exception e) {
                    log.error("Failed to process A4 canvas image from frontend: {}", e.getMessage(), e);
                    // A4 캔버스 이미지 처리 실패 시 일반 QR 코드 생성으로 진행
                    hasA4CanvasImage = false;
                }
            }
            
            // 이미지 생성 분기 처리 (A4 캔버스 이미지가 없는 경우에만)
            
            // 배경 이미지가 있는 경우 PNG 생성 시도 (A4 캔버스 이미지가 없는 경우에만)
            if (!hasA4CanvasImage && backgroundImageDestinationPath != null) {
                try {
                    String pngFileName = qrCode.getQrCodeId() + ".png";
                    relativePngPath = pngFileName;
                    fullPngPathString = qrCodeDir.resolve(pngFileName).toString();
                    
                    qrImageComposerService.generateQrWithBackgroundAsPng(
                            contentToEncode,
                            designOptions,
                            backgroundImageDestinationPath.toString(),
                            logoDestinationPath != null ? logoDestinationPath.toString() : null,
                            fullPngPathString
                    );
                    
                    // PNG 생성 성공
                    isPngGenerated = true;
                    log.info("Successfully generated PNG QR code with background at: {}", fullPngPathString);
                    
                    // 임시 배경 이미지 파일 삭제
                    try {
                        Files.deleteIfExists(backgroundImageDestinationPath);
                        log.info("Successfully deleted temporary background image file: {}", backgroundImageDestinationPath);
                    } catch (IOException e) {
                        log.warn("Failed to delete temporary background image file: {}. Error: {}", backgroundImageDestinationPath, e.getMessage());
                    }
                } catch (Exception e) {
                    // PNG 생성 실패 시 로그 기록 후 SVG 생성으로 진행
                    log.error("Failed to generate PNG QR code with background: {}", e.getMessage(), e);
                    isPngGenerated = false;
                }
            }
            
            // PNG가 생성되지 않은 경우 SVG 생성 (A4 캔버스 이미지가 없는 경우에만)
            if (!hasA4CanvasImage && !isPngGenerated) {
                String svgFileName = qrCode.getQrCodeId() + ".svg";
                relativeSvgPath = svgFileName;
                String fullSvgPathString = qrCodeDir.resolve(svgFileName).toString();

                // Extract necessary design parameters, falling back to defaults
                String foregroundColor = (designOptions != null && designOptions.getDotsOptions() != null && designOptions.getDotsOptions().getColor() != null)
                        ? designOptions.getDotsOptions().getColor() : DEFAULT_FOREGROUND_COLOR;
                String backgroundColor = (designOptions != null && designOptions.getBackgroundOptions() != null && designOptions.getBackgroundOptions().getColor() != null)
                        ? designOptions.getBackgroundOptions().getColor() : DEFAULT_BACKGROUND_COLOR;
                
                QrCodeDesignOptionsDto.CornersSquareOptions cornersSquareOpts = null;
                if (designOptions != null && designOptions.getDotsOptions() != null) {
                    cornersSquareOpts = designOptions.getDotsOptions().getCornersSquareOptions();
                }

                String finderColor = (cornersSquareOpts != null && cornersSquareOpts.getColor() != null && !cornersSquareOpts.getColor().isEmpty())
                        ? cornersSquareOpts.getColor() : DEFAULT_EYE_COLOR;
                String finderType = (cornersSquareOpts != null && cornersSquareOpts.getType() != null && !cornersSquareOpts.getType().isEmpty())
                        ? cornersSquareOpts.getType() : DEFAULT_EYE_TYPE;

                double logoRatio = (designOptions != null && designOptions.getLogoRatio() != null)
                        ? designOptions.getLogoRatio() : DEFAULT_LOGO_RATIO;
                String errorCorrectionLevelStr = (designOptions != null && designOptions.getErrorCorrectionLevel() != null)
                        ? designOptions.getErrorCorrectionLevel().toUpperCase() : DEFAULT_ERROR_CORRECTION_LEVEL;
                ErrorCorrectionLevel errorCorrectionLevel = parseErrorCorrectionLevel(errorCorrectionLevelStr);

                // Generate QR code using the determined content
                qrCodeGeneratorService.generateQrCodeSvgFile(
                        contentToEncode,
                        QR_CODE_WIDTH,
                        QR_CODE_HEIGHT,
                        fullSvgPathString,
                        foregroundColor,
                        backgroundColor,
                        finderColor,
                        finderType,
                        logoDestinationPath != null ? logoDestinationPath.toString() : null,
                        logoRatio,
                        errorCorrectionLevel
                );
                log.info("Successfully generated SVG QR code at: {}", fullSvgPathString);
            }
            if (logoDestinationPath != null) {
                try {
                    Files.deleteIfExists(logoDestinationPath);
                    log.info("Successfully deleted temporary logo file: {}", logoDestinationPath);
                } catch (IOException e) {
                    log.warn("Failed to delete temporary logo file: {}. Error: {}", logoDestinationPath, e.getMessage());
                }
            }

        } catch (IOException e) {
            log.error("IOException during file processing for QR ID {}: {}", qrCode.getQrCodeId(), e.getMessage(), e);
            throw new QRcodeException(ErrorCode.QR_CODE_FILE_SAVE_FAILED, "QR 코드 파일 처리 중 오류 발생: " + e.getMessage());
        } catch (QRcodeException e) {
            log.error("QRcodeException during SVG generation for QR ID {}: {}", qrCode.getQrCodeId(), e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("Unexpected error during file processing/SVG generation for QR ID {}: {}", qrCode.getQrCodeId(), e.getMessage(), e);
            throw new QRcodeException(ErrorCode.QR_CODE_GENERATION_FAILED, "QR 코드 파일 처리 또는 SVG 생성 중 예기치 않은 오류 발생");
        } finally {
            if (logoInputStream != null) {
                try {
                    logoInputStream.close();
                } catch (IOException e) {
                    log.error("Failed to close logo InputStream for QR ID {}: {}", qrCode.getQrCodeId(), e.getMessage(), e);
                }
            }
        }

        // DB 업데이트 - 이미지 경로 저장
        if (isPngGenerated && relativePngPath != null) {
            qrCodeMapper.updateQrCodePaths(qrCode.getQrCodeId(), relativePngPath, relativeLogoPath, relativeInstalledImagePath);
            log.info("Successfully updated paths for QR code ID {}: pngPath={}, logoPath={}, installedImagePath={}", 
                    qrCode.getQrCodeId(), relativePngPath, relativeLogoPath, relativeInstalledImagePath);
            qrCode.setImagePath(relativePngPath);
            qrCode.setQrInstalledImagePath(relativeInstalledImagePath);
        } else if (relativeSvgPath != null) {
            qrCodeMapper.updateQrCodePaths(qrCode.getQrCodeId(), relativeSvgPath, relativeLogoPath, relativeInstalledImagePath);
            log.info("Successfully updated paths for QR code ID {}: svgPath={}, logoPath={}, installedImagePath={}", 
                    qrCode.getQrCodeId(), relativeSvgPath, relativeLogoPath, relativeInstalledImagePath);
            qrCode.setImagePath(relativeSvgPath);
            qrCode.setQrInstalledImagePath(relativeInstalledImagePath);
        }
        
        // A4 캔버스 이미지 처리 후 design_options 업데이트
        if (hasA4CanvasImage && a4CanvasImagePath != null) {
            try {
                // 수정된 design_options를 JSON으로 변환하여 데이터베이스에 저장
                ObjectMapper objectMapper = new ObjectMapper();
                String updatedDesignOptionsJson = objectMapper.writeValueAsString(designOptions);
                
                // QrCode 엔티티의 design_options 필드 업데이트
                qrCode.setDesignOptions(updatedDesignOptionsJson);
                
                // 데이터베이스에 design_options 업데이트
                QrCode designOptionsUpdate = new QrCode();
                designOptionsUpdate.setQrCodeId(qrCode.getQrCodeId());
                designOptionsUpdate.setDesignOptions(updatedDesignOptionsJson);
                designOptionsUpdate.setUpdateUser(userEmail);
                designOptionsUpdate.setUpdateDate(LocalDateTime.now());
                
                qrCodeMapper.updateQrCode(designOptionsUpdate);
                log.info("Successfully updated design_options with A4 canvas image path for QR code ID: {}", qrCode.getQrCodeId());
                
            } catch (Exception e) {
                log.error("Failed to update design_options with A4 canvas image path for QR code ID {}: {}", qrCode.getQrCodeId(), e.getMessage(), e);
            }
        }

        return QrCodeResponseDto.fromEntity(qrCode);
    }

    public QrCodeResponseDto updateQrCode(Long qrCodeId, QrCodeUpdateRequestDto requestDto, String userEmail) throws IllegalStateException, IOException {
        log.info("Updating QR code ID: {} by user: {}", qrCodeId, userEmail);

        QrCode existingQrCode = qrCodeMapper.selectQrCodeById(qrCodeId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND));

        if (!checkUserProjectRoles(existingQrCode.getProjectId(), userEmail, SUPER_ADMIN, PROJECT_ADMIN)) {
            log.warn("Permission denied for user {} to update QR code ID {} in project ID {}.", userEmail, qrCodeId, existingQrCode.getProjectId());
            throw new QRcodeException(ErrorCode.ACCESS_DENIED, "이 QR 코드를 수정할 권한이 없습니다.");
        }

        QrCode qrCodeUpdates = new QrCode();
        qrCodeUpdates.setQrCodeId(qrCodeId);

        updateFieldIfNotNull(requestDto.getQrName(), qrCodeUpdates::setQrName);
        updateFieldIfNotNull(requestDto.getTargetContent(), qrCodeUpdates::setTargetContent);
        updateFieldIfNotNull(requestDto.getQrType(), qrCodeUpdates::setQrType);
        if (requestDto.getStatus() != null) {
            qrCodeUpdates.setStatus(requestDto.getStatus());
            existingQrCode.setStatus(requestDto.getStatus());
        }
        updateFieldIfNotNull(requestDto.getDescription(), qrCodeUpdates::setDescription);
        updateFieldIfNotNull(requestDto.getLinkedLandingPageId(), qrCodeUpdates::setLinkedLandingPageId);
        updateFieldIfNotNull(requestDto.getLinkedEventId(), qrCodeUpdates::setLinkedEventId);
        updateFieldIfNotNull(requestDto.getDesignOptions(), qrCodeUpdates::setDesignOptions);
        updateFieldIfNotNull(requestDto.getProjectId(), qrCodeUpdates::setProjectId);
        updateDateFieldIfValid(requestDto.getValidFromDate(), qrCodeUpdates::setValidFromDate);
        updateDateFieldIfValid(requestDto.getValidToDate(), qrCodeUpdates::setValidToDate);
        if (requestDto.getInstallationLocation() != null) {
            qrCodeUpdates.setInstallationLocation(requestDto.getInstallationLocation());
            existingQrCode.setInstallationLocation(requestDto.getInstallationLocation());
        }
        if (requestDto.getInstallationLocationLat() != null) {
            try {
                BigDecimal lat = new BigDecimal(requestDto.getInstallationLocationLat())
                        .setScale(8, RoundingMode.HALF_UP);
                String latStr = lat.toPlainString();
                qrCodeUpdates.setInstallationLocationLat(latStr);
                existingQrCode.setInstallationLocationLat(latStr);
            } catch (NumberFormatException e) {
                log.warn("Invalid installationLocationLat format: {}", requestDto.getInstallationLocationLat());
            }
        }
        if (requestDto.getInstallationLocationLng() != null) {
            try {
                BigDecimal lng = new BigDecimal(requestDto.getInstallationLocationLng())
                        .setScale(8, RoundingMode.HALF_UP);
                String lngStr = lng.toPlainString();
                qrCodeUpdates.setInstallationLocationLng(lngStr);
                existingQrCode.setInstallationLocationLng(lngStr);
            } catch (NumberFormatException e) {
                log.warn("Invalid installationLocationLng format: {}", requestDto.getInstallationLocationLng());
            }
        }

        qrCodeUpdates.setUpdateUser(userEmail);
        qrCodeUpdates.setUpdateDate(LocalDateTime.now());

        // 설치위치사진 처리
        MultipartFile uploadedInstalledImageFile = requestDto.getQrInstalledImageFile();
        
        log.info("[UPDATE] Checking qrInstalledImageFile in Service: {}", 
               uploadedInstalledImageFile != null ? "File exists" : "NULL");
        
        if (uploadedInstalledImageFile != null) {
            log.info("[UPDATE] qrInstalledImageFile details: isEmpty={}, name={}, size={}, contentType={}", 
                   uploadedInstalledImageFile.isEmpty(),
                   uploadedInstalledImageFile.getOriginalFilename(),
                   uploadedInstalledImageFile.getSize(),
                   uploadedInstalledImageFile.getContentType());
        }
        
        if (uploadedInstalledImageFile == null) {
            // 설치위치사진이 null이면 기존 이미지 삭제 처리
            log.info("No installed location image file provided for QR code ID: {}, removing existing image if any", qrCodeId);
            
            // 기존 이미지 파일 삭제
            if (existingQrCode.getQrInstalledImagePath() != null && !existingQrCode.getQrInstalledImagePath().isBlank()) {
                try {
                    String existingPath = existingQrCode.getQrInstalledImagePath();
                    log.info("Existing installed image path: {}", existingPath);
                    
                    // 파일 경로가 정확한 형식이면 파일 삭제 시도
                    if (existingPath.startsWith("/installedLocation/")) {
                        String fileName = existingPath.substring("/installedLocation/".length());
                        Path fullPath = Paths.get(qrCodeImagePath, "installedLocation", fileName);
                        
                        if (Files.exists(fullPath)) {
                            Files.delete(fullPath);
                            log.info("Successfully deleted existing installed image file: {}", fullPath);
                        } else {
                            log.warn("Existing installed image file not found: {}", fullPath);
                        }
                    }
                    
                    // DB에서 경로 정보 삭제 (널로 설정)
                    qrCodeMapper.updateQrCodePaths(qrCodeId, 
                        existingQrCode.getImagePath(), // 기존 SVG 경로 유지
                        null, // 로고 경로는 기존값 유지
                        null); // 설치위치사진 경로 널로 설정
                    
                    // 엔티티에도 널 처리 (responseDto 생성용)
                    existingQrCode.setQrInstalledImagePath(null);
                    
                } catch (IOException e) {
                    log.error("Error deleting existing installed image file for QR code ID: {}", qrCodeId, e);
                    // 파일 삭제 오류는 처리 계속 진행
                }
            } else {
                log.info("No existing installed image file to delete for QR code ID: {}", qrCodeId);
            }
        } else if (!uploadedInstalledImageFile.isEmpty()) {
            log.info("User uploaded an installed location image file for QR code ID: {}", qrCodeId);
            
            try {
                Path installedImageDir = Paths.get(qrCodeImagePath, "installedLocation");
                Files.createDirectories(installedImageDir);
                
                // 기존 이미지가 있다면 삭제
                if (existingQrCode.getQrInstalledImagePath() != null && !existingQrCode.getQrInstalledImagePath().isBlank()) {
                    try {
                        String existingPath = existingQrCode.getQrInstalledImagePath();
                        if (existingPath.startsWith("/installedLocation/")) {
                            String fileName = existingPath.substring("/installedLocation/".length());
                            Path fullPath = Paths.get(qrCodeImagePath, "installedLocation", fileName);
                            
                            if (Files.exists(fullPath)) {
                                Files.delete(fullPath);
                                log.info("Successfully deleted existing installed image file before replacing: {}", fullPath);
                            }
                        }
                    } catch (IOException e) {
                        log.warn("Failed to delete existing installed image file: {}", e.getMessage());
                        // 계속 진행
                    }
                }
                
                String originalFilename = uploadedInstalledImageFile.getOriginalFilename();
                String extension = "";
                if (originalFilename != null && originalFilename.contains(".")) {
                    extension = originalFilename.substring(originalFilename.lastIndexOf(".")).toLowerCase();
                }
                String installedImageFileName = "installed_" + qrCodeId + extension;
                Path installedImageDestinationPath = installedImageDir.resolve(installedImageFileName);
                String relativeInstalledImagePath = "/installedLocation/" + installedImageFileName;
                
                uploadedInstalledImageFile.transferTo(installedImageDestinationPath);
                log.info("Uploaded installed location image file saved to: {}", installedImageDestinationPath);
                
                // 설치위치사진 경로 업데이트
                qrCodeMapper.updateQrCodePaths(qrCodeId, 
                    existingQrCode.getImagePath(), // 기존 SVG 경로 유지
                    null, // 로고 경로는 기존값 유지 (updateQrCode 메서드에서 별도 처리 가능)
                    relativeInstalledImagePath); // 새로운 설치위치사진 경로 설정
                
                // 엔티티에도 경로 업데이트 (responseDto 생성용)
                existingQrCode.setQrInstalledImagePath(relativeInstalledImagePath);
                
            } catch (IOException e) {
                log.error("Failed to save uploaded installed location image file for QR code ID: {}", qrCodeId, e);
                throw new QRcodeException(ErrorCode.QR_CODE_FILE_SAVE_FAILED, "설치위치사진 파일 저장 실패", e);
            }
        } else {
            log.info("Empty installed location image file provided for QR code ID: {}, ignoring", qrCodeId);
        }
        
        int updatedRows = qrCodeMapper.updateQrCode(qrCodeUpdates);
        if (updatedRows == 0) {
            log.warn("Update failed for QR code ID: {}. It might not exist or is already deleted.", qrCodeId);
            throw new QRcodeException(ErrorCode.QR_CODE_UPDATE_FAILED, "QR 코드 업데이트 실패: " + qrCodeId);
        }
        log.info("Successfully updated QR code ID: {}", qrCodeId);

        QrCodeResponseDto responseDto = QrCodeResponseDto.fromEntity(existingQrCode);
        return responseDto;
    }
    /**
     * 모든 QR 코드 목록 조회 (페이징, SUPER_ADMIN용)
     * @param projectId 프로젝트 ID
     * @param pageable 페이징 정보
     * @return 모든 QR 코드 목록 (페이징 처리됨)
     */
    @Transactional(readOnly = true)
    public Page<QrCodeResponseDto> getAllQrCodes(Long projectId, String searchType, String searchKeyword, Pageable pageable) {
        List<QrCode> qrCodes;
        long total;

        //검색타입 설정. 실제 DB 컬럼과 연동.
        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "qrCodeName": searchColumn = "qr_name"; break;
                case "qrType": searchColumn = "qr_type"; break;
                case "targetContent": searchColumn = "target_content"; break;
                case "validFromDate": searchColumn = "valid_from_date"; break;
                case "validToDate": searchColumn = "valid_to_date"; break;
            }
        }

        if (projectId != null) {
            // 특정 프로젝트 ID로 필터링
            qrCodes = qrCodeMapper.selectQrCodesByProjectIdWithPaging(
                    projectId, null, searchColumn, searchKeyword, pageable
            );
            total = qrCodeMapper.countQrCodesByProjectId(projectId, null, searchColumn, searchKeyword);
        } else {
            // 모든 QR 코드 조회
            qrCodes = qrCodeMapper.selectAllQrCodesWithPaging(searchColumn, searchKeyword, pageable);
            total = qrCodeMapper.countAllQrCodes(searchColumn, searchKeyword);
        }

        List<QrCodeResponseDto> dtos = qrCodes.stream()
                .map(QrCodeResponseDto::fromEntity)
                .collect(Collectors.toList());

    return new PageImpl<>(dtos, pageable, total);
    }

    /**
     * QR 코드 스캔 시 리디렉션 URL 조회 및 스캔 로그 기록
     * @param qrUuid 스캔된 QR 코드 UUID
     * @param request HttpServletRequest 객체 (IP, User-Agent 추출용)
     * @return 리디렉션 URL 또는 오류 정보를 담은 Map
     */
    public Object getRedirectUrl(String qrUuid, HttpServletRequest request) { 
        QrCode qrCode = qrCodeMapper.selectQrCodeByUuid(qrUuid)
                .orElseThrow(() -> {
                    log.warn("QR code not found for UUID: {}", qrUuid);
                    return new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND, "QR 코드를 찾을 수 없습니다.");
                });

        LocalDateTime now = LocalDateTime.now();
        boolean isExpired = (qrCode.getValidToDate() != null && now.isAfter(qrCode.getValidToDate()));
        boolean isNotYetValid = (qrCode.getValidFromDate() != null && now.isBefore(qrCode.getValidFromDate()));

        if (qrCode.getStatus() != QrCodeStatus.ACTIVE || "Y".equals(qrCode.getDeleteYn()) || isExpired || isNotYetValid) {
            // throw new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND, "현재 접속 가능한 QR 코드가 아닙니다.");
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("isError", true);
            errorResponse.put("httpStatus", 404); // 제안하는 HTTP 상태 코드, 변경 가능
            errorResponse.put("message", "현재 접속 가능한 QR 코드가 아닙니다.");
            log.warn("Access denied for QR UUID: {}. Reason: {}", qrUuid, "Not active, deleted, expired, or not yet valid.");
            return errorResponse;
        }

        // 스캔 로그 기록 및 스캔 수 증가 (QrScanLogService에 위임)
        try {
            String ipAddress = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            // QrScanLogService의 logScan 메서드 호출 
            qrScanLogService.logScan(qrCode.getQrCodeId(), ipAddress, userAgent, null, null);
            log.info("Scan logged successfully for QR UUID: {}", qrUuid);

        } catch (Exception e) {
            log.error("Error logging QR scan for QR UUID: {}", qrUuid, e);
            // 로그 기록 실패는 리디렉션 중단을 유발하지 않음
        }

        return qrCode.getTargetContent();
    }

    /**
     * QR 코드 스캔 시 타입과 내용을 반환 (리디렉션/표시 분기용)
     * @param qrUuid 스캔된 QR 코드 UUID
     * @param request H     * @return QR 코드 타입과 내용을 담은 Map 또는 오류 정보를 담은 Map
     */
    public Map<String, String> getQrCodeTypeAndContent(String qrUuid, HttpServletRequest request) {
        QrCode qrCode = qrCodeMapper.selectQrCodeByUuid(qrUuid)
                .orElseThrow(() -> {
                    log.warn("QR code not found for UUID: {}", qrUuid);
                    return new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND, "QR 코드를 찾을 수 없습니다.");
                });

        LocalDateTime now = LocalDateTime.now();
        boolean isExpired = (qrCode.getValidToDate() != null && now.isAfter(qrCode.getValidToDate()));
        boolean isNotYetValid = (qrCode.getValidFromDate() != null && now.isBefore(qrCode.getValidFromDate()));

        if (qrCode.getStatus() != QrCodeStatus.ACTIVE || "Y".equals(qrCode.getDeleteYn()) || isExpired || isNotYetValid) {
            // throw new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND, "현재 접속 가능한 QR 코드가 아닙니다.");
            Map<String, String> errorResponse = new HashMap<>();
            errorResponse.put("isDayExpired", "true");
            errorResponse.put("httpStatus", "403"); // 제안하는 HTTP 상태 코드, 변경 가능
            errorResponse.put("message", "현재 접속 가능한 QR 코드가 아닙니다.");
            log.warn("Access denied for QR UUID: {}. Reason: {}", qrUuid, "Not active, deleted, expired, or not yet valid.");
            return errorResponse;
        }

        // 스캔 로그 기록 및 스캔 수 증가 (QrScanLogService에 위임)
        try {
            String ipAddress = getClientIpAddress(request);
            String userAgent = request.getHeader("User-Agent");
            qrScanLogService.logScan(qrCode.getQrCodeId(), ipAddress, userAgent, null, null);
            log.info("Scan logged successfully for QR UUID: {}", qrUuid);
        } catch (Exception e) {
            log.error("Error logging QR scan for QR UUID: {}", qrUuid, e);
        }

        Map<String, String> result = new HashMap<>();
        result.put("qrType", qrCode.getQrType());
        result.put("targetContent", qrCode.getTargetContent());
        return result;
    }
    
    /**
     * 특정 QR 코드의 통계 정보를 조회합니다.
     * 
     * @param qrCodeId 통계를 조회할 QR 코드 ID
     * @param startDate 통계 조회 시작일
     * @param endDate 통계 조회 종료일
     * @return QR 코드 통계 정보 맵
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getQrCodeStatistics(Long qrCodeId, LocalDate startDate, LocalDate endDate) {
        // QR 코드 존재 확인
        QrCode qrCode = qrCodeMapper.selectQrCodeById(qrCodeId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND, "QR 코드를 찾을 수 없습니다: " + qrCodeId));
        
        // 조회 시간 범위 설정 (시작일 00:00:00 ~ 종료일 23:59:59)
        LocalDateTime startDateTime = startDate.atStartOfDay();
        LocalDateTime endDateTime = endDate.plusDays(1).atStartOfDay();
        
        // 결과 맵 생성
        Map<String, Object> resultMap = new LinkedHashMap<>();
        resultMap.put("qrCodeId", qrCodeId);
        resultMap.put("qrName", qrCode.getQrName());
        resultMap.put("startDate", startDate);
        resultMap.put("endDate", endDate);
        
        // 1. 기간 내 총 스캔 수 조회
        long totalScanCount = qrScanLogMapper.countScansByQrIdAndDateRange(
                qrCodeId, startDateTime, endDateTime);
        resultMap.put("totalScanCount", totalScanCount);
        
        // 2. 상위 Top 5 스캔 시간대 조회
        List<Map<String, Object>> topScanTimes = qrScanLogMapper.selectTopScanHoursByQrIdAndDateRange(
                qrCodeId, startDateTime, endDateTime);
        resultMap.put("topScanTimes", topScanTimes);
        
        // 3. 기기 OS별 통계 조회
        List<Map<String, Object>> deviceOsStats = qrScanLogMapper.selectDeviceOsStatsByQrIdAndDateRange(
                qrCodeId, startDateTime, endDateTime);
        resultMap.put("deviceOsStats", deviceOsStats);
        
        // 4. 일자별 스캔 수 조회 (오늘부터 -30일까지, 스캔수가 없는 날짜는 0으로 표시)
        LocalDate today = LocalDate.now();
        LocalDate thirtyDaysAgo = today.minusDays(30);
        
        // 시작일이 30일 이전인 경우 30일 전으로 조정
        LocalDate effectiveStartDate = startDate;
        
        // 종료일이 오늘 이후인 경우 오늘로 조정
        LocalDate effectiveEndDate = endDate;
        
        // 실제 데이터베이스 조회
        List<Map<String, Object>> scanData = qrScanLogMapper.selectDailyScanCountsByQrIdAndDateRange(
                qrCodeId, effectiveStartDate.atStartOfDay(), effectiveEndDate.plusDays(1).atStartOfDay());
                
        // 스캔 데이터를 Map으로 변환하여 날짜별 조회를 용이하게 함
        Map<String, Long> scanCountByDate = new HashMap<>();
        for (Map<String, Object> data : scanData) {
            String date = (String) data.get("date");
            Long count = ((Number) data.get("count")).longValue();
            scanCountByDate.put(date, count);
        }
        
        // 전체 날짜 범위 생성 (실제 범위 또는 최대 30일)
        List<Map<String, Object>> dailyScanCounts = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        
        LocalDate current = effectiveStartDate;
        while (!current.isAfter(effectiveEndDate)) {
            String dateStr = current.format(formatter);
            Map<String, Object> dayData = new HashMap<>();
            dayData.put("date", dateStr);
            dayData.put("count", scanCountByDate.getOrDefault(dateStr, 0L));
            dailyScanCounts.add(dayData);
            current = current.plusDays(1);
        }
        
        resultMap.put("dailyScanCounts", dailyScanCounts);
        
        return resultMap;
    }

    public void deleteQrCode(Long qrCodeId, String userEmail) {
    log.info("Attempting to delete QR code ID: {} by user: {}", qrCodeId, userEmail);

    QrCode qrCode = qrCodeMapper.selectQrCodeById(qrCodeId)
            .orElseThrow(() -> new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND));
    if (!checkUserProjectRoles(qrCode.getProjectId(), userEmail, SUPER_ADMIN, PROJECT_ADMIN)) {
        log.warn("Permission denied for user {} to delete QR code ID {} in project ID {}.",
                userEmail, qrCodeId, qrCode.getProjectId());
        throw new QRcodeException(ErrorCode.ACCESS_DENIED, "이 QR 코드를 삭제할 권한이 없습니다.");
    }

    try {
        int deletedCount = qrCodeMapper.deleteQrCodeById(qrCodeId, userEmail);

        if (deletedCount == 0) {
            log.warn("Logical delete returned 0 affected rows for QR code ID {}. Already deleted or race condition?", qrCodeId);
            throw new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND, "QR 코드를 삭제할 수 없거나 이미 삭제되었습니다.");
        }
        log.info("Successfully logically deleted QR code ID: {} by user: {}", qrCodeId, userEmail);
    } catch (Exception e) {
        log.error("Error during logical delete for QR code ID {}: {}", qrCodeId, e.getMessage(), e);
        throw new QRcodeException(ErrorCode.INTERNAL_SERVER_ERROR, "QR 코드 삭제 중 오류가 발생했습니다.");
    }
}

/**
 * 여러 QR 코드 일괄 삭제 (논리적 삭제)
 * @param qrCodeIds 삭제할 QR 코드 ID 목록
 * @param userEmail 삭제 요청한 사용자 이메일
 */
public void deleteQrCodes(List<Long> qrCodeIds, String userEmail) {
    if (qrCodeIds == null || qrCodeIds.isEmpty()) {
        log.warn("Attempting to delete QR codes with empty ID list by user: {}", userEmail);
        throw new QRcodeException(ErrorCode.INVALID_INPUT_VALUE, "삭제할 QR 코드 ID가 제공되지 않았습니다.");
    }
    
    log.info("Attempting to delete {} QR codes by user: {}", qrCodeIds.size(), userEmail);
    
    // 각 QR 코드에 대한 권한 검증 및 충돌 확인/해결
    Map<Long, Long> qrCodeProjectMap = new HashMap<>();
    List<Long> validatedQrCodeIds = new ArrayList<>();
    
    for (Long qrCodeId : qrCodeIds) {
        try {
            QrCode qrCode = qrCodeMapper.selectQrCodeById(qrCodeId)
                    .orElseThrow(() -> new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND));
            
            // 권한 검증
            if (!checkUserProjectRoles(qrCode.getProjectId(), userEmail, SUPER_ADMIN, PROJECT_ADMIN)) {
                log.warn("Permission denied for user {} to delete QR code ID {} in project ID {}.",
                        userEmail, qrCodeId, qrCode.getProjectId());
                throw new QRcodeException(ErrorCode.ACCESS_DENIED, "QR 코드 ID: " + qrCodeId + "를 삭제할 권한이 없습니다.");
            }
            
            // 이미 삭제된 상태인지 확인
            if ("Y".equals(qrCode.getDeleteYn())) {
                log.info("QR code ID {} is already deleted, skipping.", qrCodeId);
                continue;
            }
            
            System.out.println("QR code ID1111111111: " + qrCodeId);
            // 충돌하는 삭제된 QR 코드가 있는지 확인
            QrCode conflictingQr = qrCodeMapper.findConflictingSoftDeletedQrCode(
                    qrCode.getProjectId(), qrCode.getQrName(), qrCode.getQrCodeId());
                    System.out.println("QR code ID2222222222: " + qrCodeId);
            // 충돌하는 QR 코드가 있으면 이름 변경
            if (conflictingQr != null) {
                String newQrName = conflictingQr.getQrName() + "_deleted_" + conflictingQr.getQrCodeId() + "_" + System.currentTimeMillis();
                log.info("Found conflicting soft-deleted QR code ID {}. Renaming it from '{}' to '{}'", 
                        conflictingQr.getQrCodeId(), conflictingQr.getQrName(), newQrName);
                
                int updated = qrCodeMapper.updateQrNameWithSuffix(conflictingQr.getQrCodeId(), newQrName);
                if (updated != 1) {
                    log.warn("Failed to rename conflicting QR code ID {}", conflictingQr.getQrCodeId());
                    throw new QRcodeException(ErrorCode.INTERNAL_SERVER_ERROR, "충돌하는 QR 코드 이름 변경 중 오류가 발생했습니다.");
                }
            }
            
            qrCodeProjectMap.put(qrCodeId, qrCode.getProjectId());
            validatedQrCodeIds.add(qrCodeId);
            
        } catch (QRcodeException e) {
            log.error("Error validating permission for QR code ID {}: {}", qrCodeId, e.getMessage());
            throw e;
        }
    }
    
    if (validatedQrCodeIds.isEmpty()) {
        log.info("No valid QR codes to delete after validation checks");
        return;
    }
    
    try {
        // 배치 삭제 처리
        int updatedCount = qrCodeMapper.deleteQrCodesByIds(validatedQrCodeIds, userEmail);
        
        if (updatedCount == 0) {
            log.warn("Logical delete returned 0 affected rows for QR codes: {}. Already deleted or not found?", validatedQrCodeIds);
            throw new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND, "QR 코드를 삭제할 수 없거나 이미 삭제되었습니다.");
        }
        
        log.info("Successfully soft-deleted {} QR codes", updatedCount);
        
        if (updatedCount < validatedQrCodeIds.size()) {
            log.warn("Only {} out of {} QR codes were successfully deleted", updatedCount, validatedQrCodeIds.size());
        }
        
    } catch (Exception e) {
        log.error("Error during batch delete for QR codes {}: {}", validatedQrCodeIds, e.getMessage(), e);
        throw new QRcodeException(ErrorCode.INTERNAL_SERVER_ERROR, "QR 코드 일괄 삭제 중 오류가 발생했습니다.");
    }
}

    private boolean checkUserProjectRoles(Long projectId, String userEmail, UserRole... requiredRoles) {
        if (projectId == null || userEmail == null || requiredRoles == null || requiredRoles.length == 0) {
            return false;
        }

        String userRole = userMapper.selectUserRoleByEmail(userEmail)
                .orElseThrow(() -> new QRcodeException(ErrorCode.USER_NOT_FOUND, "사용자를 찾을 수 없습니다: " + userEmail));
        if (userRole.equals(UserRole.SUPER_ADMIN.name())) {
            return true;
        }

        List<String> requiredRoleNames = Arrays.stream(requiredRoles)
                .map(UserRole::name)
                .collect(Collectors.toList());

        int count = userMapper.countUserProjectMembershipWithRoles(userEmail, projectId, requiredRoleNames);
        return count > 0;
    }

    private <T> void updateFieldIfNotNull(T value, Consumer<T> setter) {
        if (value != null) {
            setter.accept(value);
        }
    }

    private void updateDateFieldIfValid(LocalDateTime parsedDate, Consumer<LocalDateTime> setter) {
        if (parsedDate != null) {
            setter.accept(parsedDate);
        }
    }

    private kr.wayplus.wayplus_qr.dto.QrCodeDesignOptionsDto parseDesignOptions(String designOptionsJson) {
        log.debug("Parsing design options JSON: {}", designOptionsJson);
        if (designOptionsJson == null || designOptionsJson.isBlank()) {
            designOptionsJson = DEFAULT_DESIGN_OPTIONS_JSON;
        }
        try {
            kr.wayplus.wayplus_qr.dto.QrCodeDesignOptionsDto options = objectMapper.readValue(designOptionsJson, kr.wayplus.wayplus_qr.dto.QrCodeDesignOptionsDto.class);
            if (options.getLogoRatio() == null) {
                options.setLogoRatio(DEFAULT_LOGO_RATIO);
            }
            return options;
        } catch (Exception e) {
            log.warn("Failed to parse designOptions JSON: '{}'. Returning default structure. Error: {}", designOptionsJson, e.getMessage());
            kr.wayplus.wayplus_qr.dto.QrCodeDesignOptionsDto defaultDto = new kr.wayplus.wayplus_qr.dto.QrCodeDesignOptionsDto();
            defaultDto.setLogoRatio(DEFAULT_LOGO_RATIO);
            return defaultDto;
        }
    }

    // 오류 복원 수준 문자열을 ErrorCorrectionLevel Enum으로 변환하는 헬퍼 메소드
    private ErrorCorrectionLevel parseErrorCorrectionLevel(String levelStr) {
        return switch (levelStr.toUpperCase()) {
            case "M" -> ErrorCorrectionLevel.M;
            case "Q" -> ErrorCorrectionLevel.Q;
            case "H" -> ErrorCorrectionLevel.H;
            default -> ErrorCorrectionLevel.L;
        };
    }

    // HttpServletRequest에서 클라이언트 IP 주소 추출 (X-Forwarded-For 헤더 고려)
    private String getClientIpAddress(HttpServletRequest request) {
        String xfHeader = request.getHeader("X-Forwarded-For");
        if (xfHeader == null || xfHeader.isEmpty() || "unknown".equalsIgnoreCase(xfHeader)) {
            return request.getRemoteAddr();
        }
        // X-Forwarded-For는 콤마로 구분된 IP 목록일 수 있음 (proxy1, proxy2, client)
        return xfHeader.split(",")[0].trim();
    }

    @Transactional
    public Map<String, Object> linkQuizToQrCode(Long qrCodeId, Long quizId, String userEmail) {
        // 1. QR 코드 존재 여부 및 권한 확인
        QrCode qrCode = qrCodeMapper.selectQrCodeById(qrCodeId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND));

        if (!checkUserProjectRoles(qrCode.getProjectId(), userEmail, SUPER_ADMIN, PROJECT_ADMIN, SUB_ADMIN)) {
            throw new QRcodeException(ErrorCode.ACCESS_DENIED, "해당 QR에 문제를 연결할 권한이 없습니다.");
        }

        // 2. Thymeleaf 서버로 연결 요청
        Map<String, Object> response = thymeleafClient.requestQrQuizMapping(qrCodeId, quizId, userEmail);

        // 3. Thymeleaf 서버 응답 처리
        log.info("Thymeleaf response: {}", response);
        if (response == null || !Boolean.TRUE.equals(response.get("success"))) {
            String errorMessage = response != null ? (String) response.get("message") : "알 수 없는 오류가 발생했습니다.";
            throw new QRcodeException(ErrorCode.THYMELEAF_SERVER_COMM_FAILED, errorMessage);
        }

        return response;
    }

    @Transactional
    public Map<String, Object> unlinkQuizFromQrCode(Long qrCodeId, Long quizId, String userEmail) {
        // 1. QR 코드 존재 여부 및 권한 확인
        QrCode qrCode = qrCodeMapper.selectQrCodeById(qrCodeId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.QR_CODE_NOT_FOUND));

        if (!checkUserProjectRoles(qrCode.getProjectId(), userEmail, SUPER_ADMIN, PROJECT_ADMIN, SUB_ADMIN)) {
            throw new QRcodeException(ErrorCode.ACCESS_DENIED, "해당 QR의 문제 연결을 해제할 권한이 없습니다.");
        }

        // 2. Thymeleaf 서버로 연결 해제 요청
        Map<String, Object> response = thymeleafClient.deleteQrQuizMapping(qrCodeId, quizId, userEmail);

        // 3. Thymeleaf 서버 응답 처리
        if (response == null || !Boolean.TRUE.equals(response.get("success"))) {
            String errorMessage = response != null ? (String) response.get("message") : "알 수 없는 오류가 발생했습니다.";
            throw new QRcodeException(ErrorCode.THYMELEAF_SERVER_COMM_FAILED, errorMessage);
        }

        return response;
    }
}
