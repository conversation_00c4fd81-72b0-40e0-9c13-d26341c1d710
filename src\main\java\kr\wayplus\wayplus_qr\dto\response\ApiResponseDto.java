package kr.wayplus.wayplus_qr.dto.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;

@Getter
@JsonInclude(JsonInclude.Include.NON_NULL) // null인 필드는 JSON에서 제외
public class ApiResponseDto<T> {
    private final boolean success; // final로 변경
    private final T data; // 성공 시 데이터 (final)
    private final ErrorDetail error; // 실패 시 에러 정보 (final)

    // private 생성자 추가
    private ApiResponseDto(boolean success, T data, ErrorDetail error) {
        this.success = success;
        this.data = data;
        this.error = error;
    }

    // 성공 응답 생성 헬퍼 메서드
    public static <T> ApiResponseDto<T> success(T data) {
        return new ApiResponseDto<>(true, data, null);
    }

    // 실패 응답 생성 헬퍼 메서드
    public static <T> ApiResponseDto<T> error(ErrorDetail errorDetail) {
        return new ApiResponseDto<>(false, null, errorDetail);
    }

    // 실패 응답 생성 (간단한 에러 코드와 메시지 사용 - 필요시 사용)
    public static <T> ApiResponseDto<T> error(String code, String message) {
        ErrorDetail errorDetail = ErrorDetail.builder().code(code).message(message).build();
        return new ApiResponseDto<>(false, null, errorDetail);
    }
}
