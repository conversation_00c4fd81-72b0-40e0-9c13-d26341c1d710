package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.NotBlank;
import kr.wayplus.wayplus_qr.dto.common.EnumPattern;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
public class UserUpdateRequestDto {

    @NotBlank(message = "이름은 필수 입력값입니다.")
    private String name;

    // 역할 변경이 필요한 경우에만 값을 전달하도록 유도 (필수값 아님)
    private String roleId;

    private String description;

    // 사용자 상태 변경 (선택 사항, Enum 검증 추가)
    @EnumPattern(regexp = "^(ACTIVE|INACTIVE|INVITED|LOCKED)$", message = "유효하지 않은 사용자 상태입니다.")
    private String status;

    // 생성자, 빌더 등은 필요에 따라 추가
}
