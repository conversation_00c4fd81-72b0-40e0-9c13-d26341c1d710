package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.exception.FileStorageException;
import kr.wayplus.wayplus_qr.exception.FileNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.beans.factory.annotation.Value;
import jakarta.annotation.PostConstruct;

import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Objects;
import java.util.UUID;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;

@Service
@Slf4j
public class FileStorageService {

    private Path fileStorageLocation;
    private Path qrFileStorageLocation;

    @Value("${upload.file.path}") 
    private String uploadDir;

    @Value("${upload.qr-file.path}")
    private String qrUploadDir;

    public FileStorageService() {
    }

    @PostConstruct
    public void init() {
        try {
            this.fileStorageLocation = Paths.get(uploadDir).toAbsolutePath().normalize();
            Files.createDirectories(this.fileStorageLocation);
            this.qrFileStorageLocation = Paths.get(qrUploadDir).toAbsolutePath().normalize();
            Files.createDirectories(this.qrFileStorageLocation);
        } catch (Exception ex) {
            throw new FileStorageException("Could not create the directory where the uploaded files will be stored. Path: " + uploadDir, ex);
        }
    }

    public String storeFile(MultipartFile file) {
        String originalFilename = StringUtils.cleanPath(Objects.requireNonNullElse(file.getOriginalFilename(), ""));
        String extension = "";
        int i = originalFilename.lastIndexOf('.');
        if (i > 0) {
            extension = originalFilename.substring(i);
        }
        String fileName = UUID.randomUUID().toString() + extension;

        try {
            if (fileName.contains("..")) {
                throw new FileStorageException("Sorry! Filename contains invalid path sequence " + originalFilename);
            }

            Path targetLocation = this.fileStorageLocation.resolve(fileName);
            try (InputStream inputStream = file.getInputStream()) {
                 Files.copy(inputStream, targetLocation, StandardCopyOption.REPLACE_EXISTING);
            }

            return fileName;
        } catch (IOException ex) {
            throw new FileStorageException("Could not store file " + originalFilename + ". Please try again!", ex);
        }
    }
    
    /**
     * 특정 경로에 파일을 저장합니다.
     * 
     * @param file 저장할 파일
     * @param targetPath 저장할 대상 경로 (절대 경로)
     * @return 저장된 파일 경로
     */
    public String storeFilePath(MultipartFile file, String targetPath) {
        try {
            if (file.isEmpty()) {
                throw new FileStorageException("Failed to store empty file");
            }
            
            String originalFilename = StringUtils.cleanPath(Objects.requireNonNullElse(file.getOriginalFilename(), ""));
            if (originalFilename.contains("..")) {
                throw new FileStorageException("Sorry! Filename contains invalid path sequence " + originalFilename);
            }
            
            // 파일 이름에 UUID 추가
            String fileExtension = "";
            if (originalFilename.contains(".")) {
                fileExtension = originalFilename.substring(originalFilename.lastIndexOf("."));
            }
            String newFilename = UUID.randomUUID().toString() + fileExtension;
            
            // 타겟 경로가 단순 디렉토리명이면 기본 업로드 경로와 결합
            Path fullPath;
            if (targetPath.contains("/") || targetPath.contains("\\")) {
                // 이미 완전한 경로인 경우
                fullPath = Paths.get(targetPath);
            } else {
                // 단순 디렉토리명인 경우(예: "inquiry")
                fullPath = Paths.get(uploadDir, targetPath, newFilename);
            }
            
            // 디렉토리 생성 (부모 디렉토리가 null이 아닌지 확인)
            Path parentDir = fullPath.getParent();
            if (parentDir != null) {
                Files.createDirectories(parentDir);
            } else {
                throw new FileStorageException("Invalid path: Parent directory is null");
            }
            
            try (InputStream inputStream = file.getInputStream()) {
                Files.copy(inputStream, fullPath, StandardCopyOption.REPLACE_EXISTING);
            }

            return fullPath.toString();
        } catch (IOException ex) {
            throw new FileStorageException("Could not store file. Please try again!", ex);
        }
    }

    public void deleteFile(String filename) {
        if (filename == null || filename.isEmpty()) {
            log.warn("Filename is null or empty, cannot delete file.");
            return;
        }

        try {
            Path targetLocation = this.fileStorageLocation.resolve(filename);
            boolean deleted = Files.deleteIfExists(targetLocation);
            if (deleted) {
                log.info("Successfully deleted file: {}", targetLocation);
            } else {
                log.warn("File not found, could not delete: {}", targetLocation);
            }
        } catch (IOException ex) {
            log.error("Could not delete file: {}. Reason: {}", filename, ex.getMessage());
            // FileStorageException을 던지거나, 에러를 로깅만 할 수 있습니다.
            // throw new FileStorageException("Could not delete file " + filename + ". Please try again!", ex);
        } catch (Exception ex) {
             log.error("An unexpected error occurred while deleting file: {}. Reason: {}", filename, ex.getMessage());
        }
    }

    public Resource loadFileAsResource(String filename) {
        try {
            Path filePath = this.qrFileStorageLocation.resolve(filename).normalize();
            Resource resource = new UrlResource(filePath.toUri());
            log.info("resourceresourceresource {}", resource);
            if (resource.exists() && resource.isReadable()) {
                return resource;
            } else {
                log.error("File not found or not readable: {}", filename);
                throw new FileNotFoundException("File not found " + filename + " 파일을 찾지 못했습니다.");
            }
        } catch (MalformedURLException ex) {
            log.error("File URL formation error for: {}", filename, ex);
            throw new FileNotFoundException("File not found " + filename + " 파일을 찾지 못했습니다.");
        } catch (FileNotFoundException ex) {
            // 이미 위에서 throw 했으므로 다시 던지기만 함
            throw ex;
        } catch (Exception ex) {
            log.error("An error occurred while loading file: {}", filename, ex);
            throw new FileStorageException("File not found " + filename + " 파일을 찾지 못했습니다.", ex);
        }
    }
    
    /**
     * 특정 경로의 파일을 리소스로 로드합니다.
     * 
     * @param filePath 파일 경로 (절대 경로)
     * @return 파일 리소스
     */
    public Resource loadFileAsResource(Path filePath) {
        try {
            Path normalizedPath = filePath.normalize();
            Resource resource = new UrlResource(normalizedPath.toUri());
            
            if (resource.exists() && resource.isReadable()) {
                return resource;
            } else {
                log.error("File not found or not readable: {}", filePath);
                throw new FileNotFoundException("File not found " + filePath + " 파일을 찾지 못했습니다.");
            }
        } catch (MalformedURLException ex) {
            log.error("File URL formation error for: {}", filePath, ex);
            throw new FileNotFoundException("File not found " + filePath + " 파일을 찾지 못했습니다.");
        }
    }
    
    /**
     * 기존 파일을 복사하여 새 UUID로 저장
     * @param sourceFilename 복사할 원본 파일명
     * @return 새로 생성된 파일명
     */
    public String copyFile(String sourceFilename) {
        if (sourceFilename == null || sourceFilename.isEmpty()) {
            log.warn("Source filename is null or empty, cannot copy file.");
            return null;
        }
        
        try {
            // 파일 확장자 추출
            String extension = "";
            int i = sourceFilename.lastIndexOf('.');
            if (i > 0) {
                extension = sourceFilename.substring(i);
            }
            
            // 새 파일명 생성
            String newFilename = UUID.randomUUID().toString() + extension;
            
            // 원본 파일 경로
            Path sourcePath = this.fileStorageLocation.resolve(sourceFilename).normalize();
            
            // 새 파일 경로
            Path targetPath = this.fileStorageLocation.resolve(newFilename).normalize();
            
            // 파일 존재 확인
            if (!Files.exists(sourcePath)) {
                log.warn("Source file not found: {}", sourcePath);
                return null;
            }
            
            // 파일 복사
            Files.copy(sourcePath, targetPath, StandardCopyOption.REPLACE_EXISTING);
            log.info("File copied successfully: {} -> {}", sourceFilename, newFilename);
            
            return newFilename;
        } catch (IOException ex) {
            log.error("Could not copy file: {}", sourceFilename, ex);
            throw new FileStorageException("Could not copy file " + sourceFilename, ex);
        }
    }
    

}
