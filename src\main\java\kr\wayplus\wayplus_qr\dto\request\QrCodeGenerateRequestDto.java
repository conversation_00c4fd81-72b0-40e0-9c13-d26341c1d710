package kr.wayplus.wayplus_qr.dto.request;

import kr.wayplus.wayplus_qr.entity.QrCodeStatus;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor // Jackson 파싱을 위해 기본 생성자 추가
public class QrCodeGenerateRequestDto {
    // JSON 데이터에서 파싱될 필드들
    private String content; // QR 코드 내용
    private Integer width = 300;
    private Integer height = 300;
    private QrCodeDotsOptionsDto dotsOptions;
    private QrCodeBackgroundOptionsDto backgroundOptions;
    private Double logoRatio;

    // Service 레이어에서 변환되어 사용될 필드 (Controller 또는 Service에서 설정)
    private LocalDateTime validFromDate;
    private LocalDateTime validToDate;
    private QrCodeStatus status = QrCodeStatus.ACTIVE; // 기본 상태 ACTIVE
    private Long projectId; // Controller에서 설정
    private String qrName; // Controller에서 설정
    private String qrType; // Controller에서 설정
    private String targetContent; // Controller에서 설정 (content 와 동일하게 사용될 수 있음)
    private String description; // Controller에서 설정

    // 기본값 처리를 위한 getter 재정의
    public QrCodeDotsOptionsDto getDotsOptions() {
        return dotsOptions == null ? new QrCodeDotsOptionsDto() : dotsOptions;
    }

    public QrCodeBackgroundOptionsDto getBackgroundOptions() {
        return backgroundOptions == null ? new QrCodeBackgroundOptionsDto() : backgroundOptions;
    }

    // 눈 색상이 지정되지 않았을 경우 점 색상을 반환하는 편의 메서드
    public String getEyeColorOrDefault() {
        QrCodeDotsOptionsDto options = getDotsOptions();
        if (options == null) {
            return "#000000"; // 기본 점 색상
        }
        return (options.getEyeColor() == null || options.getEyeColor().trim().isEmpty())
                ? options.getColor() // eyeColor가 없으면 dotsOptions.color 사용
                : options.getEyeColor();
    }
}
