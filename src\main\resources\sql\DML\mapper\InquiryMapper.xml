<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.InquiryMapper">

    <!-- 결과 매핑 -->
    <resultMap id="inquiryResultMap" type="kr.wayplus.wayplus_qr.entity.Inquiry">
        <id property="inquiryId" column="inquiry_id" />
        <result property="projectId" column="project_id" />
        <result property="projectName" column="project_name" />
        <result property="userEmail" column="user_email" />
        <result property="inquiryTitle" column="inquiry_title" />
        <result property="inquiryContent" column="inquiry_content" />
        <result property="inquiryType" column="inquiry_type" />
        <result property="inquiryStatus" column="inquiry_status" />
        <result property="createdAt" column="created_at" />
        <result property="updatedAt" column="updated_at" />
    </resultMap>

    <!-- 문의 생성 -->
    <insert id="insertInquiry" parameterType="kr.wayplus.wayplus_qr.entity.Inquiry" useGeneratedKeys="true" keyProperty="inquiryId">
        INSERT INTO inquiries (
            project_id,
            user_email,
            inquiry_title,
            inquiry_content,
            inquiry_type,
            inquiry_status,
            created_at
        ) VALUES (
            #{projectId},
            #{userEmail},
            #{inquiryTitle},
            #{inquiryContent},
            #{inquiryType},
            #{inquiryStatus},
            NOW()
        )
    </insert>

    <!-- ID로 문의 조회 -->
    <select id="selectInquiryById" parameterType="long" resultMap="inquiryResultMap">
        SELECT 
            iq.inquiry_id,
            iq.project_id,
            iq.user_email,
            iq.inquiry_title,
            iq.inquiry_content,
            iq.inquiry_type,
            iq.inquiry_status,
            iq.created_at,
            iq.updated_at,
            p.project_name
        FROM inquiries iq
        LEFT JOIN projects p ON iq.project_id = p.project_id
        WHERE inquiry_id = #{inquiryId}
    </select>

    <!-- 조건별 문의 목록 조회 (페이징) -->
    <select id="selectInquiriesByCondition" resultMap="inquiryResultMap">
        SELECT 
            inquiry_id,
            project_id,
            user_email,
            inquiry_title,
            inquiry_content,
            inquiry_type,
            inquiry_status,
            created_at,
            updated_at
        FROM inquiries
        <where>
            delete_yn = 'N'
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
            <if test="inquiryStatus != null">
                AND inquiry_status = #{inquiryStatus}
            </if>
            <if test="inquiryType != null and inquiryType != ''">
                AND inquiry_type = #{inquiryType}
            </if>
            <if test="searchKeyword != null and searchKeyword != ''">
                <choose>
                    <when test="searchColumn == 'inquiry_title'">
                        AND inquiry_title LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="searchColumn == 'inquiry_type'">
                        AND inquiry_type LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="searchColumn == 'inquiry_status'">
                        AND inquiry_status LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="searchColumn == 'user_email'">
                        AND user_email LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="searchColumn == 'created_at'">
                        AND created_at LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <otherwise>
                        AND (
                            inquiry_title LIKE CONCAT('%', #{searchKeyword}, '%') OR
                            inquiry_type LIKE CONCAT('%', #{searchKeyword}, '%') OR
                            inquiry_status LIKE CONCAT('%', #{searchKeyword}, '%') OR
                            user_email LIKE CONCAT('%', #{searchKeyword}, '%')
                        )
                    </otherwise>
                </choose>
            </if>
            <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
                ORDER BY
                <foreach item="order" collection="pageable.sort" separator=", ">
                    <choose>
                        <when test="order.property == 'inquiryTitle'">inquiry_title</when>
                        <when test="order.property == 'inquiryType'">inquiry_type</when>
                        <when test="order.property == 'inquiryStatus'">inquiry_status</when>
                        <when test="order.property == 'userEmail'">user_email</when>
                        <when test="order.property == 'createdAt'">created_at</when>
                        <!-- 다른 허용된 정렬 기준 컬럼 추가 (예: linked_landing_page_title, linked_event_name 등) -->
                        <otherwise>created_at</otherwise> <!-- 기본 정렬 기준 -->
                    </choose>
                    <choose>
                        <when test="order.direction.name() == 'ASC'">ASC</when>
                        <when test="order.direction.name() == 'DESC'">DESC</when>
                        <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                    </choose>
                </foreach>
            </if>
            <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
                <!-- 기본 정렬: 생성일 내림차순 -->
                ORDER BY created_at DESC
            </if>
            <!-- 페이징 -->
            <if test="pageable != null">
                LIMIT #{pageable.offset}, #{pageable.pageSize}
            </if>
        </where>
    </select>

    <!-- 조건별 문의 수 조회 -->
    <select id="countInquiriesByCondition" resultType="long">
        SELECT COUNT(*)
        FROM inquiries
        <where>
            delete_yn = 'N'
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
            <if test="inquiryStatus != null">
                AND inquiry_status = #{inquiryStatus}
            </if>
            <if test="inquiryType != null and inquiryType != ''">
                AND inquiry_type = #{inquiryType}
            </if>
            <if test="searchKeyword != null and searchKeyword != ''">
                <choose>
                    <when test="searchColumn == 'inquiry_title'">
                        AND inquiry_title LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="searchColumn == 'inquiry_type'">
                        AND inquiry_type LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="searchColumn == 'inquiry_status'">
                        AND inquiry_status LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="searchColumn == 'user_email'">
                        AND user_email LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <when test="searchColumn == 'created_at'">
                        AND created_at LIKE CONCAT('%', #{searchKeyword}, '%')
                    </when>
                    <otherwise>
                        AND (
                            inquiry_title LIKE CONCAT('%', #{searchKeyword}, '%') OR
                            inquiry_type LIKE CONCAT('%', #{searchKeyword}, '%') OR
                            inquiry_status LIKE CONCAT('%', #{searchKeyword}, '%') OR
                            user_email LIKE CONCAT('%', #{searchKeyword}, '%')
                        )
                    </otherwise>
                </choose>
            </if>
        </where>
    </select>

    <!-- 문의 내용 수정 -->
    <update id="updateInquiryContent">
        UPDATE inquiries
        SET inquiry_content = #{inquiryContent}, 
            inquiry_type = #{inquiryType},
            updated_at = NOW()
        WHERE inquiry_id = #{inquiryId}
          AND user_email = #{userEmail}
    </update>

    <!-- 문의 상태 변경 -->
    <update id="updateInquiryStatus">
        UPDATE inquiries
        SET inquiry_status = #{inquiryStatus},
            updated_at = NOW()
        WHERE inquiry_id = #{inquiryId}
    </update>

    <!-- 문의 삭제 (논리적 삭제) -->
    <update id="deleteInquiry">
        UPDATE inquiries
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE inquiry_id = #{inquiryId}
          AND delete_yn = 'N'
    </update>

</mapper>
