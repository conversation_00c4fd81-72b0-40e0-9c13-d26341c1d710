package kr.wayplus.wayplus_qr.util;

import io.jsonwebtoken.*;
import io.jsonwebtoken.security.Keys;
import jakarta.annotation.PostConstruct;
import kr.wayplus.wayplus_qr.entity.User; // User 엔티티 import
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;

import javax.crypto.SecretKey;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.stream.Collectors;


@Slf4j
@Component
public class JwtUtil {

    @Value("${jwt.secret}")
    private String secretKeyPlain; // application.yml의 jwt.secret 값

    @Value("${jwt.access-token-expiration-ms}")
    private long accessTokenExpirationMs; // application.yml의 jwt.access-token-expiration-ms 값

    @Value("${jwt.refresh-token-expiration-ms}")
    private long refreshTokenExpirationMs; // application.yml의 jwt.refresh-token-expiration-ms 값

    private SecretKey secretKey;
    private static final String AUTHORITIES_KEY = "auth";
    private static final String TOKEN_TYPE_ACCESS = "access";
    private static final String TOKEN_TYPE_REFRESH = "refresh";
    private static final String BEARER_PREFIX = "Bearer ";

    @PostConstruct
    protected void init() {
        // 평문 Secret Key를 SecretKey 객체로 변환
        this.secretKey = Keys.hmacShaKeyFor(secretKeyPlain.getBytes());
    }

    /**
     * Authentication 객체로부터 Access Token을 생성합니다.
     *
     * @param authentication Spring Security Authentication 객체
     * @return 생성된 Access Token 문자열
     */
    public String createAccessToken(Authentication authentication) {
        String authorities = authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .collect(Collectors.joining(","));

        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + accessTokenExpirationMs);

        return Jwts.builder()
                .setSubject(authentication.getName()) // 사용자 식별자 (여기서는 userEmail)
                .claim(AUTHORITIES_KEY, authorities) // 권한 정보
                .claim("type", TOKEN_TYPE_ACCESS) // 토큰 타입 명시
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(secretKey) // Key 타입에 맞는 알고리즘 자동 선택 (HS512)
                .compact();
    }

    /**
     * Refresh Token을 생성합니다. (특별한 claim 없이 만료 시간만 설정)
     *
     * @param authentication Spring Security Authentication 객체 (Subject 설정용)
     * @return 생성된 Refresh Token 문자열
     */
    public String createRefreshToken(Authentication authentication) {
        Date now = new Date();
        Date expiryDate = new Date(now.getTime() + refreshTokenExpirationMs);

        return Jwts.builder()
                .setSubject(authentication.getName()) // 사용자 식별자 (여기서는 userEmail)
                .claim("type", TOKEN_TYPE_REFRESH) // 토큰 타입 명시
                .setIssuedAt(now)
                .setExpiration(expiryDate)
                .signWith(secretKey) // Key 타입에 맞는 알고리즘 자동 선택 (HS512)
                .compact();
    }

    /**
     * 토큰에서 사용자 식별자(Subject, 여기서는 userEmail)를 추출합니다.
     *
     * @param token JWT 토큰
     * @return 사용자 이메일
     */
    public String getUserEmailFromToken(String token) {
        Claims claims = getClaimsFromToken(token);
        return claims.getSubject();
    }

    /**
     * 토큰에서 Authentication 객체를 생성합니다.
     *
     * @param token JWT 토큰
     * @return Spring Security Authentication 객체
     */
    public Authentication getAuthentication(String token) {
        Claims claims = getClaimsFromToken(token);

        Collection<? extends GrantedAuthority> authorities =
                Arrays.stream(claims.get(AUTHORITIES_KEY).toString().split(","))
                        .map(SimpleGrantedAuthority::new)
                        .collect(Collectors.toList());

        // User 엔티티를 직접 생성하기보다 UserDetails 객체(Principal)만 설정
        // 필요하다면 UserDetailsService를 통해 완전한 User 객체를 로드할 수 있음
        User principal = User.builder()
                             .userEmail(claims.getSubject())
                             // password는 토큰에 없으므로 빈 값 또는 null 설정
                             .roleId(claims.get(AUTHORITIES_KEY).toString()) // 권한 ID 문자열 그대로 설정 (필요시 파싱)
                             .build();

        return new UsernamePasswordAuthenticationToken(principal, token, authorities);
    }

    /**
     * 토큰의 유효성을 검증합니다. (서명, 만료 시간 등)
     *
     * @param token JWT 토큰
     * @return 유효하면 true, 아니면 false
     */
    public boolean validateToken(String token) {
        try {
            // verifyWith 와 parseSignedClaims 사용
            Jwts.parser()
                .verifyWith(secretKey) // setSigningKey 대신 verifyWith 사용
                .build()
                .parseSignedClaims(token); // parseClaimsJws 대신 parseSignedClaims 사용 (반환값 사용 안 함)
            return true;
        } catch (io.jsonwebtoken.security.SecurityException | MalformedJwtException e) {
             log.warn("Invalid JWT signature: {}", e.getMessage());
        } catch (ExpiredJwtException e) {
             log.warn("Expired JWT token: {}", e.getMessage());
        } catch (UnsupportedJwtException e) {
             log.warn("Unsupported JWT token: {}", e.getMessage());
        } catch (IllegalArgumentException e) {
             log.warn("JWT claims string is empty: {}", e.getMessage());
        } catch (Exception e) {
             log.error("JWT validation error: {}", e.getMessage());
        }
        return false;
    }

    /**
     * 토큰에서 Claims 정보를 추출합니다.
     *
     * @param token JWT 토큰
     * @return Claims 객체
     */
    private Claims getClaimsFromToken(String token) {
        // verifyWith, parseSignedClaims, getPayload 사용
        return Jwts.parser()
                .verifyWith(secretKey) // setSigningKey 대신 verifyWith 사용
                .build()
                .parseSignedClaims(token) // parseClaimsJws 대신 parseSignedClaims 사용
                .getPayload(); // getBody 대신 getPayload 사용
    }

    /**
     * HTTP 요청 헤더에서 Bearer 토큰을 추출합니다.
     *
     * @param bearerToken "Bearer <token>" 형식의 문자열
     * @return 실제 토큰 문자열 또는 null
     */
    public String resolveToken(String bearerToken) {
        if (bearerToken != null && bearerToken.startsWith(BEARER_PREFIX)) {
            return bearerToken.substring(BEARER_PREFIX.length());
        }
        return null;
    }

    /**
     * Access Token의 만료 시간을 반환합니다. (초 단위)
     * @return 만료 시간 (초)
     */
     public long getAccessTokenExpirationSeconds() {
        return accessTokenExpirationMs / 1000;
    }

    /**
     * Refresh Token의 만료 시간을 반환합니다. (밀리초 단위)
     * @return 만료 시간 (밀리초)
     */
     public long getRefreshTokenExpirationMillis() {
        return refreshTokenExpirationMs;
    }
}
