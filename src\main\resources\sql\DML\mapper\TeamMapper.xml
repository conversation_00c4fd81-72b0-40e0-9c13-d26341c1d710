<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.TeamMapper">
  <resultMap id="TeamResponseResultMap" type="kr.wayplus.wayplus_qr.entity.Team">
    <id property="teamId" column="team_id"/>
    <result property="eventId" column="event_id"/>
    <result property="eventName" column="event_name"/>
    <result property="teamName" column="team_name"/>
    <result property="teamCode" column="team_code"/>
    <result property="description" column="description"/>
    <result property="profileImagePath" column="profile_img_path"/>
    <result property="joinQrCodeId" column="join_qr_code_id"/>
    <result property="leaderAttendeeId" column="leader_attendee_id"/>
    <result property="useYn" column="use_yn"/>
    <result property="deleteYn" column="delete_yn"/>
    <result property="createUserEmail" column="create_user_email"/>
    <result property="createDate" column="create_date"/>
    <result property="updateUserEmail" column="update_user_email"/>
    <result property="lastUpdateDate" column="last_update_date"/>
  </resultMap>

  <!-- 팀 등록 -->
  <insert id="insertTeam" parameterType="kr.wayplus.wayplus_qr.entity.Team" useGeneratedKeys="true" keyProperty="teamId">
    INSERT INTO teams (
            event_id, team_name, team_code, description, max_members, team_status, profile_img_path, join_qr_code_id, 
            leader_attendee_id, use_yn, delete_yn, create_user_email, create_date,
            leader_name, leader_phone)
    VALUES (
            #{eventId}, #{teamName}, #{teamCode}, #{description}, #{maxMembers}, #{teamStatus}, #{profileImagePath}, #{joinQrCodeId}, 
            #{leaderAttendeeId}, 'Y', 'N', #{createUserEmail}, NOW(),
            #{leaderName}, #{leaderPhone})
  </insert>

  <!-- 팀 수정 -->
  <update id="updateTeam" parameterType="kr.wayplus.wayplus_qr.entity.Team">
    UPDATE teams
       SET team_name = #{teamName},
           team_code = #{teamCode},
           team_status = #{teamStatus},
           description = #{description},
           max_members = #{maxMembers},
           profile_img_path = #{profileImagePath},
           join_qr_code_id = #{joinQrCodeId},
           leader_attendee_id = #{leaderAttendeeId},
           leader_name = #{leaderName},
           leader_phone = #{leaderPhone},
           update_user_email = #{updateUserEmail},
           last_update_date = NOW()
     WHERE team_id = #{teamId}
       AND delete_yn = 'N'
  </update>

  <!-- 팀 삭제 (논리 삭제) -->
  <update id="deleteTeam">
    UPDATE teams
       SET use_yn = 'N', delete_yn = 'Y',
           delete_user_email = #{deleteUserEmail},
           delete_date = NOW()
     WHERE team_id = #{teamId}
       AND delete_yn = 'N'
  </update>

  <!-- 팀 ID로 팀 조회 -->
  <select id="selectTeamById" resultMap="TeamResponseResultMap">
    SELECT *
      FROM teams
     WHERE team_id = #{teamId}
       AND delete_yn = 'N'
  </select>

  <!-- 이벤트별 팀 목록 조회 -->
  <select id="selectTeamsByEventId" resultMap="TeamResponseResultMap">
    SELECT team_id, event_id, team_name, description, join_qr_code_id, leader_attendee_id,
           use_yn, delete_yn, create_user_email, create_date, update_user_email, last_update_date
      FROM teams
     WHERE event_id = #{eventId}
       AND delete_yn = 'N'
     ORDER BY create_date DESC
  </select>

  <!-- 모든 팀 목록 조회 -->
  <select id="selectAllTeams" resultMap="TeamResponseResultMap">
    SELECT team_id, event_id, team_name, description, join_qr_code_id, leader_attendee_id,
           use_yn, delete_yn, create_user_email, create_date, update_user_email, last_update_date
      FROM teams
     WHERE delete_yn = 'N'
     ORDER BY create_date DESC
  </select>

  <!-- 이벤트 내에서 팀명으로 팀 검색 -->
  <select id="selectTeamsByEventIdAndName" resultMap="TeamResponseResultMap">
    SELECT team_id, event_id, team_name, description, join_qr_code_id, leader_attendee_id,
           use_yn, delete_yn, create_user_email, create_date, update_user_email, last_update_date
      FROM teams
     WHERE event_id = #{eventId}
       AND team_name LIKE CONCAT('%', #{teamName}, '%')
       AND delete_yn = 'N'
     ORDER BY create_date DESC
  </select>

  <!-- 팀 리더 참가자 ID로 팀 검색 -->
  <select id="selectTeamsByLeaderAttendeeId" resultMap="TeamResponseResultMap">
    SELECT team_id, event_id, team_name, description, join_qr_code_id, leader_attendee_id,
           use_yn, delete_yn, create_user_email, create_date, update_user_email, last_update_date
      FROM teams
     WHERE leader_attendee_id = #{leaderAttendeeId}
       AND delete_yn = 'N'
     ORDER BY create_date DESC
  </select>

  <!-- 이벤트 내에서 팀명 중복 체크 -->
  <select id="countTeamsByEventIdAndName" resultType="int">
    SELECT COUNT(*)
      FROM teams
     WHERE event_id = #{eventId}
       AND team_name = #{teamName}
       AND use_yn = 'Y'
       AND delete_yn = 'N'
       <if test="excludeTeamId != null">
       AND team_id != #{excludeTeamId}
       </if>
  </select>

  <!-- 이벤트 내에서 팀 코드 중복 체크 -->
  <select id="countTeamsByEventIdAndCode" resultType="int">
    SELECT COUNT(*)
      FROM teams
     WHERE team_code = #{teamCode}
       AND use_yn = 'Y'
       AND delete_yn = 'N'
       <if test="excludeTeamId != null">
       AND team_id != #{excludeTeamId}
       </if>
  </select>

  <!-- 필터링 조건에 따른 팀 개수 조회 -->
  <select id="countTeamsWithFilters" resultType="int">
    SELECT COUNT(*)
    FROM teams t 
    LEFT JOIN events evt ON t.event_id = evt.event_id
    WHERE t.use_yn = 'Y'
       AND t.delete_yn = 'N'
       <if test="projectId != null">
       AND evt.project_id = #{projectId}
       </if>
       <if test="eventId != null">
       AND t.event_id = #{eventId}
       </if>
       <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
         <choose>
           <when test="searchColumn == 'team_name'">
           AND t.team_name LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'team_code'">
           AND t.team_code LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'leader_name'">
           AND t.leader_name LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'team_status'">
           AND t.team_status LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'create_date'">
           AND t.create_date LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
         </choose>
       </if>
  </select>

  <!-- 필터링 조건에 따른 팀 목록 조회 (페이징) -->
  <select id="selectTeamsWithFilters" resultMap="TeamResponseResultMap">
    SELECT t.*, evt.event_name
    FROM teams t 
    LEFT JOIN events evt ON t.event_id = evt.event_id
    WHERE t.use_yn = 'Y'
       AND t.delete_yn = 'N'
       <if test="projectId != null">
       AND evt.project_id = #{projectId}
       </if>
       <if test="eventId != null">
       AND t.event_id = #{eventId}
       </if>
       <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
         <choose>
           <when test="searchColumn == 'team_name'">
           AND t.team_name LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'team_code'">
           AND t.team_code LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'leader_name'">
           AND t.leader_name LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'team_status'">
           AND t.team_status LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'create_date'">
           AND t.create_date LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
         </choose>
       </if>
        <!-- 동적 정렬 -->
        <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
            ORDER BY
            <foreach item="order" collection="pageable.sort" separator=", ">
                <choose>
                    <when test="order.property == 'teamName'">team_name</when>
                    <when test="order.property == 'teamCode'">team_code</when>
                    <when test="order.property == 'leaderName'">leader_name</when>
                    <when test="order.property == 'teamStatus'">team_status</when>
                    <when test="order.property == 'createDate'">t.create_date</when>
                    <when test="order.property == 'lastUpdateDate'">t.last_update_date</when>
                    <when test="order.property == 'maxMembers'">max_members</when>
                    <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                    <otherwise>t.create_date</otherwise> <!-- 기본 정렬 기준 -->
                </choose>
                <choose>
                    <when test="order.direction.name() == 'ASC'">ASC</when>
                    <when test="order.direction.name() == 'DESC'">DESC</when>
                    <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                </choose>
            </foreach>
        </if>
        <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
            <!-- 기본 정렬: 생성일 내림차순 -->
            ORDER BY t.create_date DESC
        </if>
        <!-- 페이징 -->
        <if test="pageable != null">
            LIMIT #{pageable.offset}, #{pageable.pageSize}
        </if>
  </select>

</mapper>