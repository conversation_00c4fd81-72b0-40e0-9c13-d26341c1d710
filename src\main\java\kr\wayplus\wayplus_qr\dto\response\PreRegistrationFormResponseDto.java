package kr.wayplus.wayplus_qr.dto.response;

import kr.wayplus.wayplus_qr.entity.PreRegistrationForm;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class PreRegistrationFormResponseDto {
    private Long formId;
    private Long projectId;
    private String formName;
    private String description;
    private String completionMessage;
    private String autoConfirmYn;
    private String privacyPolicyAgreementText;
    private Boolean requireConsent;
    private String createUserEmail;
    private LocalDateTime createDate;
    private String updateUserEmail;
    private LocalDateTime lastUpdateDate;
    private List<PreRegistrationFormFieldResponseDto> fields;

    public static PreRegistrationFormResponseDto fromEntity(PreRegistrationForm e) {
        PreRegistrationFormResponseDto dto = new PreRegistrationFormResponseDto();
        dto.setFormId(e.getFormId());
        dto.setProjectId(e.getProjectId());
        dto.setFormName(e.getFormName());
        dto.setDescription(e.getDescription());
        dto.setCompletionMessage(e.getCompletionMessage());
        dto.setAutoConfirmYn(e.getAutoConfirmYn());
        dto.setPrivacyPolicyAgreementText(e.getPrivacyPolicyAgreementText());
        dto.setRequireConsent(e.getRequireConsent());
        dto.setCreateUserEmail(e.getCreateUserEmail());
        dto.setCreateDate(e.getCreateDate());
        dto.setUpdateUserEmail(e.getUpdateUserEmail());
        dto.setLastUpdateDate(e.getLastUpdateDate());
        return dto;
    }
}
