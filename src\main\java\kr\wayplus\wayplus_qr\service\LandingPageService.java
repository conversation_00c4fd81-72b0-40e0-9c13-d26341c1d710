package kr.wayplus.wayplus_qr.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import kr.wayplus.wayplus_qr.dto.request.LandingPageCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.LandingPageUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.LandingPageResponseDto;
import kr.wayplus.wayplus_qr.entity.LandingPage;
import kr.wayplus.wayplus_qr.entity.LandingPageStatus;
import kr.wayplus.wayplus_qr.entity.Project;
import kr.wayplus.wayplus_qr.entity.enums.UserRole;
import kr.wayplus.wayplus_qr.exception.CustomLandingPageException;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.mapper.LandingPageMapper;
import kr.wayplus.wayplus_qr.mapper.ProjectMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class LandingPageService {

    private final LandingPageMapper landingPageMapper;
    private final ProjectMapper projectMapper;
    private final UserMapper userMapper;
    private final ObjectMapper objectMapper;
    private final FileStorageService fileStorageService;

    private static final Map<String, String> LANDING_PAGE_SORT_PROPERTY_MAP = Map.of(
            "landingPageId", "landing_page_id",
            "projectId", "project_id",
            "pageTitle", "page_title",
            "status", "status",
            "validFromDate", "valid_from_date",
            "validToDate", "valid_to_date",
            "createDate", "create_date",
            "lastUpdateDate", "last_update_date"
    );

    @Transactional
    public Long createLandingPage(LandingPageCreateRequestDto requestDto, String creatorEmail) {
        // 1. 프로젝트 유효성 확인
        Project project = projectMapper.selectProjectById(requestDto.getProjectId())
                .orElseThrow(() -> new CustomLandingPageException(ErrorCode.PROJECT_NOT_FOUND, "프로젝트를 찾을 수 없습니다: ID=" + requestDto.getProjectId()));

        // 2. 접근 권한 확인 (프로젝트 생성자와 요청 사용자가 동일한지 확인)
        checkAccessPermission(project.getProjectId(), creatorEmail);

        // 3. 내부 이름 중복 확인
        landingPageMapper.selectLandingPageByProjectIdAndPageTitle(requestDto.getProjectId(), requestDto.getPageTitle())
                .ifPresent(lp -> { throw new CustomLandingPageException(ErrorCode.INVALID_INPUT_VALUE); });

        LandingPage landingPage = LandingPage.builder()
                .projectId(requestDto.getProjectId())
                .pageTitle(requestDto.getPageTitle())
                .description(requestDto.getDescription())
                .status(requestDto.getStatus() != null ? requestDto.getStatus() : LandingPageStatus.DRAFT)
                .validFromDate(requestDto.getValidFromDate())
                .validToDate(requestDto.getValidToDate())
                .createUserEmail(creatorEmail)
                .useYn("Y")
                .deleteYn("N")
                .build();

        log.debug("Request DTO contentJson Map: {}", requestDto.getContentJson());
        if (requestDto.getContentJson() != null && !requestDto.getContentJson().isEmpty()) {
            try {
                String jsonContent = objectMapper.writeValueAsString(requestDto.getContentJson());
                log.debug("Serialized contentJson String before setting to entity: {}", jsonContent);
                landingPage.setContentJson(jsonContent);
            } catch (JsonProcessingException e) {
                throw new CustomLandingPageException(ErrorCode.INTERNAL_SERVER_ERROR);
            }
        }

        log.info("LandingPage entity before insert: {}", landingPage);
        int result = landingPageMapper.insertLandingPage(landingPage);
        if (result == 0) {
            throw new CustomLandingPageException(ErrorCode.INTERNAL_SERVER_ERROR);
        }

        log.info("Successfully created landing page with ID {} for project ID {}", landingPage.getLandingPageId(), requestDto.getProjectId());
        return landingPage.getLandingPageId();
    }

    @Transactional(readOnly = true)
    public LandingPageResponseDto getLandingPageById(Long landingPageId, String userEmail) {
        LandingPage landingPage = landingPageMapper.selectLandingPageById(landingPageId)
                .orElseThrow(() -> new CustomLandingPageException(ErrorCode.ENTITY_NOT_FOUND, "랜딩 페이지를 찾을 수 없습니다: ID=" + landingPageId));

        // 접근 권한 확인 (사용자 이메일이 있을 경우에만)
        if (userEmail != null) {
            checkAccessPermission(landingPage.getProjectId(), userEmail);
        }

        return LandingPageResponseDto.fromEntity(landingPage);
    }

    @Transactional(readOnly = true)
    public Page<LandingPageResponseDto> getLandingPagesByProjectId(Long projectId, String status, String searchType, String searchKeyword, Pageable pageable, String userEmail) {
        // 현재 인증 정보 가져오기
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        boolean isSuperAdmin = authentication != null && authentication.getAuthorities().stream()
                .map(GrantedAuthority::getAuthority)
                .anyMatch("ROLE_SUPER_ADMIN"::equals);

        // SUPER_ADMIN이 아닌 경우에만 접근 권한 확인
        if (!isSuperAdmin) {
            checkAccessPermission(projectId, userEmail);
        }

        // 검색용 컬럼 매핑
        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "pageTitle": searchColumn = "page_title"; break;
                case "validFromDate": searchColumn = "valid_from_date"; break;
                case "validToDate": searchColumn = "valid_to_date"; break;
            }
        }

        // 1. 해당 페이지의 랜딩 페이지 목록 조회
        List<LandingPage> landingPages = landingPageMapper.selectLandingPagesByProjectId(projectId, status, searchColumn, searchKeyword, pageable);

        // 2. 전체 랜딩 페이지 개수 조회
        long total = landingPageMapper.countLandingPagesByProjectId(projectId, status, searchColumn, searchKeyword);

        // 3. 엔티티 리스트를 DTO 리스트로 변환
        List<LandingPageResponseDto> dtos = landingPages.stream()
                .map(LandingPageResponseDto::fromEntity)
                .collect(Collectors.toList());

        // 4. Page 객체 생성 및 반환
        return new PageImpl<>(dtos, pageable, total);
    }

    @Transactional
    public void updateLandingPage(Long landingPageId, LandingPageUpdateRequestDto requestDto, String userEmail) {
        // 1. 랜딩 페이지 조회
        LandingPage existingLandingPage = landingPageMapper.selectLandingPageById(landingPageId)
                .orElseThrow(() -> new CustomLandingPageException(ErrorCode.ENTITY_NOT_FOUND, "랜딩 페이지를 찾을 수 없습니다: ID=" + landingPageId));

        // 2. 접근 권한 확인 (프로젝트 생성자와 요청 사용자가 동일한지 확인)
        checkAccessPermission(existingLandingPage.getProjectId(), userEmail);

        // 3. Project 유효성 확인 (이미 랜딩 페이지 조회 시 projectId 확보됨)
        projectMapper.selectProjectById(existingLandingPage.getProjectId())
                .orElseThrow(() -> new CustomLandingPageException(ErrorCode.PROJECT_NOT_FOUND, "프로젝트를 찾을 수 없습니다: ID=" + existingLandingPage.getProjectId()));

        // 5. 랜딩 페이지 정보 업데이트 (null이 아닌 필드만 업데이트)
        boolean updated = false;
        if (requestDto.getLandingPageName() != null && !requestDto.getLandingPageName().equals(existingLandingPage.getPageTitle())) {
            existingLandingPage.setPageTitle(requestDto.getLandingPageName());
            updated = true;
        }
        if (requestDto.getDescription() != null && !requestDto.getDescription().equals(existingLandingPage.getDescription())) {
            existingLandingPage.setDescription(requestDto.getDescription());
            updated = true;
        }

        // Status 업데이트 로직 추가
        if (requestDto.getStatus() != null && !requestDto.getStatus().equals(existingLandingPage.getStatus())) {
            existingLandingPage.setStatus(requestDto.getStatus());
            updated = true;
        }

        // validFromDate 업데이트 로직 수정
        if (requestDto.getValidFromDate() != null) { // DTO에 값이 있는 경우
            if (!requestDto.getValidFromDate().equals(existingLandingPage.getValidFromDate())) {
                existingLandingPage.setValidFromDate(requestDto.getValidFromDate());
                updated = true;
            }
        } else { // DTO에 값이 null인 경우
            if (existingLandingPage.getValidFromDate() != null) { // 기존 값이 null이 아닐 때만 null로 업데이트
                existingLandingPage.setValidFromDate(null);
                updated = true;
            }
        }

        // validToDate 업데이트 로직 수정
        if (requestDto.getValidToDate() != null) { // DTO에 값이 있는 경우
            if (!requestDto.getValidToDate().equals(existingLandingPage.getValidToDate())) {
                existingLandingPage.setValidToDate(requestDto.getValidToDate());
                updated = true;
            }
        } else { // DTO에 값이 null인 경우
            if (existingLandingPage.getValidToDate() != null) { // 기존 값이 null이 아닐 때만 null로 업데이트
                existingLandingPage.setValidToDate(null);
                updated = true;
            }
        }

        if (requestDto.getContentJson() != null) {
            try {
                String newContentJson = objectMapper.writeValueAsString(requestDto.getContentJson());
                if (!newContentJson.equals(existingLandingPage.getContentJson())) {
                    existingLandingPage.setContentJson(newContentJson);
                    updated = true;
                }
            } catch (JsonProcessingException e) {
                log.error("Error processing JSON content for landing page update: {}", landingPageId, e);
                throw new CustomLandingPageException(ErrorCode.INVALID_INPUT_VALUE, "JSON 콘텐츠 처리 중 오류 발생");
            }
        }

        // 6. 변경 사항이 있을 경우에만 업데이트 수행
        if (updated) {
            existingLandingPage.setUpdateUserEmail(userEmail);
            existingLandingPage.setLastUpdateDate(LocalDateTime.now());
            int affectedRows = landingPageMapper.updateLandingPage(existingLandingPage);
            if (affectedRows == 0) {
                log.error("Landing page update failed for ID: {}", landingPageId);
                throw new CustomLandingPageException(ErrorCode.INTERNAL_SERVER_ERROR, "랜딩 페이지 업데이트 실패");
            }
            log.info("Landing page updated successfully: ID={}, Updated by={}", landingPageId, userEmail);
        } else {
            log.info("No changes detected for landing page update: ID={}", landingPageId);
        }
    }

    // 랜딩 페이지 삭제 (논리적 삭제)
    public void deleteLandingPage(Long landingPageId, String userEmail) {
        // 1. 랜딩 페이지 조회
        LandingPage landingPage = landingPageMapper.selectLandingPageById(landingPageId)
                .orElseThrow(() -> new CustomLandingPageException(ErrorCode.ENTITY_NOT_FOUND, "삭제할 랜딩 페이지를 찾을 수 없습니다: ID=" + landingPageId));

        // 2. 접근 권한 확인 (프로젝트 생성자와 요청 사용자가 동일한지 확인)
        checkAccessPermission(landingPage.getProjectId(), userEmail);

        // 3. 랜딩 페이지 논리적 삭제
        int affectedRows = landingPageMapper.logicalDeleteLandingPage(landingPageId, userEmail);
        if (affectedRows == 0) {
            log.warn("Landing page logical delete failed or already deleted: ID={}", landingPageId);
            // 이미 삭제되었거나 존재하지 않는 경우에도 성공으로 간주할 수 있음 (요구사항에 따라 다름)
            // 여기서는 실패 로그만 남기고 예외는 던지지 않음.
            // throw new CustomLandingPageException(ErrorCode.ENTITY_NOT_FOUND, "삭제할 랜딩 페이지를 찾을 수 없거나 업데이트 실패: ID=" + landingPageId);
        }
        log.info("Landing page logically deleted: ID={}, Deleted by={}", landingPageId, userEmail);
    }

    @SuppressWarnings({"unused", "unchecked"}) // 응답 DTO 생성 시 사용될 수 있음, ObjectMapper 타입 캐스팅 경고 무시
    private Map<String, Object> parseJsonContent(String jsonContent) {
        if (jsonContent == null || jsonContent.isEmpty()) {
            return null;
        }
        try {
            return objectMapper.readValue(jsonContent, Map.class);
        } catch (JsonProcessingException e) {
            log.error("Error parsing landing page JSON content: {}", jsonContent, e);
            throw new CustomLandingPageException(ErrorCode.INTERNAL_SERVER_ERROR, "랜딩 페이지 콘텐츠 처리 중 오류 발생");
        }
    }

    private void checkAccessPermission(Long projectId, String userEmail) {
        // Project 존재 여부 확인 (선택적: 필요하다면 ProjectMapper 사용 유지)
        projectMapper.selectProjectById(projectId)
                .orElseThrow(() -> new CustomLandingPageException(ErrorCode.PROJECT_NOT_FOUND, "접근 권한 확인 중 프로젝트를 찾을 수 없습니다: ID=" + projectId));

        // UserMapper를 사용하여 사용자-프로젝트 멤버십 확인
        String userRole = userMapper.selectUserRoleByEmail(userEmail)
                .orElseThrow(() -> new CustomLandingPageException(
                        ErrorCode.USER_NOT_FOUND,
                        "사용자를 찾을 수 없습니다: " + userEmail));
        if (!UserRole.SUPER_ADMIN.name().equals(userRole)) {
            int membershipCount = userMapper.countUserProjectMembership(userEmail, projectId);
            if (membershipCount == 0) {
                throw new CustomLandingPageException(ErrorCode.ACCESS_DENIED, "해당 프로젝트에 대한 접근 권한이 없습니다.");
            }
        }
        // 접근 허용 (멤버십 존재)
        log.info("Access granted for user {} on project {}.", userEmail, projectId);
    }

    /**
     * SUPER_ADMIN: 모든 또는 특정 프로젝트의 랜딩 페이지 목록 조회 (페이징 및 동적 정렬)
     */
    @Transactional(readOnly = true)
    public Page<LandingPageResponseDto> getAllLandingPagesForSuperAdmin(
            Long projectId, String searchType, String searchKeyword, Pageable pageable) {
        List<LandingPage> landingPages;
        long total;
        //검색타입 설정. 실제 DB 컬럼과 연동.
        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "pageTitle": searchColumn = "page_title"; break;
                case "validFromDate": searchColumn = "valid_from_date"; break;
                case "validToDate": searchColumn = "valid_to_date"; break;
            }
        }

        if (projectId != null) {
            // 특정 프로젝트 ID로 필터링
            landingPages = landingPageMapper.selectLandingPagesByProjectIdForSuperAdmin(projectId, searchColumn, searchKeyword, pageable);
            total = landingPageMapper.countLandingPagesByProjectIdForSuperAdmin(projectId, searchColumn, searchKeyword);
        } else {
            // 모든 랜딩 페이지 조회
            landingPages = landingPageMapper.selectAllLandingPagesForSuperAdmin(searchColumn, searchKeyword, pageable);
            total = landingPageMapper.countAllLandingPagesForSuperAdmin(searchColumn, searchKeyword);
        }

        List<LandingPageResponseDto> dtos = landingPages.stream()
                .map(LandingPageResponseDto::fromEntity)
                .collect(Collectors.toList());

        return new PageImpl<>(dtos, pageable, total);
    }

    /**
     * Pageable 객체의 정렬 파라미터를 검증하고 DB 컬럼명으로 변환하여 Map으로 반환
     */
    /**
     * contentJson에서 이미지 파일 경로를 추출하여 복사하고 새 경로로 업데이트
     * @param contentJson 원본 contentJson
     * @return 이미지 경로가 업데이트된 새 contentJson
     */
    /**
     * contentJson에서 이미지 파일 경로를 추출하여 복사하고 새 경로로 업데이트
     * @param contentJson 원본 contentJson
     * @return 이미지 경로가 업데이트된 새 contentJson
     */
    private String copyImagesInContentJson(String contentJson) {
        if (contentJson == null || contentJson.isEmpty()) {
            return contentJson;
        }
        
        try {
            // UUID 형태 파일명 정규식 패턴 (파일 이름만 추출)
            Pattern pattern = Pattern.compile("uploads/([a-f0-9-]{36}\\.[a-zA-Z0-9]+)");
            Matcher matcher = pattern.matcher(contentJson);
            
            StringBuffer sb = new StringBuffer();
            while (matcher.find()) {
                String filename = matcher.group(1); // 파일명만 추출 (UUID.확장자)
                log.debug("찾은 이미지 파일: {}", filename);
                
                // 파일 복사
                String newFilename = fileStorageService.copyFile(filename);
                
                if (newFilename != null) {
                    // 전체 JSON에서 파일명 업데이트
                    matcher.appendReplacement(sb, "uploads/" + newFilename);
                    log.info("이미지 파일 복사 완료: {} -> {}", filename, newFilename);
                }
            }
            matcher.appendTail(sb);
            
            return sb.toString();
        } catch (Exception e) {
            log.error("이미지 파일 복사 중 오류 발생: {}", e.getMessage(), e);
            // 오류 발생 시 원본 contentJson 그대로 반환
            return contentJson;
        }
    }

    private Map<String, Object> validateAndPrepareParams(Pageable pageable) {
        Map<String, Object> params = new HashMap<>();
        params.put("offset", pageable.getOffset());
        params.put("pageSize", pageable.getPageSize());

        Sort.Order sortOrder = pageable.getSort().stream().findFirst().orElse(Sort.Order.asc("landingPageId")); // 기본 정렬
        String property = sortOrder.getProperty();
        String direction = sortOrder.getDirection().name();

        String sortColumn = LANDING_PAGE_SORT_PROPERTY_MAP.get(property);
        if (sortColumn == null) {
            log.warn("지원하지 않는 정렬 속성입니다: {}", property);
            throw new CustomLandingPageException(ErrorCode.INVALID_SORT_PARAMETER, "지원하지 않는 정렬 속성입니다: " + property);
            // 혹은 기본 정렬 컬럼 사용
            // sortColumn = "landing_page_id";
        }

        params.put("sortColumn", sortColumn);
        params.put("sortDirection", direction);

        log.debug("랜딩페이지 목록 조회 파라미터: {}", params);
        return params;
    }

    /**
     * 랜딩 페이지 복사
     * @param landingPageId 복사할 원본 랜딩 페이지 ID
     * @param newPageTitle 복사된 랜딩 페이지의 새 제목 (선택적)
     * @param userEmail 요청 사용자 이메일
     * @return 복사된 랜딩 페이지 ID
     */
    @Transactional
    public Long copyLandingPage(Long landingPageId, String newPageTitle, String userEmail) {
        // 1. 원본 랜딩 페이지 조회
        LandingPage sourceLandingPage = landingPageMapper.selectLandingPageById(landingPageId)
                .orElseThrow(() -> new CustomLandingPageException(ErrorCode.ENTITY_NOT_FOUND, 
                        "복사할 랜딩 페이지를 찾을 수 없습니다: ID=" + landingPageId));

        // 2. 접근 권한 확인
        checkAccessPermission(sourceLandingPage.getProjectId(), userEmail);

        // 3. 새 랜딩 페이지 제목 결정
        final String finalPageTitle;
        if (newPageTitle == null || newPageTitle.trim().isEmpty()) {
            finalPageTitle = sourceLandingPage.getPageTitle() + "- copy";
        } else {
            finalPageTitle = newPageTitle;
        }

        // 4. 제목 중복 확인
        landingPageMapper.selectLandingPageByProjectIdAndPageTitle(sourceLandingPage.getProjectId(), finalPageTitle)
                .ifPresent(lp -> { 
                    throw new CustomLandingPageException(
                        ErrorCode.INVALID_INPUT_VALUE, 
                        "동일한 제목의 랜딩 페이지가 이미 존재합니다: " + finalPageTitle
                    ); 
                });

        // 5. 원본 contentJson에서 이미지 파일 복사 및 경로 업데이트
        String newContentJson = copyImagesInContentJson(sourceLandingPage.getContentJson());
        
        // 6. 새 랜딩 페이지 객체 생성
        LandingPage newLandingPage = LandingPage.builder()
                .projectId(sourceLandingPage.getProjectId())
                .pageTitle(finalPageTitle)
                .description(sourceLandingPage.getDescription())
                .contentJson(newContentJson)
                .status(LandingPageStatus.DRAFT) // 복사본은 항상 DRAFT 상태로 시작
                .validFromDate(sourceLandingPage.getValidFromDate())
                .validToDate(sourceLandingPage.getValidToDate())
                .createUserEmail(userEmail)
                .useYn("Y")
                .deleteYn("N")
                .build();

        // 7. 새 랜딩 페이지 저장
        log.info("랜딩 페이지 복사 시작: 원본 ID={}, 새 제목={}, 요청자={}", landingPageId, finalPageTitle, userEmail);
        int result = landingPageMapper.insertLandingPage(newLandingPage);
        if (result == 0) {
            log.error("랜딩 페이지 복사 실패: 원본 ID={}", landingPageId);
            throw new CustomLandingPageException(ErrorCode.INTERNAL_SERVER_ERROR, "랜딩 페이지 복사 중 오류가 발생했습니다.");
        }

        log.info("랜딩 페이지 복사 완료: 원본 ID={}, 새 ID={}, 요청자={}", 
                landingPageId, newLandingPage.getLandingPageId(), userEmail);
        return newLandingPage.getLandingPageId();
    }
}
