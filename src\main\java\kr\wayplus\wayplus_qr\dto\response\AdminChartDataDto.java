package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AdminChartDataDto {
    private List<String> dates;        // 날짜 목록 (YYYY-MM-DD 형식)
    private Map<String, List<Long>> createdCounts; // 역할별(Key: role_id) 일별 관리자 생성 수 목록
}
