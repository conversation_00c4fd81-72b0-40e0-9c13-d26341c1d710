package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.FileUploadResponseDto;
import kr.wayplus.wayplus_qr.service.FileStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import jakarta.servlet.http.HttpServletRequest;

import java.io.IOException;
import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/way/upload")
@Slf4j
public class FileUploadController {

    @Autowired
    private FileStorageService fileStorageService;

    @PostMapping("/images")
    public ResponseEntity<ApiResponseDto<FileUploadResponseDto>> uploadFile(@RequestParam("file") MultipartFile file) {
        String storedFileName = fileStorageService.storeFile(file);
        String originalFileName = Objects.toString(file.getOriginalFilename(), "");

        String fileDownloadUri = ServletUriComponentsBuilder.fromCurrentContextPath()
                                     .path("/uploads/")
                                     .path(storedFileName)
                                     .toUriString();

        FileUploadResponseDto fileInfo = FileUploadResponseDto.builder()
                .imageId(storedFileName)
                .imageUrl(fileDownloadUri)
                .imageName(originalFileName)
                .build();

        return ResponseEntity.ok(ApiResponseDto.success(fileInfo));
    }
}
