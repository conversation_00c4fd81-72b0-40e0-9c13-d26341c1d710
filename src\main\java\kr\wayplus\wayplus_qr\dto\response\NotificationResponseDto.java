package kr.wayplus.wayplus_qr.dto.response;

import kr.wayplus.wayplus_qr.entity.Notification;
import kr.wayplus.wayplus_qr.entity.NotificationType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 알림 응답 DTO.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationResponseDto {
    private Long notificationId;
    private String userEmail;
    private Long inquiryId;
    private String message;
    private boolean isRead;
    private NotificationType notificationType;
    private LocalDateTime createdAt;

    public static NotificationResponseDto fromEntity(Notification entity) {
        return NotificationResponseDto.builder()
                .notificationId(entity.getNotificationId())
                .userEmail(entity.getUserEmail())
                .inquiryId(entity.getInquiryId())
                .message(entity.getMessage())
                .isRead(entity.isRead())
                .notificationType(entity.getNotificationType())
                .createdAt(entity.getCreatedAt())
                .build();
    }
}
