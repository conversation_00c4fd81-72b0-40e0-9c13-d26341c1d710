package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.request.LandingPageCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.LandingPageUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.LandingPageResponseDto;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.service.LandingPageService;
import kr.wayplus.wayplus_qr.config.SearchTypeRegistry;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Collections;
import kr.wayplus.wayplus_qr.dto.response.ListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.SearchTypeInfo;

@RestController
@RequestMapping("/api/way/landing-pages")
@RequiredArgsConstructor
public class LandingPageController {

    private final LandingPageService landingPageService;
    private final SearchTypeRegistry searchTypeRegistry;

    /**
     * 랜딩 페이지 생성
     * @param requestDto 생성 요청 DTO
     * @return 생성된 랜딩 페이지 ID
     */
    @PostMapping
    public ResponseEntity<ApiResponseDto<Long>> createLandingPage(
            @Valid @RequestBody LandingPageCreateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {

        // 로그인된 사용자 정보 확인
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("로그인이 필요합니다.", "UNAUTHORIZED"));
        }
        String userEmail = userDetails.getUserEmail();

        Long createdLandingPageId = landingPageService.createLandingPage(requestDto, userEmail);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success(createdLandingPageId));
    }

    /**
     * 특정 랜딩 페이지 조회
     * @param landingPageId 조회할 랜딩 페이지 ID
     * @return 랜딩 페이지 정보 DTO
     */
    @GetMapping("/{landingPageId}")
    public ResponseEntity<ApiResponseDto<LandingPageResponseDto>> getLandingPageById(
            @PathVariable("landingPageId") Long landingPageId,
            @AuthenticationPrincipal User userDetails) {

        // 로그인된 사용자 정보 확인
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("로그인이 필요합니다.", "UNAUTHORIZED"));
        }
        String userEmail = userDetails.getUserEmail();

        // Service가 DTO를 반환하므로 바로 받아서 사용
        LandingPageResponseDto landingPageDto = landingPageService.getLandingPageById(landingPageId, userEmail);
        return ResponseEntity.ok(ApiResponseDto.success(landingPageDto)); // DTO를 바로 사용
    }


    /**
     * 특정 프로젝트의 랜딩 페이지 목록 조회
     * @param projectId 조회할 프로젝트 ID
     * @param pageable 페이지 정보
     * @return 랜딩 페이지 정보 DTO 목록 페이지
     */
    @GetMapping("/list/{projectId}")
    public ResponseEntity<ApiResponseDto<ListResponseDto<LandingPageResponseDto>>> getLandingPagesByProjectId(
            @PathVariable("projectId") Long projectId,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "createDate,desc") Pageable pageable,
            @AuthenticationPrincipal User userDetails) {

        // 로그인된 사용자 정보 확인
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("로그인이 필요합니다.", "UNAUTHORIZED"));
        }
        String userEmail = userDetails.getUserEmail();

        // 검색 타입 유효성 검사
        if (!searchTypeRegistry.isValidSearchType("landingPage", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }

        Page<LandingPageResponseDto> page = landingPageService.getLandingPagesByProjectId(projectId, status, searchType, searchKeyword, pageable, userEmail);
        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("landingPage");
        ListResponseDto<LandingPageResponseDto> responseDto = new ListResponseDto<>(page, availableSearchTypes);
        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }
    

    /**
     * 랜딩 페이지 수정
     * @param landingPageId 수정할 랜딩 페이지 ID
     * @param requestDto 수정 요청 DTO
     * @return 성공 응답 (본문 없음)
     */
    @PutMapping("/{landingPageId}")
    public ResponseEntity<ApiResponseDto<Void>> updateLandingPage(
            @PathVariable("landingPageId") Long landingPageId,
            @Valid @RequestBody LandingPageUpdateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {

        // 로그인된 사용자 정보 확인
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("로그인이 필요합니다.", "UNAUTHORIZED"));
        }
        String userEmail = userDetails.getUserEmail();

        landingPageService.updateLandingPage(landingPageId, requestDto, userEmail);
        return ResponseEntity.ok(ApiResponseDto.success(null));
    }

    /**
     * 랜딩 페이지 삭제
     * @param landingPageId 삭제할 랜딩 페이지 ID
     * @return 성공 응답 (본문 없음)
     */
    @DeleteMapping("/{landingPageId}")
    public ResponseEntity<ApiResponseDto<Void>> deleteLandingPage(
            @PathVariable("landingPageId") Long landingPageId,
            @AuthenticationPrincipal User userDetails) {

        // 로그인된 사용자 정보 확인
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("로그인이 필요합니다.", "UNAUTHORIZED"));
        }
        String userEmail = userDetails.getUserEmail();

        landingPageService.deleteLandingPage(landingPageId, userEmail);
        return ResponseEntity.ok(ApiResponseDto.success(null));
    }

    /**
     * 랜딩 페이지 복사
     * @param landingPageId 복사할 랜딩 페이지 ID
     * @param newPageTitle 새 랜딩 페이지의 제목 (선택적)
     * @return 복사된 랜딩 페이지 ID
     */
    @PostMapping("/{landingPageId}/copy")
    public ResponseEntity<ApiResponseDto<Long>> copyLandingPage(
            @PathVariable("landingPageId") Long landingPageId,
            @RequestParam(value = "newPageTitle", required = false) String newPageTitle,
            @AuthenticationPrincipal User userDetails) {

        // 로그인된 사용자 정보 확인
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("로그인이 필요합니다.", "UNAUTHORIZED"));
        }
        String userEmail = userDetails.getUserEmail();

        Long copiedLandingPageId = landingPageService.copyLandingPage(landingPageId, newPageTitle, userEmail);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success(copiedLandingPageId));
    }
}
