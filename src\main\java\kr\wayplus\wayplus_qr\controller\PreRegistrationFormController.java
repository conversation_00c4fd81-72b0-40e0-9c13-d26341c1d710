package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.dto.request.PreRegistrationFormCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.PreRegistrationFormUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.PreRegistrationFormResponseDto;
import kr.wayplus.wayplus_qr.dto.response.SearchTypeInfo;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.config.SearchTypeRegistry;
import kr.wayplus.wayplus_qr.service.PreRegistrationFormService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/way")
@RequiredArgsConstructor
public class PreRegistrationFormController {
    private final PreRegistrationFormService formService;
    private final SearchTypeRegistry searchTypeRegistry;

    @GetMapping("/pre-registration-forms/list/{projectId}")
    public ResponseEntity<ApiResponseDto<ListResponseDto<PreRegistrationFormResponseDto>>> getFormsByProject(
            @PathVariable("projectId") Long projectId,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "createDate,desc") Pageable pageable) {

        if (searchType != null && !searchTypeRegistry.isValidSearchType("preRegistrationForm", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }

        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("preRegistrationForm");
        Page<PreRegistrationFormResponseDto> page = formService.getFormsByProjectId(projectId, searchType, searchKeyword, pageable);
        ListResponseDto<PreRegistrationFormResponseDto> responseDto = new ListResponseDto<>(page, availableSearchTypes);

        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    @GetMapping("/pre-registration-forms/{formId}")
    public ResponseEntity<ApiResponseDto<PreRegistrationFormResponseDto>> getForm(
            @PathVariable("formId") Long formId) {
        PreRegistrationFormResponseDto dto = formService.getFormById(formId);
        return ResponseEntity.ok(ApiResponseDto.success(dto));
    }

    @PostMapping("/pre-registration-forms")
    public ResponseEntity<ApiResponseDto<Long>> createForm(
            @RequestBody PreRegistrationFormCreateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }
        Long formId = formService.createForm(requestDto, userDetails.getUserEmail());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success(formId));
    }

    @PutMapping("/pre-registration-forms/{formId}")
    public ResponseEntity<ApiResponseDto<PreRegistrationFormResponseDto>> updateForm(
            @PathVariable("formId") Long formId,
            @RequestBody PreRegistrationFormUpdateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }
        PreRegistrationFormResponseDto updated = formService.updateForm(formId, requestDto, userDetails.getUserEmail());
        return ResponseEntity.ok(ApiResponseDto.success(updated));
    }

    @DeleteMapping("/pre-registration-forms/remove/{formId}")
    public ResponseEntity<ApiResponseDto<Void>> deleteForm(
            @PathVariable("formId") Long formId,
            @AuthenticationPrincipal User userDetails) {
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }
        formService.deleteForm(formId, userDetails.getUserEmail());
        return ResponseEntity.ok(ApiResponseDto.success(null));
    }
    
    /**
     * 사전 신청서 양식 복사
     * @param formId 복사할 사전 신청서 양식 ID
     * @param userDetails 사용자 정보
     * @return 복사된 사전 신청서 양식 정보
     */
    @PostMapping("/pre-registration-forms/{formId}/copy")
    public ResponseEntity<ApiResponseDto<PreRegistrationFormResponseDto>> copyForm(
            @PathVariable("formId") Long formId,
            @AuthenticationPrincipal User userDetails) {
        
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }
        
        PreRegistrationFormResponseDto copiedForm = formService.copyForm(formId, userDetails.getUserEmail());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success(copiedForm));
    }
}
