package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.entity.InquiryAttachment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

@Mapper
public interface InquiryAttachmentMapper {

    /**
     * 첨부파일 생성
     * 
     * @param attachment 첨부파일 정보
     * @return 생성된 행 수
     */
    int insertAttachment(InquiryAttachment attachment);

    /**
     * ID로 첨부파일 조회
     * 
     * @param attachmentId 첨부파일 ID
     * @return 첨부파일 정보 (Optional)
     */
    Optional<InquiryAttachment> selectAttachmentById(Long attachmentId);

    /**
     * 문의 ID로 첨부파일 목록 조회
     * 
     * @param inquiryId 문의 ID
     * @return 첨부파일 목록
     */
    List<InquiryAttachment> selectAttachmentsByInquiryId(Long inquiryId);

    /**
     * 댓글 ID로 첨부파일 목록 조회
     * 
     * @param commentId 댓글 ID
     * @return 첨부파일 목록
     */
    List<InquiryAttachment> selectAttachmentsByCommentId(Long commentId);

    /**
     * 문의 ID 또는 댓글 ID로 첨부파일 목록 조회
     * 
     * @param inquiryId 문의 ID (nullable)
     * @param commentId 댓글 ID (nullable)
     * @return 첨부파일 목록
     */
    List<InquiryAttachment> selectAttachmentsByInquiryIdOrCommentId(
            @Param("inquiryId") Long inquiryId,
            @Param("commentId") Long commentId);

    /**
     * 첨부파일 삭제
     * 
     * @param attachmentId 첨부파일 ID
     * @return 삭제된 행 수
     */
    int deleteAttachment(Long attachmentId);

    /**
     * 문의에 속한 모든 첨부파일 삭제
     * 
     * @param inquiryId 문의 ID
     * @return 삭제된 행 수
     */
    int deleteAttachmentsByInquiryId(Long inquiryId);

    /**
     * 댓글에 속한 모든 첨부파일 삭제
     * 
     * @param commentId 댓글 ID
     * @return 삭제된 행 수
     */
    int deleteAttachmentsByCommentId(Long commentId);
    
    /**
     * 첨부파일 ID와 댓글 ID로 첨부파일 조회 (안전한 삭제를 위한 권한 검증용)
     * 
     * @param attachmentId 첨부파일 ID
     * @param commentId 댓글 ID
     * @return 첨부파일 정보 (Optional)
     */
    Optional<InquiryAttachment> selectAttachmentByIdAndCommentId(
            @Param("attachmentId") Long attachmentId, 
            @Param("commentId") Long commentId);
    
    /**
     * 첨부파일 ID로 첨부파일 삭제 (alias for deleteAttachment)
     * 
     * @param attachmentId 첨부파일 ID
     * @return 삭제된 행 수
     */
    default int deleteAttachmentById(Long attachmentId) {
        return deleteAttachment(attachmentId);
    }
}
