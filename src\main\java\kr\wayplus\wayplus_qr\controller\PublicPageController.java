package kr.wayplus.wayplus_qr.controller;

import jakarta.servlet.http.HttpServletRequest;
import kr.wayplus.wayplus_qr.entity.FormFieldDefinitionDto;
import kr.wayplus.wayplus_qr.dto.request.AttendeeRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.EventResponseDto;
import kr.wayplus.wayplus_qr.dto.response.PreRegistrationFormResponseDto;
import kr.wayplus.wayplus_qr.dto.response.PublicEventResponseDto;
import kr.wayplus.wayplus_qr.entity.PreRegistrationFormDefinitionDto;
import kr.wayplus.wayplus_qr.dto.response.LandingPageResponseDto;
import kr.wayplus.wayplus_qr.dto.response.AttendeeRegistrationResult;
import kr.wayplus.wayplus_qr.service.AttendeeService;
import kr.wayplus.wayplus_qr.service.EventService;
import kr.wayplus.wayplus_qr.service.LandingPageService;
import kr.wayplus.wayplus_qr.service.PreRegistrationFormService;
import kr.wayplus.wayplus_qr.entity.Attendee;
import kr.wayplus.wayplus_qr.entity.EventStatus;
import kr.wayplus.wayplus_qr.exception.CustomException;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/way/public")
@RequiredArgsConstructor
@Slf4j
public class PublicPageController {

    private final LandingPageService landingPageService;
    private final EventService eventService;
    private final PreRegistrationFormService preRegistrationFormService;
    private final AttendeeService attendeeService;

    /**
     * 공개 랜딩 페이지 조회 (인증 불필요)
     * @param landingPageId 조회할 랜딩 페이지 ID
     * @return 랜딩 페이지 정보 DTO
     */
    @GetMapping("/landing/{landingPageId}")
    public ResponseEntity<ApiResponseDto<LandingPageResponseDto>> getPublicLandingPageById(
        @PathVariable("landingPageId") Long landingPageId) {

        LandingPageResponseDto landingPageDto = landingPageService.getLandingPageById(landingPageId, null);

        if (landingPageDto == null) { 
             return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponseDto.error("LANDING_PAGE_NOT_FOUND", "랜딩 페이지를 찾을 수 없습니다."));
        }

        return ResponseEntity.ok(ApiResponseDto.success(landingPageDto));
    }

    /**
     * 공개 이벤트 조회 (인증 불필요)
     * QR 코드 스캔 등 외부 접근을 위해 사용됩니다.
     * @param eventId 조회할 이벤트 ID
     * @return 이벤트 정보 DTO
     */
    @GetMapping("/event/{eventId}")
    public ResponseEntity<ApiResponseDto<PublicEventResponseDto>> getPublicEventById(
        @PathVariable("eventId") Long eventId) {

        EventResponseDto eventDto = eventService.getEventById(eventId, null);

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startDate = eventDto.getStartDate();
        LocalDateTime endDate = eventDto.getEndDate();

        if (endDate != null && now.isAfter(endDate)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("EVENT_NOT_ACTIVE", "이벤트 운영 기간이 아닙니다."));
        } else if (startDate != null && now.isBefore(startDate)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("EVENT_NOT_ACTIVE", "이벤트 운영 기간이 아닙니다."));
        }

        if (EventStatus.FINISHED.equals(eventDto.getStatus()) || EventStatus.CANCELED.equals(eventDto.getStatus())) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponseDto.error("EVENT_NOT_FOUND", "조회된 이벤트가 없습니다."));
        }

        PreRegistrationFormResponseDto formDto = null;
        if (eventDto.getPreRegistrationFormId() != null) {
            try {
                formDto = preRegistrationFormService.getFormById(eventDto.getPreRegistrationFormId());
            } catch (Exception e) {
                // 폼 조회 실패 시 로그 남기고 무시 (이벤트 정보는 계속 반환)
            }
        }

        PublicEventResponseDto publicEvent = PublicEventResponseDto.builder()
            .eventId(eventDto.getEventId())
            .eventName(eventDto.getEventName())
            .teamName(eventDto.getTeamName())
            .teamCode(eventDto.getTeamCode())
            .description(eventDto.getDescription())
            .startDate(eventDto.getStartDate())
            .endDate(eventDto.getEndDate())
            .location(eventDto.getLocation())
            .participantLimit(eventDto.getParticipantLimit())
            .preRegistrationFormId(eventDto.getPreRegistrationFormId())
            .preRegistrationForm(formDto) 
            .linkedQrCodeId(eventDto.getLinkedQrCodeId())
            .eventImagePath(eventDto.getEventImagePath())
            .status(eventDto.getStatus())
            .createUserEmail(eventDto.getCreateUserEmail())
            .createDate(eventDto.getCreateDate())
            .build();

        return ResponseEntity.ok(ApiResponseDto.success(publicEvent));
    }


    /**
     * 공개 이벤트 사전 신청 저장 (인증 불필요)
     */
    @PostMapping("/event/{eventId}/attendees")
    public ResponseEntity<?> registerAttendee(
            @PathVariable("eventId") Long eventId,
            @RequestBody AttendeeRequestDto attendeeRequestDto,
            HttpServletRequest request) { 

        log.info("Received attendee registration request for event ID: {}, Attendee: {}", eventId, attendeeRequestDto.getAttendeeName());

        // 1. 이벤트 유효성 검사 (존재 여부, 기간 등)
        EventResponseDto eventDto = eventService.getEventById(eventId, null);
        if (eventDto == null) {
            return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponseDto.error("EVENT_NOT_FOUND", "유효한 이벤트를 찾을 수 없습니다."));
        }

        PreRegistrationFormResponseDto formDto = new PreRegistrationFormResponseDto();

        boolean shouldSkipValidation = Boolean.TRUE.equals(attendeeRequestDto.getSkipValidation());
        if (shouldSkipValidation) {
            log.info("Skipping validation for attendee registration: Event ID={}, Attendee={}", eventId, attendeeRequestDto.getAttendeeName());
        } else {
             // 2. 사전 등록 양식 유효성 검사 (존재 여부, 해당 이벤트에 연결되었는지 등)
            Long formId = attendeeRequestDto.getFormId();
            if (formId == null || formId == 0L) {
                log.error("Form ID is missing in the attendee registration request for event ID: {}", eventId);
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponseDto.error("FORM_INVALID", "유효한 사전 등록 양식을 찾을 수 없거나 이벤트에 연결되지 않았습니다."));
            }

            formDto = preRegistrationFormService.getFormById(formId);
            if (formDto == null) {
                log.warn("Pre-registration form not found: Form ID={}", attendeeRequestDto.getFormId());
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponseDto.error("FORM_NOT_FOUND", "사전 등록 양식을 찾을 수 없습니다."));
            }
            
            // 3. 입력 데이터 유효성 검사 (필수 필드, 형식 등) 
            PreRegistrationFormDefinitionDto formDefinition = preRegistrationFormService.getFormDefinitionById(formId);
            if (formDefinition != null && formDefinition.getFieldDefinitions() != null) {
                List<String> missingFieldLabels = new ArrayList<>();
                Map<String, Object> submissionData = attendeeRequestDto.getSubmissionData();

                if (submissionData == null) {
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponseDto.error("INVALID_SUBMISSION", "유효하지 않은 제출 데이터입니다."));
                }

                for (FormFieldDefinitionDto field : formDefinition.getFieldDefinitions()) {
                    if (field.isRequired()) {
                        Object value = null;
                        boolean missing = false;
                        String fieldType = field.getFieldType();

                        switch (fieldType) {
                            case "NAME":
                                value = attendeeRequestDto.getAttendeeName();
                                break;
                            case "TEL":
                                value = attendeeRequestDto.getAttendeeContact();
                                break;
                            case "EMAIL":
                                value = attendeeRequestDto.getAttendeeEmail();
                                break;
                            default: 
                                if (submissionData != null) {
                                    value = submissionData.get(field.getFieldName());
                                } else {
                                    missing = true;
                                }
                                break;
                        }

                        if (!missing && (value == null || (value instanceof String && ((String) value).trim().isEmpty()))) {
                            missing = true;
                        }

                        if (!missing && "EMAIL".equals(fieldType) && value instanceof String) {
                            String email = (String) value;
                            if (!email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,6}$")) {
                                missingFieldLabels.add(field.getFieldLabel() + " (유효하지 않은 형식)");
                                missing = true;
                            }
                        }

                        if (missing && !missingFieldLabels.contains(field.getFieldLabel() + " (유효하지 않은 형식)")) {
                            missingFieldLabels.add(field.getFieldLabel());
                        }
                    }
                }

                if (!missingFieldLabels.isEmpty()) {
                    String errorMessage = "다음 필수 항목을 확인해주세요: " + String.join(", ", missingFieldLabels);
                    return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                            .body(ApiResponseDto.error("INVALID_SUBMISSION", errorMessage));
                }
            }

            // 4. 개인정보 동의 여부 확인 (양식 설정에 따라)
            if ("Y".equals(formDto.getRequireConsent())) {
                log.warn("Consent not given for registration: Attendee={}", attendeeRequestDto.getAttendeeName());
                return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                        .body(ApiResponseDto.error("CONSENT_REQUIRED", "개인정보 처리방침 동의가 필요합니다."));
            }
        }

        // 5. 참석자 등록 처리 (서비스 호출)
        AttendeeRegistrationResult registrationResult;
        try {
            attendeeRequestDto.setEventId(eventId); // 서비스 호출 전에 eventId 설정 추가
            attendeeRequestDto.setTeamId(attendeeRequestDto.getTeamId());
            registrationResult = attendeeService.registerAttendee(attendeeRequestDto, formDto, eventDto);
        } catch (CustomException e) {
            log.warn("Custom exception during attendee registration: {}", e.getMessage());
            if (e.getErrorCode() == ErrorCode.TEAM_FULL) {
                return ResponseEntity.status(HttpStatus.CONFLICT)
                        .body(ApiResponseDto.error("TEAM_FULL", e.getMessage()));
            }
            return ResponseEntity.status(e.getErrorCode().getStatus())
                    .body(ApiResponseDto.error(e.getErrorCode().getCode(), e.getErrorCode().getMessage()));
        } catch (Exception e) {
            log.error("Error during attendee registration: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ApiResponseDto.error("REGISTRATION_FAILED", "참석자 등록 중 오류가 발생했습니다."));
        }

        // 6. 응답 데이터 구성
        Map<String, Object> responseBody = new HashMap<>();
        Attendee registeredAttendee = registrationResult.getAttendee(); 
        String qrImagePath = registrationResult.getQrImagePath(); 

        responseBody.put("message", formDto.getCompletionMessage() != null ? formDto.getCompletionMessage() : "사전 신청이 완료되었습니다.");
        responseBody.put("attendeeId", registeredAttendee.getAttendeeId());
        responseBody.put("confirmationCode", registeredAttendee.getConfirmationCode());

        // QR 코드 URL 생성 (절대 경로)
        String qrCodeUrl = null;
        if (qrImagePath != null && !qrImagePath.isEmpty()) { 
            String scheme = request.getScheme(); // http 또는 https
            String serverName = request.getServerName(); // 도메인 또는 IP
            int serverPort = request.getServerPort(); // 포트

            // 기본 포트(http 80, https 443)는 URL에 포함하지 않음
            String portString = "";
            if (!((scheme.equals("http") && serverPort == 80) || (scheme.equals("https") && serverPort == 443))) {
                portString = ":" + serverPort;
            }

            String baseUrl = scheme + "://" + serverName + portString;
            qrCodeUrl = baseUrl + "/qr-images/" + qrImagePath; 

            responseBody.put("qrCodeUrl", qrCodeUrl);
            log.info("Generated QR Code absolute URL for attendee {}: {}", registeredAttendee.getAttendeeId(), qrCodeUrl);
        } else {
            log.warn("QR code path is null or empty for attendee ID: {}. Not including QR code URL in response.", registeredAttendee.getAttendeeId());
            responseBody.put("qrCodeUrl", ""); 
        }

        return ResponseEntity.ok(responseBody); 
    }
}