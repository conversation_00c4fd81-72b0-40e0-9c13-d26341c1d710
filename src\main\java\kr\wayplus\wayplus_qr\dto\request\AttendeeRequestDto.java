package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Map;
import lombok.*;

/**
 * 사용자 사전신청자 정보를 담는 DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AttendeeRequestDto {
    private Long formId;
    private Long eventId;
    private Long teamId;
    private Map<String, Object> submissionData;
    private String attendeeName;
    private String attendeeContact;
    @Email(message = "이메일 형식이 아닙니다.")
    private String attendeeEmail;
    private String attendedConfirmYn;
    private String attendedYn;
    private Boolean skipValidation;
}
