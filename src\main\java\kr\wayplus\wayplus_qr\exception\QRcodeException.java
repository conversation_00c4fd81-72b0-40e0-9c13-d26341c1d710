package kr.wayplus.wayplus_qr.exception;

import lombok.Getter;

@Getter
public class QRcodeException extends RuntimeException {

    private final ErrorCode errorCode;

    public QRcodeException(ErrorCode errorCode) {
        super(errorCode.getMessage()); // 부모 클래스(RuntimeException)의 생성자를 호출하여 메시지 설정
        this.errorCode = errorCode;
    }

    // 필요에 따라 메시지를 직접 설정하는 생성자 추가 가능
    public QRcodeException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }

    // 원인 예외(cause)를 포함하는 생성자 추가
    public QRcodeException(ErrorCode errorCode, String message, Throwable cause) {
        super(message, cause); // 부모 클래스의 생성자를 호출하여 메시지와 원인 예외 설정
        this.errorCode = errorCode;
    }
}
