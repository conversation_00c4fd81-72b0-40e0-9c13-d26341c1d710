package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.dto.AttendeeManualCheckDto;
import kr.wayplus.wayplus_qr.dto.request.UpdateAttendeesConfirmStatusRequestDto;
import kr.wayplus.wayplus_qr.dto.request.UpdateManualAttendanceRequestDto;
import kr.wayplus.wayplus_qr.dto.request.RedeemBenefitRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.AttendeeRedemptionDetailsResponseDto;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.exception.CustomEventException;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.service.AdminAttendeeRedemptionService;
import kr.wayplus.wayplus_qr.service.AttendeeService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * Admin Attendee Controller
 * 
 * This class handles attendee-related operations for administrators.
 * 
 * <AUTHOR> Name]
 */
@Slf4j
@RestController
@RequestMapping("/api/way") // 기본 URL 설정
@RequiredArgsConstructor
@Validated
public class AdminAttendeeController {

    /**
     * Attendee Service instance
     */
    private final AttendeeService attendeeService;
    private final AdminAttendeeRedemptionService redemptionService;

    /**
     * QR 코드 스캔을 통해 참석을 기록합니다.
     * 응답은 항상 ApiResponseDto 형식을 따릅니다.
     *
     * @param confirmationCode QR 코드에 포함된 고유 확인 코드
     * @param userDetails      인증된 사용자 정보
     * @return ResponseEntity<ApiResponseDto<?>> 형식의 응답
     */
    @PutMapping("/admin/attendees/attend/{confirmationCode}")
    public ResponseEntity<ApiResponseDto<?>> markAttendanceByQrCode(
            @PathVariable("confirmationCode") String confirmationCode,
            @AuthenticationPrincipal User userDetails) {
        String adminEmail = userDetails.getUserEmail();
        log.info("[Admin] Received request from {} to mark attendance via QR code: {}", adminEmail, confirmationCode);
        try {
            attendeeService.markAttendanceByCode(confirmationCode, adminEmail);
            log.info("[Admin] Successfully marked attendance for code: {}", confirmationCode);
            // 성공 시 고정된 메시지 반환
            return ResponseEntity.ok(ApiResponseDto.success("참석 처리가 완료되었습니다."));
        } catch (CustomEventException e) {
            ErrorCode errorCode = e.getErrorCode();
            log.warn("[Admin] Failed to mark attendance for code {}: {} ({})", confirmationCode, e.getMessage(), errorCode.getCode());
            // CustomEventException 발생 시 ErrorCode에 정의된 상태 코드와 메시지로 에러 응답 생성
            return ResponseEntity.status(errorCode.getStatus())
                    .body(ApiResponseDto.error(errorCode.getCode(), errorCode.getMessage())); // e.getMessage() 대신 errorCode.getMessage() 사용 권장 (일관성)
        } catch (Exception e) {
            // 예상치 못한 다른 예외 처리
            log.error("[Admin] Unexpected error marking attendance for code {}: {}", confirmationCode, e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponseDto.error(ErrorCode.INTERNAL_SERVER_ERROR.getCode(), ErrorCode.INTERNAL_SERVER_ERROR.getMessage()));
        }
    }

    /**
     * 수동 참석 확인을 위한 참석자 검색 (관리자용)
     * 
     * @param eventId 필수 이벤트 ID
     * @param name 선택적 참석자 이름 필터
     * @return 검색된 참석자 목록
     */
    @GetMapping("/admin/attendees/search")
    public ResponseEntity<ApiResponseDto<List<AttendeeManualCheckDto>>> searchAttendeesForManualCheck(
            @RequestParam("eventId") Long eventId,
            @RequestParam(required = false) String name) {
        log.info("[Admin] Received request to search attendees for manual check. Event ID: {}, Name filter: '{}'", eventId, name);
        List<AttendeeManualCheckDto> attendees = attendeeService.searchAttendeesForManualCheck(eventId, name);
        log.info("[Admin] Found {} attendees for manual check.", attendees.size());
        return ResponseEntity.ok(ApiResponseDto.success(attendees));
    }

    /**
     * 수동으로 참석 확인 (관리자용)
     * 
     * @param attendeeId 참석 처리할 참석자의 ID
     * @param requestDto 참석 상태 정보
     * @return ResponseEntity<ApiResponseDto<Void>>
     */
    @PutMapping("/admin/attendees/attend/manual/{attendeeId}")
    public ResponseEntity<ApiResponseDto<Void>> markAttendanceManually(@PathVariable("attendeeId") Long attendeeId,
                                                                     @RequestBody UpdateManualAttendanceRequestDto requestDto) { // DTO 파라미터 추가
        log.info("[Admin] Received request to manually update attendance status for attendee ID: {} to attended={}", attendeeId, requestDto.getAttended()); // isAttended -> getAttended
        try {
            attendeeService.updateAttendanceStatusManually(attendeeId, requestDto.getAttended()); // 서비스 메서드 호출 수정, isAttended -> getAttended
            log.info("[Admin] Successfully manually updated attendance status for attendee ID: {} to attended={}", attendeeId, requestDto.getAttended()); // isAttended -> getAttended
            return ResponseEntity.ok(ApiResponseDto.success(null));
        } catch (Exception e) {
            log.error("[Admin] Error manually updating attendance status for attendee ID {}: {}", attendeeId, e.getMessage());
            throw e; // GlobalExceptionHandler에게 위임
        }
    }

    /**
     * 여러 참가자의 참석 확정 상태 (attended_confirm_yn)를 일괄 업데이트합니다.
     * 
     * @param requestDto 업데이트할 참가자 ID 목록과 상태 값 ('Y' 또는 'N')
     * @return 성공 또는 실패 응답
     */
    @PutMapping("/admin/attendees/confirm-status")
    public ResponseEntity<ApiResponseDto<Void>> updateAttendeesConfirmStatus(@Valid @RequestBody UpdateAttendeesConfirmStatusRequestDto requestDto) {
        log.info("[Admin] Received request to update attended_confirm_yn to '{}' for attendees: {}",
                requestDto.getAttendedConfirmYn(), requestDto.getAttendeeIds());
        try {
            attendeeService.updateAttendeesConfirmStatus(requestDto);
            log.info("[Admin] Successfully updated attended_confirm_yn.");
            return ResponseEntity.ok(ApiResponseDto.success(null));
        } catch (IllegalArgumentException e) {
            log.warn("[Admin] Invalid argument for updating confirm status: {}", e.getMessage());
            return ResponseEntity.badRequest()
                    .body(ApiResponseDto.error("INVALID_INPUT", e.getMessage()));
        } catch (Exception e) {
            log.error("[Admin] Unexpected error updating confirm status: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError()
                    .body(ApiResponseDto.error(ErrorCode.INTERNAL_SERVER_ERROR.getCode(), "참가자 상태 업데이트 중 오류가 발생했습니다."));
        }
    }

    /**
     * 스캔된 QR 코드로 참가자 및 혜택 현황 조회
     */
    @GetMapping("/admin/attendee-redemption/details/{confirmationCode}")
    public ResponseEntity<ApiResponseDto<AttendeeRedemptionDetailsResponseDto>> getAttendeeRedemptionDetails(
            @PathVariable("confirmationCode") String confirmationCode,
            @AuthenticationPrincipal User userDetails) {
        AttendeeRedemptionDetailsResponseDto details = redemptionService.getRedemptionDetails(confirmationCode);
        return ResponseEntity.ok(ApiResponseDto.success(details));
    }

    /**
     * 특정 혜택 사용(승인) 처리
     */
    @PostMapping("/admin/attendee-redemption/redeem")
    public ResponseEntity<ApiResponseDto<?>> redeemBenefit(
            @Valid @RequestBody RedeemBenefitRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {
        String adminEmail = userDetails.getUserEmail();
        try {
            redemptionService.redeemBenefit(requestDto.getConfirmationCode(), requestDto.getBenefitId(), adminEmail);
            return ResponseEntity.ok(ApiResponseDto.success("혜택 사용 처리가 완료되었습니다."));
        } catch (CustomEventException e) {
            ErrorCode errorCode = e.getErrorCode();
            return ResponseEntity.status(errorCode.getStatus())
                    .body(ApiResponseDto.error(errorCode.getCode(), errorCode.getMessage()));
        }
    }

    /**
     * 특정 혜택 사용 취소 처리
     */
    @DeleteMapping("/admin/attendee-redemption/cancel/{confirmationCode}/{benefitId}")
    public ResponseEntity<ApiResponseDto<?>> cancelBenefitRedemption(
            @PathVariable("confirmationCode") String confirmationCode,
            @PathVariable("benefitId") Long benefitId,
            @AuthenticationPrincipal User userDetails) {
        String adminEmail = userDetails.getUserEmail();
        try {
            redemptionService.cancelBenefitRedemption(confirmationCode, benefitId, adminEmail);
            return ResponseEntity.ok(ApiResponseDto.success(null));
        } catch (CustomEventException e) {
            ErrorCode errorCode = e.getErrorCode();
            return ResponseEntity.status(errorCode.getStatus())
                    .body(ApiResponseDto.error(errorCode.getCode(), errorCode.getMessage()));
        }
    }
}
