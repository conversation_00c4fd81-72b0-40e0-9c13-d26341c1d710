package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.request.EventCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.EventUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.EventResponseDto;
import kr.wayplus.wayplus_qr.entity.Event;
import kr.wayplus.wayplus_qr.exception.CustomEventException;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.mapper.EventMapper;
import kr.wayplus.wayplus_qr.mapper.EventBenefitMapper;
import kr.wayplus.wayplus_qr.mapper.ProjectMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import kr.wayplus.wayplus_qr.entity.enums.UserRole;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectReader;
import com.fasterxml.jackson.databind.DeserializationFeature;
import kr.wayplus.wayplus_qr.dto.request.BenefitCreateDto;
import kr.wayplus.wayplus_qr.entity.EventBenefit;
import kr.wayplus.wayplus_qr.entity.EventStatus;
import kr.wayplus.wayplus_qr.dto.response.EventBenefitResponseDto;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.stream.Collectors;
import java.util.function.Function;

@Service
@RequiredArgsConstructor
@Slf4j
public class EventService {
    private final EventMapper eventMapper;
    private final ProjectMapper projectMapper;
    private final UserMapper userMapper;
    private final EventBenefitMapper eventBenefitMapper;
    private final ObjectMapper objectMapper;
    private final FileStorageService fileStorageService;

    // 이벤트 정렬 필드 매핑 (API 요청 필드명 -> DB 컬럼명)
    private static final Map<String, String> EVENT_SORT_PROPERTY_MAP = Map.of(
            "eventId", "event_id",
            "projectId", "project_id",
            "eventName", "event_name",
            "status", "status",
            "startDate", "start_date",
            "endDate", "end_date",
            "createDate", "create_date",
            "lastUpdateDate", "last_update_date"
            // 필요에 따라 추가
    );

    /**
     * 이벤트 목록 조회 (페이징/검색/필터/정렬) - Pageable 사용
     */
    @Transactional(readOnly = true)
    public Page<EventResponseDto> getEvents(
            Long projectId,
            String status,
            String searchType,
            String searchKeyword,
            Pageable pageable,
            String userEmail) {
        // 프로젝트 존재 및 권한 확인
        if (projectId != null) {
            projectMapper.selectProjectById(projectId)
                    .orElseThrow(() -> new CustomEventException(ErrorCode.PROJECT_NOT_FOUND, "프로젝트를 찾을 수 없습니다: ID=" + projectId));
        }

        // 권한 확인
        String userRole = userMapper.selectUserRoleByEmail(userEmail)
                .orElseThrow(() -> new CustomEventException(ErrorCode.USER_NOT_FOUND, "사용자를 찾을 수 없습니다: " + userEmail));
        if (!userRole.equals(UserRole.SUPER_ADMIN.name())) {
            int membershipCount = userMapper.countUserProjectMembership(userEmail, projectId);
            if (membershipCount == 0) {
                log.warn("Access denied for user {} on project {}", userEmail, projectId);
                throw new CustomEventException(ErrorCode.ACCESS_DENIED, "해당 프로젝트에 대한 접근 권한이 없습니다.");
            }
        }

        //검색타입 설정. 실제 DB 컬럼과 연동.
        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "eventName":   searchColumn = "e.event_name"; break;
                case "description": searchColumn = "e.description"; break;
                case "startDate":   searchColumn = "e.start_date"; break;
                case "endDate":     searchColumn = "e.end_date"; break;
                case "preRegistrationFormName": searchColumn = "prf.form_name"; break;
            }
        }
        
        List<Event> events = eventMapper.selectEvents(projectId, status, searchColumn, searchKeyword, pageable);
        int total = eventMapper.countEvents(projectId, status, searchColumn, searchKeyword);

        List<EventResponseDto> items = events.stream()
                .map(EventResponseDto::fromEntity)
                .collect(Collectors.toList());

        return new PageImpl<>(items, pageable, total);
    }

    /**
     * SUPER_ADMIN: 모든 또는 특정 프로젝트의 이벤트 목록 조회 (페이징 및 동적 정렬)
     */
    @Transactional(readOnly = true)
    public Page<EventResponseDto> getAllEventsForSuperAdmin(Long projectId, String searchType, String searchKeyword, Pageable pageable) {
        List<Event> events;
        long total;

        //검색타입 설정. 실제 DB 컬럼과 연동.
        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "eventName": searchColumn = "event_name"; break;
                case "description": searchColumn = "description"; break;
                case "startDate": searchColumn = "start_date"; break;
                case "endDate": searchColumn = "end_date"; break;
                case "preRegistrationFormName": searchColumn = "form_name"; break;
            }
        }

        if (projectId != null) {
            // 특정 프로젝트 ID로 필터링
            events = eventMapper.selectEventsByProjectIdForSuperAdmin(projectId, searchColumn, searchKeyword, pageable);
            total = eventMapper.countEventsByProjectIdForSuperAdmin(projectId, searchColumn, searchKeyword);
        } else {
            // 모든 이벤트 조회
            events = eventMapper.selectAllEventsForSuperAdmin(searchColumn, searchKeyword, pageable);
            total = eventMapper.countAllEventsForSuperAdmin(searchColumn, searchKeyword);
        }

        List<EventResponseDto> dtos = events.stream()
                .map(EventResponseDto::fromEntity)
                .collect(Collectors.toList());

        return new PageImpl<>(dtos, pageable, total);
    }

    /**
     * 이벤트 생성
     */
    @Transactional
    public Long createEvent(EventCreateRequestDto dto, String userEmail) {
        // 기간 유효성 검사: 시작일자가 종료일자 이후인 경우 예외 처리
        if (dto.getStartDate() != null && dto.getEndDate() != null && dto.getStartDate().isAfter(dto.getEndDate())) {
            throw new CustomEventException(ErrorCode.INVALID_INPUT_VALUE, "시작일자는 종료일자 이전이어야 합니다.");
        }
        try {
            // 프로젝트 존재 및 권한 확인
            projectMapper.selectProjectById(dto.getProjectId())
                    .orElseThrow(() -> new CustomEventException(ErrorCode.PROJECT_NOT_FOUND, "프로젝트를 찾을 수 없습니다: ID=" + dto.getProjectId()));
            
            String userRole = userMapper.selectUserRoleByEmail(userEmail)
                    .orElseThrow(() -> new CustomEventException(
                            ErrorCode.USER_NOT_FOUND,
                            "사용자를 찾을 수 없습니다: " + userEmail));
            if (!UserRole.SUPER_ADMIN.name().equals(userRole)) {
                int membershipCount = userMapper.countUserProjectMembership(userEmail, dto.getProjectId());
                if (membershipCount == 0) {
                    log.warn("Permission denied for user {} to project {}", userEmail, dto.getProjectId());
                    throw new CustomEventException(ErrorCode.ACCESS_DENIED, "해당 프로젝트에 대한 접근 권한이 없습니다.");
                }
            }

            // 이미지 파일 처리
            String eventImagePath = null;
            MultipartFile eventImageFile = dto.getEventImageFile();
            if (eventImageFile != null && !eventImageFile.isEmpty()) {
                try {
                    String storedFileName = fileStorageService.storeFile(eventImageFile);
                    // DB에 저장될 경로 생성 (/uploads/ 접두사 추가)
                    eventImagePath = "/uploads/" + storedFileName;
                    log.info("Stored event image file, path: {}", eventImagePath);
                } catch (Exception e) {
                    log.error("Failed to save event image file", e);
                    // 이미지 저장 실패는 중대한 오류가 아니므로 진행
                }
            }

            Event event = Event.builder()
                    .projectId(dto.getProjectId())
                    .teamId(dto.getTeamId())
                    .eventName(dto.getEventName())
                    .description(dto.getDescription())
                    .startDate(dto.getStartDate())
                    .endDate(dto.getEndDate())
                    .location(dto.getLocation())
                    .participantLimit(dto.getParticipantLimit())
                    .preRegistrationFormId(dto.getPreRegistrationFormId())
                    .linkedQrCodeId(dto.getLinkedQrCodeId())
                    .status(dto.getStatus())
                    .eventImagePath(eventImagePath)
                    .createUserEmail(userEmail)
                    .createDate(LocalDateTime.now())
                    .useYn("Y")
                    .deleteYn("N")
                    .build();
            int result = eventMapper.insertEvent(event);
            if (result == 0) {
                log.error("Event creation failed for project {}", dto.getProjectId());
                throw new CustomEventException(ErrorCode.INTERNAL_SERVER_ERROR, "이벤트 생성 실패");
            }

            // -------- 혜택 JSON 처리 --------
            String benefitsJson = dto.getBenefitsJson();
            if (benefitsJson != null && !benefitsJson.isBlank()) {
                try {
                    ObjectReader reader = objectMapper
                            .readerFor(new TypeReference<List<BenefitCreateDto>>() {})
                            .with(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
                    List<BenefitCreateDto> benefitDtos = reader.readValue(benefitsJson);
                    java.util.Set<String> codesInRequest = new java.util.HashSet<>();
                    for (BenefitCreateDto b : benefitDtos) {
                        // 로그 추가: 어떤 benefitCode와 eventId로 중복 검사를 하는지 확인
                        log.info("Checking duplicate benefit code: {}, eventId: {}", b.getBenefitCode(), event.getEventId());
                        // 요청 내 중복
                        if (!codesInRequest.add(b.getBenefitCode())) {
                            throw new CustomEventException(ErrorCode.BENEFIT_CODE_DUPLICATE_IN_REQUEST);
                        }
                        // 로그 추가: isPresent() 결과 확인
                        log.info("Benefit code '{}' check result: isPresent={}", b.getBenefitCode(), eventBenefitMapper.selectBenefitByCodeAndEventId(b.getBenefitCode(), event.getEventId()).isPresent());
                        // DB 중복
                        if (eventBenefitMapper.selectBenefitByCodeAndEventId(b.getBenefitCode(), event.getEventId()).isPresent()) {
                            throw new CustomEventException(ErrorCode.BENEFIT_CODE_ALREADY_EXISTS);
                        }
                        EventBenefit eb = EventBenefit.builder()
                                .eventId(event.getEventId())
                                .benefitCode(b.getBenefitCode())
                                .benefitName(b.getBenefitName())
                                .description(b.getDescription())
                                .quantity(b.getQuantity())
                                .status("ACTIVE")
                                .useYn("Y")
                                .deleteYn("N")
                                .createUserEmail(userEmail)
                                .createDate(java.time.LocalDateTime.now())
                                .build();
                        eventBenefitMapper.insertEventBenefit(eb);
                    }
                } catch (JsonProcessingException e) {
                    throw new CustomEventException(ErrorCode.BENEFIT_JSON_PARSE_ERROR);
                }
            }
            // 이미지는 이미 위에서 처리되어 Event 객체에 설정됨

            return event.getEventId();
        } catch (Exception e) {
            log.error("Error creating event: {}", e.getMessage(), e);
            throw new CustomEventException(ErrorCode.EVENT_CREATE_FAILED, "이벤트 생성 중 오류가 발생했습니다.");
        }
    }

    /**
     * 이벤트 상세 조회
     */
    @Transactional(readOnly = true)
    public EventResponseDto getEventById(Long eventId, String userEmail) {
        // 이벤트 존재 확인
        Event event = eventMapper.selectEventById(eventId)
                .orElseThrow(() -> new CustomEventException(ErrorCode.EVENT_NOT_FOUND));

        // 프로젝트 권한 확인
        if (userEmail != null) {
            String userRole = userMapper.selectUserRoleByEmail(userEmail)
                    .orElseThrow(() -> new CustomEventException(
                            ErrorCode.USER_NOT_FOUND,
                            "사용자를 찾을 수 없습니다: " + userEmail));
            if (!UserRole.SUPER_ADMIN.name().equals(userRole)) {
                int membershipCount = userMapper.countUserProjectMembership(userEmail, event.getProjectId());
                if (membershipCount == 0) {
                    log.warn("Access denied for user {} trying to access event {}", userEmail, eventId);
                    throw new CustomEventException(ErrorCode.ACCESS_DENIED, "해당 이벤트에 대한 접근 권한이 없습니다.");
                }
            }
        }

        // 이벤트에 연결된 활성 혜택 목록 조회
        List<EventBenefit> benefits = eventBenefitMapper.selectActiveBenefitsByEventId(eventId);
        List<EventBenefitResponseDto> benefitDtos = benefits.stream()
                .map(EventBenefitResponseDto::fromEntity)
                .collect(Collectors.toList());

        // EventResponseDto 생성 시 혜택 목록 포함
        return EventResponseDto.builder()
                .teamId(event.getTeamId())
                .eventId(event.getEventId())
                .projectId(event.getProjectId())
                .eventName(event.getEventName())
                .teamName(event.getTeamName())
                .teamCode(event.getTeamCode())
                .description(event.getDescription())
                .startDate(event.getStartDate())
                .endDate(event.getEndDate())
                .location(event.getLocation())
                .participantLimit(event.getParticipantLimit())
                .preRegistrationFormId(event.getPreRegistrationFormId())
                .preRegistrationFormName(event.getPreRegistrationFormName()) // Assuming Event entity has this field populated
                .linkedQrCodeId(event.getLinkedQrCodeId())
                .status(event.getStatus())
                .eventImagePath(event.getEventImagePath())
                .createUserEmail(event.getCreateUserEmail())
                .createDate(event.getCreateDate())
                .benefits(benefitDtos) // Add benefits list here
                .build();
    }

    /**
     * 이벤트 수정
     */
    @Transactional
    public EventResponseDto updateEvent(Long eventId, EventUpdateRequestDto dto, String userEmail) {
        // 기간 유효성 검사: 시작일자가 종료일자 이후인 경우 예외 처리
        if (dto.getStartDate() != null && dto.getEndDate() != null && dto.getStartDate().isAfter(dto.getEndDate())) {
            throw new CustomEventException(ErrorCode.INVALID_INPUT_VALUE, "시작일자는 종료일자 이전이어야 합니다.");
        }

        Event event = eventMapper.selectEventById(eventId)
                .orElseThrow(() -> new CustomEventException(
                        ErrorCode.EVENT_NOT_FOUND,
                        "이벤트를 찾을 수 없습니다: ID=" + eventId));

        // 권한 확인
        String userRole = userMapper.selectUserRoleByEmail(userEmail)
                .orElseThrow(() -> new CustomEventException(
                        ErrorCode.USER_NOT_FOUND,
                        "사용자를 찾을 수 없습니다: " + userEmail));
        if (!UserRole.SUPER_ADMIN.name().equals(userRole)) {
            int membershipCount = userMapper.countUserProjectMembership(userEmail, event.getProjectId());
            if (membershipCount == 0) {
                throw new CustomEventException(
                        ErrorCode.ACCESS_DENIED,
                        "해당 프로젝트에 대한 접근 권한이 없습니다.");
            }
        }

        // 기존 이미지 경로 저장
        String currentImagePath = event.getEventImagePath();
        String imagePathToUpdate = currentImagePath; // DB에 업데이트될 최종 경로, 기본값은 현재 경로

        // 1. 이미지 제거 요청 처리 (removeImage == true)
        if (dto.getRemoveImage() != null && dto.getRemoveImage()) {
            log.info("Removing event image for event ID: {}", eventId);
            if (currentImagePath != null && !currentImagePath.isEmpty()) {
                // 기존 파일 삭제 요청 (DB 경로에서 /uploads/ 제거 후 파일명만 전달)
                String filenameToDelete = currentImagePath.replace("/uploads/", "");
                fileStorageService.deleteFile(filenameToDelete);
            }
            imagePathToUpdate = null; // DB 경로를 null로 설정
        } else {
            // 2. 새 이미지 파일 업로드 처리 (removeImage == false 또는 null 이고, 새 파일 존재)
            MultipartFile updateImageFile = dto.getEventImageFile();
            if (updateImageFile != null && !updateImageFile.isEmpty()) {
                log.info("Updating event image for event ID: {}", eventId);
                // 기존 이미지가 있으면 삭제
                if (currentImagePath != null && !currentImagePath.isEmpty()) {
                    // DB 경로에서 /uploads/ 제거 후 파일명만 전달
                    String filenameToDelete = currentImagePath.replace("/uploads/", "");
                    fileStorageService.deleteFile(filenameToDelete);
                }
                // 새 파일 저장 및 경로 설정
                String storedFileName = fileStorageService.storeFile(updateImageFile);
                // DB에 저장될 경로 생성 (/uploads/ 접두사 추가)
                imagePathToUpdate = "/uploads/" + storedFileName;
                log.info("Stored new image file, path to update: {}", imagePathToUpdate);
            }
            // 3. 이미지 변경 없음 (removeImage == false 또는 null 이고, 새 파일 없음)
            // 이 경우 imagePathToUpdate는 초기값인 currentImagePath 유지
        }

        // 이벤트 정보 업데이트
        event.setTeamId(dto.getTeamId());
        event.setEventName(dto.getEventName());
        event.setDescription(dto.getDescription());
        event.setStartDate(dto.getStartDate());
        event.setEndDate(dto.getEndDate());
        event.setLocation(dto.getLocation());
        event.setParticipantLimit(dto.getParticipantLimit());
        event.setPreRegistrationFormId(dto.getPreRegistrationFormId());
        event.setLinkedQrCodeId(dto.getLinkedQrCodeId());
        event.setStatus(dto.getStatus());
        event.setUpdateUserEmail(userEmail);
        event.setLastUpdateDate(LocalDateTime.now());
        event.setEventImagePath(imagePathToUpdate); // 최종 이미지 경로 설정

        // DB 업데이트
        int result = eventMapper.updateEvent(event);
        if (result == 0) {
            throw new CustomEventException(
                    ErrorCode.EVENT_NOT_FOUND,
                    "이벤트를 수정할 수 없습니다: ID=" + eventId);
        }

        // -------- 혜택 정보 업데이트 로직 시작 --------
        String benefitsJson = dto.getBenefitsJson();
        log.info("Updating benefits for event ID: {}. Received benefitsJson: {}", eventId, benefitsJson);

        if (benefitsJson != null && !benefitsJson.isBlank()) {
            try {
                ObjectReader reader = objectMapper
                        .readerFor(new TypeReference<List<BenefitCreateDto>>() {})
                        .with(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT);
                List<BenefitCreateDto> benefitDtos = reader.readValue(benefitsJson);
                log.info("Parsed {} benefits from JSON for event ID: {}", benefitDtos.size(), eventId);

                // 1) 기존(미삭제) 혜택 조회 -> Map<benefitId, EventBenefit>
                List<EventBenefit> existingBenefits = eventBenefitMapper.selectNonDeletedBenefitsByEventId(eventId);
                Map<Long, EventBenefit> existingMapById = existingBenefits.stream()
                        .collect(Collectors.toMap(EventBenefit::getBenefitId, Function.identity()));
                // benefitCode 중복 검사를 위해 코드-Id 매핑도 생성
                Map<String, Long> existingCodeToId = existingBenefits.stream()
                        .collect(Collectors.toMap(EventBenefit::getBenefitCode, EventBenefit::getBenefitId));

                // 2) 요청 내 중복 체크 및 Add / Update 처리
                Set<String> codesInRequest = new HashSet<>();
                Set<Long> idsInRequest = new HashSet<>();

                for (BenefitCreateDto b : benefitDtos) {
                    // 프론트에서 eventBenefitId 또는 benefitId 필드 중 하나로 보낼 수 있음
                    Long benefitIdFromDto = b.getEventBenefitId() != null ? b.getEventBenefitId() : b.getBenefitId();

                    // 요청 내 benefitCode 중복 검사
                    if (!codesInRequest.add(b.getBenefitCode())) {
                        log.warn("Duplicate benefit code '{}' found in request for event ID: {}", b.getBenefitCode(), eventId);
                        throw new CustomEventException(ErrorCode.BENEFIT_CODE_DUPLICATE_IN_REQUEST);
                    }

                    // Update 경로: benefitId가 존재 -> 기존 존재 여부 확인
                    if (benefitIdFromDto != null) {
                        idsInRequest.add(benefitIdFromDto);
                        EventBenefit existing = existingMapById.get(benefitIdFromDto);
                        if (existing == null) {
                            log.warn("Benefit not found for ID {} in event ID {}", benefitIdFromDto, eventId);
                            throw new CustomEventException(ErrorCode.BENEFIT_NOT_FOUND);
                        }

                        // benefitCode 변경 허용 -> 이벤트 내 중복 검사
                        Long idWithSameCode = existingCodeToId.get(b.getBenefitCode());
                        if (idWithSameCode != null && !idWithSameCode.equals(benefitIdFromDto)) {
                            log.warn("benefitCode '{}' already exists in another benefit (id={}) for event ID {}", b.getBenefitCode(), idWithSameCode, eventId);
                            throw new CustomEventException(ErrorCode.BENEFIT_CODE_ALREADY_EXISTS);
                        }

                        // ---- 수정(Update) ----
                        existing.setBenefitCode(b.getBenefitCode());
                        existing.setBenefitName(b.getBenefitName());
                        existing.setDescription(b.getDescription());
                        existing.setQuantity(b.getQuantity());
                        existing.setUpdateUserEmail(userEmail);
                        existing.setLastUpdateDate(LocalDateTime.now());
                        eventBenefitMapper.updateEventBenefit(existing);
                        log.debug("Updated benefit id {} (code '{}') for event ID {}", benefitIdFromDto, b.getBenefitCode(), eventId);
                    } else {
                        if (existingCodeToId.containsKey(b.getBenefitCode())) {
                            log.warn("benefitCode '{}' already exists for event ID {}", b.getBenefitCode(), eventId);
                            throw new CustomEventException(ErrorCode.BENEFIT_CODE_ALREADY_EXISTS);
                        }

                        EventBenefit newBenefit = EventBenefit.builder()
                                .eventId(eventId)
                                .benefitCode(b.getBenefitCode())
                                .benefitName(b.getBenefitName())
                                .description(b.getDescription())
                                .quantity(b.getQuantity())
                                .status("ACTIVE")
                                .useYn("Y")
                                .deleteYn("N")
                                .createUserEmail(userEmail)
                                .createDate(LocalDateTime.now())
                                .build();
                        eventBenefitMapper.insertEventBenefit(newBenefit);
                        log.debug("Inserted new benefit code '{}' for event ID {}", b.getBenefitCode(), eventId);
                        // 새 코드 추가해 두어 이어지는 루프에서 중복 체크에 반영
                        existingCodeToId.put(b.getBenefitCode(), newBenefit.getBenefitId());
                    }
                }

                // 3) 불일치 검사: 기존 혜택 중 요청에 없는 ID 확인
                Set<Long> missingIds = existingMapById.keySet().stream()
                        .filter(id -> !idsInRequest.contains(id))
                        .collect(Collectors.toSet());
                if (!missingIds.isEmpty()) {
                    log.error("Benefit inconsistency detected. Missing ids {} for event ID: {}", missingIds, eventId);
                    throw new CustomEventException(ErrorCode.BENEFIT_INCONSISTENCY_DETECTED,
                            "요청에 누락된 혜택 ID: " + missingIds.stream().map(String::valueOf).collect(Collectors.joining(", ")));
                }
            } catch (JsonProcessingException e) {
                log.error("Error parsing benefitsJson for event ID: {}", eventId, e);
                throw new CustomEventException(ErrorCode.BENEFIT_JSON_PARSE_ERROR);
            }
        } else {
            log.info("No benefitsJson provided or it's blank for event ID: {}. Skipping benefit update.", eventId);
        }

        return getEventById(eventId, userEmail);
    }

    /**
     * 이벤트 삭제
     */
    @Transactional
    public void deleteEvent(Long eventId, String userEmail) {
        Event event = eventMapper.selectEventById(eventId)
                .orElseThrow(() -> new CustomEventException(
                        ErrorCode.EVENT_NOT_FOUND,
                        "이벤트를 찾을 수 없습니다: ID=" + eventId));
        // 권한 확인
        String userRole = userMapper.selectUserRoleByEmail(userEmail)
                .orElseThrow(() -> new CustomEventException(
                        ErrorCode.USER_NOT_FOUND,
                        "사용자를 찾을 수 없습니다: " + userEmail));
        if (!UserRole.SUPER_ADMIN.name().equals(userRole)) {
            int membershipCount = userMapper.countUserProjectMembership(userEmail, event.getProjectId());
            if (membershipCount == 0) {
                throw new CustomEventException(
                        ErrorCode.ACCESS_DENIED,
                        "해당 프로젝트에 대한 접근 권한이 없습니다.");
            }
        }

        int result = eventMapper.logicalDeleteEvent(eventId, userEmail);
        if (result == 0) {
            throw new CustomEventException(
                    ErrorCode.EVENT_NOT_FOUND,
                    "이벤트를 삭제할 수 없습니다: ID=" + eventId);
        }
    }

    /**
     * 이벤트 혜택 삭제
     */
    @Transactional
    public void deleteEventBenefit(Long eventId, Long benefitId, String userEmail) {
        // 이벤트 존재 확인
        Event event = eventMapper.selectEventById(eventId)
                .orElseThrow(() -> new CustomEventException(ErrorCode.EVENT_NOT_FOUND, "이벤트를 찾을 수 없습니다: ID=" + eventId));
        // 권한 확인
        String userRole = userMapper.selectUserRoleByEmail(userEmail)
                .orElseThrow(() -> new CustomEventException(
                        ErrorCode.USER_NOT_FOUND,
                        "사용자를 찾을 수 없습니다: " + userEmail));
        if (!UserRole.SUPER_ADMIN.name().equals(userRole)) {
            int membershipCount = userMapper.countUserProjectMembership(userEmail, event.getProjectId());
            if (membershipCount == 0) {
                throw new CustomEventException(ErrorCode.ACCESS_DENIED, "해당 프로젝트에 대한 접근 권한이 없습니다.");
            }
        }
        // 혜택 조회
        EventBenefit benefit = eventBenefitMapper.selectBenefitById(benefitId);
        if (benefit == null || "Y".equals(benefit.getDeleteYn())) {
            throw new CustomEventException(ErrorCode.BENEFIT_NOT_FOUND, "혜택을 찾을 수 없습니다: ID=" + benefitId);
        }
        if (!benefit.getEventId().equals(eventId)) {
            throw new CustomEventException(ErrorCode.BENEFIT_NOT_FOUND_IN_EVENT, "해당 이벤트에 존재하지 않는 혜택입니다: ID=" + benefitId);
        }
        // 논리 삭제
        int result = eventBenefitMapper.logicalDeleteEventBenefit(benefitId, userEmail);
        if (result == 0) {
            throw new CustomEventException(ErrorCode.BENEFIT_NOT_FOUND, "혜택을 삭제할 수 없습니다: ID=" + benefitId);
        }
    }

    /**
     * 페이징 및 정렬 파라미터 검증 및 준비 (Event 용)
     */
    private Map<String, Object> validateAndPrepareEventParams(Pageable pageable) {
        Map<String, Object> params = new HashMap<>();
        params.put("offset", pageable.getOffset());
        params.put("pageSize", pageable.getPageSize());

        String sortColumn = "event_id"; // 기본 정렬 컬럼
        String sortDirection = "DESC"; // 기본 정렬 방향

        if (pageable.getSort().isSorted()) {
            Sort.Order order = pageable.getSort().iterator().next(); // 첫 번째 정렬 조건만 사용
            String property = order.getProperty();
            if (EVENT_SORT_PROPERTY_MAP.containsKey(property)) {
                sortColumn = EVENT_SORT_PROPERTY_MAP.get(property);
                sortDirection = order.getDirection().name();
            } else {
                log.warn("Invalid sort property received: {}. Using default sort.", property);
            }
        }

        params.put("sortColumn", sortColumn);
        params.put("sortDirection", sortDirection);
        log.debug("Prepared event query params: {}", params);
        return params;
    }
    
    /**
     * 이벤트 복사
     * 원본 이벤트의 기본 정보와 함께 연결된 혜택, 사전 등록 양식, 랜딩 페이지를 복사합니다.
     * 참석자 정보 및 QR 코드는 복사하지 않습니다.
     * 
     * @param originalEventId 복사할 원본 이벤트의 ID
     * @param currentUserEmail 복사를 요청한 사용자의 이메일
     * @return 복사된 새 이벤트 정보
     */
    @Transactional
    public EventResponseDto copyEvent(Long originalEventId, String currentUserEmail) {
        // 1. 원본 이벤트 조회
        Event originalEvent = eventMapper.selectEventById(originalEventId)
                .orElseThrow(() -> new CustomEventException(ErrorCode.EVENT_NOT_FOUND, "이벤트를 찾을 수 없습니다: ID=" + originalEventId));
        
        // 2. 사용자 권한 확인 (SUPER_ADMIN은 권한 검사 건너뜀)
        String userRole = userMapper.selectUserRoleByEmail(currentUserEmail)
                .orElseThrow(() -> new CustomEventException(ErrorCode.USER_NOT_FOUND, "사용자를 찾을 수 없습니다: " + currentUserEmail));
        
        // SUPER_ADMIN이 아닌 경우에만 프로젝트 멤버십 확인
        if (!UserRole.SUPER_ADMIN.name().equals(userRole)) {
            int membershipCount = userMapper.countUserProjectMembership(currentUserEmail, originalEvent.getProjectId());
            if (membershipCount == 0) {
                throw new CustomEventException(ErrorCode.ACCESS_DENIED, "해당 프로젝트에 대한 접근 권한이 없습니다.");
            }
        }
        
        // 3. 새 이벤트 객체 생성
        Event newEvent = Event.builder()
                .projectId(originalEvent.getProjectId())
                .eventName(originalEvent.getEventName() + " - copy")
                .description(originalEvent.getDescription())
                .status(EventStatus.SCHEDULED) // Enum 타입 사용
                .startDate(originalEvent.getStartDate())
                .endDate(originalEvent.getEndDate())
                .location(originalEvent.getLocation())
                .participantLimit(originalEvent.getParticipantLimit())
                .preRegistrationFormId(originalEvent.getPreRegistrationFormId()) // 원본 이벤트의 사전 등록 양식 ID 복사
                .linkedQrCodeId(null) // QR 코드는 복사하지 않음
                .eventImagePath(copyEventImage(originalEvent.getEventImagePath()))
                .createUserEmail(currentUserEmail)
                .createDate(LocalDateTime.now())
                .useYn("Y")
                .deleteYn("N")
                .build();
        
        // 4. 새 이벤트 저장
        eventMapper.insertEvent(newEvent);
        Long newEventId = newEvent.getEventId(); // auto-generated ID
        
        // 5. 이벤트 혜택 복사 - 활성화된 혜택만 복사
        List<EventBenefit> originalBenefits = eventBenefitMapper.selectActiveBenefitsByEventId(originalEventId);
        for (EventBenefit originalBenefit : originalBenefits) {
            // 삭제된 혜택은 복사하지 않음
            if ("Y".equals(originalBenefit.getDeleteYn())) {
                continue;
            }
            
            // 새 혜택 생성
            EventBenefit newBenefit = EventBenefit.builder()
                    .eventId(newEventId)
                    .benefitCode(originalBenefit.getBenefitCode())
                    .benefitName(originalBenefit.getBenefitName())
                    .description(originalBenefit.getDescription())
                    .quantity(originalBenefit.getQuantity())
                    .status(originalBenefit.getStatus())
                    .createUserEmail(currentUserEmail)
                    .createDate(LocalDateTime.now())
                    .useYn("Y")
                    .deleteYn("N")
                    .build();
            
            // 혜택 저장
            eventBenefitMapper.insertEventBenefit(newBenefit);
        }
        
        // 랜딩 페이지 복사 기능 삭제 - 요청에 따라 랜딩 페이지는 복사하지 않음
        
        // 6. 복사된 이벤트 정보 반환
        Event copiedEvent = eventMapper.selectEventById(newEventId)
                .orElseThrow(() -> new CustomEventException(ErrorCode.EVENT_NOT_FOUND, "복사된 이벤트를 찾을 수 없습니다: ID=" + newEventId));
        
        return EventResponseDto.fromEntity(copiedEvent);
    }
    
    /**
     * 이벤트 이미지 파일을 새로운 파일로 복사
     * @param originalImagePath 원본 이미지 경로
     * @return 복사된 새 이미지 경로, 복사 실패 시 원본 경로 그대로 반환
     */
    private String copyEventImage(String originalImagePath) {
        if (originalImagePath == null || originalImagePath.isEmpty()) {
            return originalImagePath; // 이미지가 없으면 그대로 반환
        }
        
        try {
            // 경로에서 파일명만 추출
            String filename = extractFilenameFromPath(originalImagePath);
            if (filename.isEmpty()) {
                log.warn("이미지 경로에서 파일명을 추출할 수 없음: {}", originalImagePath);
                return originalImagePath;
            }
            
            // 파일 복사
            String newFilename = fileStorageService.copyFile(filename);
            if (newFilename == null) {
                log.warn("이미지 파일 복사 실패: {}", filename);
                return originalImagePath;
            }
            
            // 새 파일명으로 경로 생성
            String newImagePath = originalImagePath.replace(filename, newFilename);
            log.info("이벤트 이미지 복사 완료: {} -> {}", filename, newFilename);
            
            return newImagePath;
        } catch (Exception e) {
            log.error("이미지 복사 중 오류 발생: {}", e.getMessage(), e);
            return originalImagePath; // 오류 발생 시 원본 경로 그대로 반환
        }
    }
    
    /**
     * 이미지 경로에서 파일명만 추출
     * @param path 이미지 경로
     * @return 파일명
     */
    private String extractFilenameFromPath(String path) {
        if (path == null || path.isEmpty()) {
            return "";
        }
        
        // uploads/ 뒤의 UUID 형태 파일명 추출
        int lastSlashIndex = path.lastIndexOf("/");
        if (lastSlashIndex != -1 && lastSlashIndex < path.length() - 1) {
            return path.substring(lastSlashIndex + 1);
        }
        
        return "";
    }
}
