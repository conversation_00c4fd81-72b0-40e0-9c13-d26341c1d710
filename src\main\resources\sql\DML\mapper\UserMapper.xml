<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.UserMapper">
    <resultMap id="userWithProjectsResultMap" type="kr.wayplus.wayplus_qr.entity.User">
        <id property="userEmail" column="user_email"/> <!-- user_email을 ID로 명시 -->
        <result property="userIdx" column="user_idx"/>
        <result property="name" column="name"/>
        <result property="password" column="password"/>
        <result property="roleId" column="role_id"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="initialPasswordYn" column="initial_password_yn"/>
        <result property="createDate" column="create_date"/>
        <result property="createUserEmail" column="create_user_email"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="lastLoginDate" column="last_login_date"/>
        <!-- projects 컬렉션 매핑 (ProjectResponseDto 사용) -->
        <collection property="projects" ofType="kr.wayplus.wayplus_qr.dto.response.ProjectResponseDto">
            <id property="projectId" column="project_id"/>
            <result property="projectName" column="project_name"/>
        </collection>
    </resultMap>

    <!-- 사용자 이메일로 사용자 정보 조회-->
    <select id="selectUserByEmail" parameterType="String" resultMap="userWithProjectsResultMap">
        SELECT
            u.user_idx,
            u.user_email,
            u.name,
            u.password,
            u.role_id,
            u.description,
            u.status,
            u.initial_password_yn,
            u.create_date,
            u.create_user_email,
            u.last_update_date,
            u.last_login_date,
            upm.project_id,
            p.project_name
        FROM
            users u
        LEFT JOIN user_project_mapping upm ON u.user_email = upm.user_email AND upm.delete_yn = 'N'
        LEFT JOIN projects p ON upm.project_id = p.project_id AND p.delete_yn = 'N'
        WHERE
            u.user_email = #{userEmail}
            AND u.delete_yn = 'N'
            AND u.use_yn = 'Y'
    </select>

    <!-- 등록된 모든 사용자 목록 조회 (SUPER_ADMIN용) -->
    <!-- 기존 selectUserList 쿼리가 있다면 여기에 UserListResponseDto에 맞게 필드 조정 -->
    <select id="selectUserList" resultType="kr.wayplus.wayplus_qr.dto.response.UserListResponseDto">
        SELECT
            u.user_email AS userEmail,
            u.name AS name,
            u.role_id AS roleId,
            u.status,
            u.last_login_date AS lastLoginDate,
            u.create_date AS createDate
        FROM
            users u
        WHERE
            u.delete_yn = 'N'
        ORDER BY
            u.create_date DESC
    </select>

    <!-- 사용자 역할 조회 -->
    <select id="selectUserRoleByEmail" parameterType="String" resultType="String">
        SELECT role_id
        FROM users
        WHERE user_email = #{userEmail}
        AND delete_yn = 'N'
        AND use_yn = 'Y'
    </select>

    <!-- 사용자 정보 수정 -->
    <update id="updateUser" parameterType="kr.wayplus.wayplus_qr.entity.User">
        UPDATE users
        <set>
            <if test="name != null and name != ''">
                name = #{name},
            </if>
            <if test="roleId != null and roleId != ''">
                role_id = #{roleId},
            </if>
            <if test="description != null">
                description = #{description},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="lastUpdateDate != null">
                last_update_date = NOW(),
            </if>
            <if test="updateUserEmail != null and updateUserEmail != ''">
                update_user_email = #{updateUserEmail},
            </if>
        </set>
        WHERE user_email = #{userEmail}
        AND delete_yn = 'N'
        AND use_yn = 'Y'
    </update>

    <!-- 사용자 논리 삭제 -->
    <update id="deleteUserLogically">
        UPDATE users
        SET
            delete_yn = 'Y',
            use_yn = 'N',
            delete_date = NOW(),
            delete_user_email = #{deleterEmail}
        WHERE
            user_email = #{userEmail}
            AND delete_yn = 'N' -- 이미 삭제된 사용자는 제외
    </update>

    <!-- 사용자-프로젝트 매핑 정보 삽입 (UserService에서 사용) -->
    <insert id="insertUserProjectMapping" parameterType="map">
        INSERT INTO user_project_mapping (
            user_email,
            project_id,
            create_user_email,
            create_date,
            last_update_date,
            delete_yn,
            use_yn
        ) VALUES (
            #{userEmail},
            #{projectId},
            #{createUserEmail},
            NOW(),
            NOW(),
            'N',
            'Y'
        )
        ON DUPLICATE KEY UPDATE
            delete_yn = 'N',                 -- 이미 존재하면 활성 상태로 변경
            use_yn = 'Y',                    -- 사용 상태로 변경
            update_user_email = VALUES(create_user_email), -- 삽입 시도 값 사용 (매핑 요청자)
            last_update_date = NOW()         -- 업데이트 시간 기록
    </insert>

    <!-- 사용자-프로젝트 매핑 논리적 삭제 -->
    <update id="deleteUserProjectMappingLogically" parameterType="map">
        UPDATE user_project_mapping
           SET delete_yn = 'Y', use_yn = 'N',
               delete_user_email = #{deleterEmail},
               delete_date = NOW(),
               update_user_email = #{updateUserEmail},    -- 업데이트 수행자 이메일
               last_update_date = NOW()                   -- 현재 시간으로 업데이트 날짜 설정
         WHERE user_email = #{userEmail}                 -- 대상 사용자 이메일
           AND project_id = #{projectId}                 -- 대상 프로젝트 ID
           AND delete_yn = 'N'                           -- 현재 활성화된(삭제되지 않은) 매핑만 대상
    </update>

    <!-- 이메일 존재 여부 확인 (삭제된 사용자 포함) -->
    <select id="existsByUserEmail" parameterType="String" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM users
        WHERE user_email = #{userEmail}
    </select>

    <!-- 사용자 이메일로 사용자 정보 조회 (업데이트용) -->
    <select id="selectUserByEmailForUpdate" resultType="kr.wayplus.wayplus_qr.entity.User">
        SELECT
            user_idx, user_email, name, password, role_id, status,
            description, login_fail_count, create_date, last_update_date, delete_date
        FROM
            users
        WHERE
            user_email = #{userEmail}
          AND delete_date IS NULL
        <!-- 필요한 경우 FOR UPDATE 추가 (비관적 락) -->
    </select>

    <!-- 사용자 비밀번호 업데이트 -->
    <!-- 파라미터: userEmail (String), encodedPassword (String) -->
    <update id="updateUserPassword">
        UPDATE users
        SET
            password = #{encodedPassword},      -- 인코딩된 새 비밀번호
            initial_password_yn = 'N',         -- 초기 비밀번호 여부 'N'으로 변경
            last_update_date = CURRENT_TIMESTAMP,
            update_user_email = #{userEmail}     -- 비밀번호 변경 주체 (본인 이메일)
        WHERE user_email = #{userEmail}
          AND delete_yn = 'N'                  -- 삭제되지 않은 사용자만
    </update>

    <!-- 사용자가 속한 프로젝트 ID 목록 조회 -->
    <select id="selectProjectIdsByUserEmail" resultType="long">
        SELECT
            project_id
        FROM
            user_project_mapping
        WHERE
            user_email = #{userEmail}
        AND delete_yn = 'N'
    </select>

    <!-- 프로젝트 관리자 본인 및 관리 프로젝트 소속 사용자 목록 조회 -->
    <select id="selectUsersByProjectIdsAndSelf" resultType="kr.wayplus.wayplus_qr.dto.response.UserListResponseDto">
        SELECT DISTINCT -- 중복 제거
            u.user_email AS userEmail,
            u.name AS name,
            u.role_id AS roleId,
            u.status,
            u.last_login_date AS lastLoginDate,
            u.create_date AS createDate
        FROM
            users u
        LEFT JOIN user_project_mapping upm ON u.user_email = upm.user_email AND upm.delete_yn = 'N'
        WHERE
            u.delete_yn = 'N'
            AND (
                u.user_email = #{adminEmail} -- 관리자 본인
                <if test="projectIds != null and !projectIds.isEmpty()">
                OR upm.project_id IN
                <foreach item="projectId" collection="projectIds" open="(" separator="," close=")">
                    #{projectId}
                </foreach>
                </if>
            )
        ORDER BY
            u.create_date DESC
    </select>

    <!-- 할당 가능한 프로젝트 관리자 목록 조회 -->
    <select id="selectAvailableProjectAdmins" resultType="kr.wayplus.wayplus_qr.dto.response.UserResponseDto">
        SELECT
            u.user_idx AS userIdx,
            u.user_email AS userEmail,
            u.name AS name,
            u.role_id AS roleId,
            u.status,
            u.description,
            u.create_date AS createDate,
            u.last_update_date AS updateDate
        FROM
            users u
        WHERE
            u.role_id = 'PROJECT_ADMIN'
            AND u.delete_yn = 'N'
            AND u.use_yn = 'Y'
            AND u.user_email NOT IN (
                SELECT DISTINCT p.project_admin_user_email
                FROM projects p
                WHERE p.project_admin_user_email IS NOT NULL
                  AND p.delete_yn = 'N'
            )
        ORDER BY
            u.name ASC;
    </select>

    <!-- 특정 프로젝트 수정 시 할당 가능한 프로젝트 관리자 목록 조회 -->
    <select id="selectAvailableProjectAdminsForProject" parameterType="long" resultType="kr.wayplus.wayplus_qr.dto.response.AvailableProjectAdminDto">
        SELECT
            u.user_idx AS userIdx,
            u.user_email AS userEmail,
            u.name AS userName,
            CASE
                WHEN EXISTS (SELECT 1 FROM user_project_mapping upl WHERE upl.user_email = u.user_email AND upl.project_id = #{projectId})
                    THEN 'Y' -- 현재 프로젝트 관리자
                ELSE 'N' -- 할당되지 않은 관리자
                END AS isCurrentAdmin
        FROM
            users u
        WHERE
            u.role_id = 'PROJECT_ADMIN'
          AND u.use_yn = 'Y'
          AND (
                -- 현재 프로젝트의 관리자 이거나
                EXISTS (SELECT 1 FROM user_project_mapping upl WHERE upl.user_email = u.user_email AND upl.project_id = #{projectId})
                OR
                -- 어떤 프로젝트에도 할당되지 않은 관리자
                NOT EXISTS (SELECT 1 FROM user_project_mapping upl WHERE upl.user_email = u.user_email)
            )
        ORDER BY
            u.name ASC
    </select>

    <select id="countUserProjectMembership" resultType="int">
        SELECT COUNT(*)
        FROM user_project_mapping upl
                 JOIN users u ON upl.user_email = u.user_email
        WHERE u.user_email = #{userEmail}
          AND upl.project_id = #{projectId}
          AND u.use_yn = 'Y'
    </select>

    <!-- 프로젝트 사용자 목록 페이징 조회 -->
    <select id="selectProjectUserListWithPaging" resultType="kr.wayplus.wayplus_qr.dto.response.UserListResponseDto">
        SELECT
            u.user_idx AS userIdx,
            u.user_email AS userEmail,
            u.name AS name,
            u.contact_number AS userPhone,
            u.role_id AS roleId,
            u.create_date AS createDate,
            u.last_login_date AS lastLoginDate,
            u.use_yn AS useYn,
            u.status AS status,
            p.project_name AS projectNames
        FROM
            users u
                INNER JOIN
            user_project_mapping upl ON u.user_email = upl.user_email
                INNER JOIN
            projects p ON upl.project_id = p.project_id
        WHERE
            u.use_yn = 'Y'
          AND p.use_yn = 'Y'
          AND upl.project_id = #{projectId}
          <if test="searchKeyword != null and searchKeyword != '' and searchColumn != null">
            <choose>
                <when test="searchColumn == 'user_email'">
                    AND u.user_email LIKE CONCAT('%', #{searchKeyword}, '%')
                </when>
                <otherwise>
                    AND u.${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
                </otherwise>
            </choose>
          </if>
        <!-- 동적 정렬 -->
        <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
            ORDER BY
            <foreach item="order" collection="pageable.sort" separator=", ">
                <choose>
                    <when test="order.property == 'userEmail'">u.user_email</when>
                    <when test="order.property == 'name'">u.name</when>
                    <when test="order.property == 'roleId'">u.role_id</when>
                    <when test="order.property == 'status'">u.status</when>
                    <!-- 다른 허용된 정렬 기준 컬럼 추가 (예: linked_landing_page_title, linked_event_name 등) -->
                    <otherwise>u.create_date</otherwise> <!-- 기본 정렬 기준 -->
                </choose>
                <choose>
                    <when test="order.direction.name() == 'ASC'">ASC</when>
                    <when test="order.direction.name() == 'DESC'">DESC</when>
                    <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                </choose>
            </foreach>
        </if>
        <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
            <!-- 기본 정렬: 생성일 내림차순 -->
            ORDER BY u.create_date DESC
        </if>
        <!-- 페이징 -->
        <if test="pageable != null">
            LIMIT #{pageable.offset}, #{pageable.pageSize}
        </if>
    </select>

    <!-- 프로젝트 총 사용자 수 조회 -->
    <select id="countProjectUsers" resultType="long">
        SELECT COUNT(DISTINCT u.user_idx)
        FROM users u
                 INNER JOIN user_project_mapping upl ON u.user_email = upl.user_email
                 INNER JOIN projects p ON upl.project_id = p.project_id
        WHERE u.use_yn = 'Y'
          AND p.use_yn = 'Y'
          AND upl.project_id = #{projectId}
          <if test="searchKeyword != null and searchKeyword != '' and searchColumn != null">
            <choose>
              <when test="searchColumn == 'user_email'">
                AND u.user_email LIKE CONCAT('%', #{searchKeyword}, '%')
              </when>
              <otherwise>
                AND u.${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
              </otherwise>
            </choose>
          </if>
    </select>

    <!-- 전체 사용자 목록 페이징 조회 -->
    <select id="selectAllUsersWithPaging" resultType="kr.wayplus.wayplus_qr.dto.response.UserListResponseDto">
        SELECT
            u.user_idx AS userIdx,
            u.user_email AS userEmail,
            u.name AS name,
            u.contact_number AS userPhone,
            u.role_id AS roleId,
            u.create_date AS createDate,
            u.last_login_date AS lastLoginDate,
            u.use_yn AS useYn,
            u.status AS status,
            GROUP_CONCAT(p.project_name SEPARATOR ', ') AS projectNames
        FROM
            users u
                LEFT JOIN
            user_project_mapping upl ON u.user_email = upl.user_email
                LEFT JOIN
            projects p ON upl.project_id = p.project_id AND p.use_yn = 'Y'
        WHERE
            u.use_yn = 'Y'
          <if test="searchKeyword != null and searchKeyword != '' and searchColumn != null">
            <choose>
                <when test="searchColumn == 'user_email'">
                    AND u.user_email LIKE CONCAT('%', #{searchKeyword}, '%')
                </when>
                <otherwise>
                    AND u.${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
                </otherwise>
            </choose>
          </if>
        GROUP BY
            u.user_idx
        <!-- 동적 정렬 -->
        <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
            ORDER BY
            <foreach item="order" collection="pageable.sort" separator=", ">
                <choose>
                    <when test="order.property == 'userEmail'">u.user_email</when>
                    <when test="order.property == 'name'">u.name</when>
                    <when test="order.property == 'roleId'">u.role_id</when>
                    <when test="order.property == 'status'">u.status</when>
                    <!-- 다른 허용된 정렬 기준 컬럼 추가 (예: linked_landing_page_title, linked_event_name 등) -->
                    <otherwise>u.create_date</otherwise> <!-- 기본 정렬 기준 -->
                </choose>
                <choose>
                    <when test="order.direction.name() == 'ASC'">ASC</when>
                    <when test="order.direction.name() == 'DESC'">DESC</when>
                    <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                </choose>
            </foreach>
        </if>
        <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
            <!-- 기본 정렬: 생성일 내림차순 -->
            ORDER BY u.create_date DESC
        </if>
        <!-- 페이징 -->
        <if test="pageable != null">
            LIMIT #{pageable.offset}, #{pageable.pageSize}
        </if>
    </select>

    <!-- 전체 총 사용자 수 조회 -->
    <select id="countAllUsers" resultType="long">
        SELECT COUNT(*)
        FROM users
        WHERE use_yn = 'Y'
        <if test="searchKeyword != null and searchKeyword != '' and searchColumn != null">
            AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
          </if>
    </select>

    <!-- 사용자가 특정 프로젝트에 할당되었는지 확인 -->
    <select id="checkUserProjectAssignment" resultType="Integer">
        SELECT COUNT(*)
        FROM users u
                 JOIN user_project_mapping upl ON u.user_email = upl.user_email
        WHERE u.user_email = #{userEmail}
          AND upl.project_id = #{projectId}
          AND u.use_yn = 'Y'
    </select>

    <!-- 사용자 추가 -->
    <insert id="insertUser" useGeneratedKeys="true" keyProperty="userIdx">
        INSERT INTO users (
            user_email,
            name,
            contact_number,
            role_id,
            create_date,
            last_login_date,
            use_yn,
            password, -- 비밀번호 필드 추가
            initial_password_yn -- 최초 비밀번호 여부 필드 추가
        ) VALUES (
            #{userEmail},
            #{name},
            #{contactNumber},
            #{roleId},
            NOW(),
            NOW(),
            'Y',
            #{password}, -- 비밀번호 값 매핑
            'Y' -- 기본값 'Y'
        )
    </insert>

    <!-- 특정 사용자가 특정 프로젝트에서 지정된 역할 중 하나를 가지고 있는지 확인 (users 테이블과 조인) -->
    <select id="countUserProjectMembershipWithRoles" resultType="int">
        SELECT COUNT(*)
        FROM user_project_mapping upm
        JOIN users u ON upm.user_email = u.user_email
        WHERE upm.user_email = #{userEmail}
          AND upm.project_id = #{projectId}
          AND u.role_id IN
            <foreach item="roleName" collection="roleNames" open="(" separator="," close=")">
                #{roleName}
            </foreach>
          AND upm.delete_yn = 'N'
          AND u.delete_yn = 'N' <!-- users 테이블의 삭제 여부도 확인 -->
    </select>

    <!-- 사용자-프로젝트 링크 추가 -->
    <insert id="insertUserProjectLink">
        INSERT INTO user_project_mapping (user_email, project_id)
        VALUES (#{userEmail}, #{projectId})
    </insert>

    <!-- 사용자-프로젝트 링크 삭제 -->
    <delete id="deleteUserProjectLinks">
        DELETE FROM user_project_mapping WHERE user_email = #{userEmail}
    </delete>

    <!-- 특정 프로젝트 ID로 사용자-프로젝트 링크 삭제 -->
    <delete id="deleteUserProjectLinksByProjectId">
        DELETE FROM user_project_mapping WHERE project_id = #{projectId}
    </delete>

    <!-- ********** 관리자 역할 통계 조회 추가 ********** -->
    <select id="selectAdminRoleCounts" resultType="java.util.Map">
        SELECT
            role_id,
            COUNT(*) as count
        FROM
            users u
        WHERE
            u.use_yn = 'Y'
          AND u.delete_yn = 'N'
          AND u.role_id IS NOT NULL
          <if test="projectId != null">
            AND u.user_email IN (
                SELECT user_email FROM user_project_mapping WHERE project_id = #{projectId} AND delete_yn = 'N'
            )
          </if>
        GROUP BY
            role_id
        ORDER BY
            role_id
    </select>

    <!-- 기간별 일일 관리자 생성 수 조회 (역할별) -->
    <select id="selectDailyAdminCreationCounts" resultType="kr.wayplus.wayplus_qr.dto.response.DailyRoleCountDto">
        SELECT
            DATE_FORMAT(u.create_date, '%Y-%m-%d') AS date,
            u.role_id,
            COUNT(*) AS count
        FROM
            users u
        <if test="projectId != null">
        JOIN user_project_mapping up ON u.user_email = up.user_email
        </if>
        WHERE
            <![CDATA[
            DATE_FORMAT(u.create_date, '%Y-%m-%d') >= STR_TO_DATE(#{startDate}, '%Y-%m-%d')
          AND DATE_FORMAT(u.create_date, '%Y-%m-%d') <= STR_TO_DATE(#{endDate}, '%Y-%m-%d')
            ]]>
            <if test="projectId != null">
            AND up.project_id = #{projectId}
            AND up.delete_yn = 'N'
            </if>
        GROUP BY
            DATE_FORMAT(u.create_date, '%Y-%m-%d'),
            u.role_id
        ORDER BY
            date,
            u.role_id
    </select>

    <!-- 관리자별 가장 많이 접속한 URI 조회 -->
    <select id="selectAdminMostVisitedUri" resultType="map">
        SELECT
            w.user_email              AS userEmail,
            w.request_uri             AS requestUri,
            COUNT(*)                  AS count
        FROM webservice_log w
        JOIN users u ON w.user_email = u.user_email
        <if test="projectId != null">
            JOIN user_project_mapping upm ON u.user_email = upm.user_email AND upm.project_id = #{projectId} AND upm.delete_yn = 'N'
        </if>
        WHERE
            u.role_id IS NOT NULL
            AND u.use_yn = 'Y'
            AND u.delete_yn = 'N'
            AND u.role_id &lt;&gt; (SELECT role_id FROM roles WHERE role_id = 'SUPER_ADMIN' LIMIT 1)
            <if test="startDate != null and endDate != null">
                AND w.request_time BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="projectId != null">
                AND (
                    w.request_params LIKE CONCAT('%projectId=', #{projectId}, '%')
                    OR w.request_params LIKE CONCAT('%"projectId":"', #{projectId}, '"%')
                    OR w.request_uri LIKE CONCAT('%/', #{projectId}, '/%')
                )
            </if>
        GROUP BY w.user_email, w.request_uri
        HAVING COUNT(*) = (
            SELECT COUNT(*)
            FROM webservice_log w2
            WHERE w2.user_email = w.user_email
            <if test="startDate != null and endDate != null">
                AND w2.request_time BETWEEN #{startDate} AND #{endDate}
            </if>
            <if test="projectId != null">
                AND (
                    w2.request_params LIKE CONCAT('%projectId=', #{projectId}, '%')
                    OR w2.request_params LIKE CONCAT('%"projectId":"', #{projectId}, '"%')
                    OR w2.request_uri LIKE CONCAT('%/', #{projectId}, '/%')
                )
            </if>
            GROUP BY w2.user_email, w2.request_uri
            ORDER BY COUNT(*) DESC
            LIMIT 1
        )
    </select>

    <!-- 사용자 기본 통계 조회 -->
    <select id="selectUserBasicStats" resultType="map">
        SELECT
            COUNT(*) AS totalUsers,
            SUM(CASE WHEN status = 'ACTIVE' THEN 1 ELSE 0 END) AS activeUsers,
            SUM(CASE WHEN status = 'INACTIVE' THEN 1 ELSE 0 END) AS inactiveUsers
        FROM users
        <where>
            <if test="projectId != null">
                AND user_email IN (
                    SELECT user_email FROM user_project_mapping WHERE project_id = #{projectId}
                )
            </if>
            <if test="startDate != null and endDate != null">
                AND create_date BETWEEN #{startDate} AND DATE_ADD(#{endDate}, INTERVAL 1 DAY)
            </if>
        </where>
    </select>

    <!-- 역할별 사용자 수 조회 -->
    <select id="selectUserCountByRole" resultType="map">
        SELECT
            r.role_name AS role,
            COUNT(DISTINCT u.user_idx) AS count
        FROM users u
        JOIN roles r ON u.role_id = r.role_id
        <where>
            u.use_yn = 'Y'
            AND u.delete_yn = 'N'
            AND r.role_id &lt;&gt; 'SUPER_ADMIN'
            <if test="projectId != null">
                AND u.user_email IN (
                    SELECT user_email FROM user_project_mapping WHERE project_id = #{projectId} AND delete_yn = 'N'
                )
            </if>
            <if test="startDate != null and endDate != null">
                AND u.create_date BETWEEN #{startDate} AND DATE_ADD(#{endDate}, INTERVAL 1 DAY)
            </if>
        </where>
        GROUP BY r.role_name
        ORDER BY r.role_id
    </select>


    <!-- 특정 역할을 가진 사용자 목록 조회 -->
    <select id="selectUserListByRole" parameterType="string" resultMap="userWithProjectsResultMap">
        SELECT
            u.user_idx,
            u.user_email,
            u.name,
            u.password,
            u.role_id,
            u.description,
            u.status,
            u.initial_password_yn,
            u.create_date,
            u.create_user_email,
            u.last_update_date,
            u.last_login_date,
            upm.project_id,
            p.project_name
        FROM
            users u
        LEFT JOIN user_project_mapping upm ON u.user_email = upm.user_email AND upm.delete_yn = 'N'
        LEFT JOIN projects p ON upm.project_id = p.project_id AND p.delete_yn = 'N'
        WHERE
            u.role_id = #{role}
            AND u.delete_yn = 'N'
            AND u.use_yn = 'Y'
    </select>

    <!-- 상위 QR 코드 생성자 통계 조회 -->
    <select id="selectTopQrCreators" resultType="map">
        SELECT
            u.user_email AS userEmail,
            COUNT(q.qr_code_id) AS totalQrCodes,
            SUM(CASE WHEN q.status = 'ACTIVE' THEN 1 ELSE 0 END) AS activeQrCodes,
            IFNULL(SUM(
                (SELECT COUNT(*) FROM qr_scan_logs WHERE qr_code_id = q.qr_code_id)
            ), 0) AS totalScans
        FROM users u
        JOIN qr_codes q ON u.user_email = q.create_user_email
        WHERE q.delete_yn = 'N'
        <if test="projectId != null">
            AND q.project_id = #{projectId}
        </if>
        GROUP BY u.user_email
        ORDER BY totalQrCodes DESC
        LIMIT #{limit}
    </select>

    <!-- 사용자별 QR 코드 타입 분포 조회 -->
    <select id="selectUserQrTypeDistribution" resultType="map">
        SELECT
            u.user_email AS userEmail,
            q.qr_type AS qrType,
            COUNT(q.qr_code_id) AS count
        FROM users u
        JOIN qr_codes q ON u.user_email = q.create_user_email
        WHERE q.delete_yn = 'N'
        <if test="projectId != null">
            AND q.project_id = #{projectId}
        </if>
        <if test="userEmail != null">
            AND u.user_email = #{userEmail}
        </if>
        GROUP BY u.user_email, q.qr_type
    </select>

    <!-- 사용자당 평균 QR 코드 수 조회 -->
    <select id="selectAvgQrCodesPerUser" resultType="double">
        SELECT
            IFNULL(AVG(qr_count), 0) AS avgQrCodesPerUser
        FROM (
            SELECT
                u.user_email,
                COUNT(q.qr_code_id) AS qr_count
            FROM users u
            LEFT JOIN qr_codes q ON u.user_email = q.create_user_email AND q.delete_yn = 'N'
            <where>
                <if test="projectId != null">
                    AND (q.project_id = #{projectId} OR q.project_id IS NULL)
                    AND u.user_email IN (
                        SELECT user_email FROM user_project_mapping WHERE project_id = #{projectId}
                    )
                </if>
            </where>
            GROUP BY u.user_email
        ) AS user_qr_counts
    </select>

    <!-- 상위 이벤트 생성자 통계 조회 -->
    <select id="selectTopEventCreators" resultType="map">
        SELECT
            u.user_email AS userEmail,
            COUNT(e.event_id) AS totalEvents,
            SUM(CASE WHEN e.status = 'ACTIVE' THEN 1 ELSE 0 END) AS activeEvents,
            IFNULL(SUM(
                (SELECT COUNT(*) FROM attendees WHERE event_id = e.event_id)
            ), 0) AS totalAttendees
        FROM users u
        JOIN events e ON u.user_email = e.create_user_email
        WHERE e.delete_yn = 'N'
        <if test="projectId != null">
            AND e.project_id = #{projectId}
        </if>
        GROUP BY u.user_email
        ORDER BY totalEvents DESC
        LIMIT #{limit}
    </select>

    <!-- 사용자당 평균 이벤트 수 조회 -->
    <select id="selectAvgEventsPerUser" resultType="double">
        SELECT
            IFNULL(AVG(event_count), 0) AS avgEventsPerUser
        FROM (
            SELECT
                u.user_email,
                COUNT(e.event_id) AS event_count
            FROM users u
            LEFT JOIN events e ON u.user_email = e.create_user_email AND e.delete_yn = 'N'
            <where>
                <if test="projectId != null">
                    AND (e.project_id = #{projectId} OR e.project_id IS NULL)
                    AND u.user_email IN (
                        SELECT user_email FROM user_project_mapping WHERE project_id = #{projectId}
                    )
                </if>
            </where>
            GROUP BY u.user_email
        ) AS user_event_counts
    </select>

    <!-- 상위 랜딩 페이지 생성자 통계 조회 -->
    <select id="selectTopLandingPageCreators" resultType="map">
        SELECT
            u.user_email AS userEmail,
            COUNT(lp.landing_page_id) AS totalLandingPages,
            SUM(CASE WHEN lp.status = 'PUBLISHED' THEN 1 ELSE 0 END) AS activeLandingPages,
            0 AS totalViews
        FROM users u
        JOIN landing_pages lp ON u.user_email = lp.create_user_email
        WHERE lp.delete_yn = 'N'
        <if test="projectId != null">
            AND lp.project_id = #{projectId}
        </if>
        GROUP BY u.user_email
        ORDER BY totalLandingPages DESC
        LIMIT #{limit}
    </select>

    <!-- 사용자당 평균 랜딩 페이지 수 조회 -->
    <select id="selectAvgLandingPagesPerUser" resultType="double">
        SELECT
            IFNULL(AVG(lp_count), 0) AS avgLandingPagesPerUser
        FROM (
            SELECT
                u.user_email,
                COUNT(lp.landing_page_id) AS lp_count
            FROM users u
            LEFT JOIN landing_pages lp ON u.user_email = lp.create_user_email AND lp.delete_yn = 'N'
            <where>
                <if test="projectId != null">
                    AND (lp.project_id = #{projectId} OR lp.project_id IS NULL)
                    AND u.user_email IN (
                        SELECT user_email FROM user_project_mapping WHERE project_id = #{projectId}
                    )
                </if>
            </where>
            GROUP BY u.user_email
        ) AS user_lp_counts
    </select>

    <!-- 상위 교환권 승인자 통계 조회 -->
    <select id="selectTopExchangeApprovers" resultType="map">
        SELECT
            approver_user_email AS approverEmail,
            COUNT(*) AS approvalCount
        FROM exchange_approval_logs
        <where>
            <if test="startDate != null">
                AND approval_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND approval_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
            </if>
            <if test="projectId != null">
                AND qr_code_id IN (SELECT qr_code_id FROM qr_codes WHERE project_id = #{projectId} AND delete_yn = 'N')
            </if>
        </where>
        GROUP BY approver_user_email
        ORDER BY approvalCount DESC
        LIMIT #{limit}
    </select>

    <!-- 총 교환권 승인 횟수 조회 -->
    <select id="selectTotalExchangeApprovals" resultType="long">
        SELECT
            COUNT(*) AS totalApprovals
        FROM exchange_approval_logs
        <where>
            <if test="startDate != null">
                AND approval_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND approval_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
            </if>
            <if test="projectId != null">
                AND qr_code_id IN (SELECT qr_code_id FROM qr_codes WHERE project_id = #{projectId} AND delete_yn = 'N')
            </if>
        </where>
    </select>

    <!-- 승인자당 평균 승인 횟수 조회 -->
    <select id="selectAvgApprovalsPerApprover" resultType="double">
        SELECT
            IFNULL(AVG(approval_count), 0) AS avgApprovalsPerApprover
        FROM (
            SELECT
                approver_user_email,
                COUNT(*) AS approval_count
            FROM exchange_approval_logs
            <where>
                <if test="startDate != null">
                    AND approval_time >= #{startDate}
                </if>
                <if test="endDate != null">
                    AND approval_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
                </if>
                <if test="projectId != null">
                    AND qr_code_id IN (SELECT qr_code_id FROM qr_codes WHERE project_id = #{projectId} AND delete_yn = 'N')
                </if>
            </where>
            GROUP BY approver_user_email
        ) AS approver_counts
    </select>

    <!-- 일별 사용자 활동 통계 조회 -->
    <select id="selectDailyUserActivities" resultType="map">
        SELECT
            DATE(request_time) AS date, 
            user_email,
            COUNT(*) AS activityCount
        FROM webservice_log 
        <where>
            <if test="startDate != null">
                AND request_time >= #{startDate} 
            </if>
            <if test="endDate != null">
                AND request_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY) 
            </if>
            <if test="projectId != null">
                AND (
                    /* request_params에서 projectId 확인 */
                    request_params LIKE CONCAT('%projectId=', #{projectId}, '%')
                    OR request_params LIKE CONCAT('%"projectId":"', #{projectId}, '"%')
                    OR request_params LIKE CONCAT('%"projectId":', #{projectId}, ',%')
                    OR request_params LIKE CONCAT('%"projectId":', #{projectId}, '}%')
                    /* request_uri에서 projectId 확인 */
                    OR request_uri LIKE CONCAT('%/', #{projectId}, '/%')
                    OR request_uri LIKE CONCAT('%/project/', #{projectId}, '%')
                    OR request_uri LIKE CONCAT('%projectId=', #{projectId}, '%')
                )
            </if>
        </where>
        GROUP BY DATE(request_time), user_email
        ORDER BY DATE(request_time), activityCount DESC
    </select>

    <!-- 시간대별 사용자 활동 통계 조회 -->
    <select id="selectHourlyUserActivities" resultType="map">
        SELECT
            HOUR(request_time) AS hour, 
            COUNT(*) AS activityCount
        FROM webservice_log 
        <where>
            <if test="startDate != null">
                AND request_time >= #{startDate} 
            </if>
            <if test="endDate != null">
                AND request_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY) 
            </if>
            <if test="projectId != null">
                AND (
                    /* request_params에서 projectId 확인 */
                    request_params LIKE CONCAT('%projectId=', #{projectId}, '%')
                    OR request_params LIKE CONCAT('%"projectId":"', #{projectId}, '"%')
                    OR request_params LIKE CONCAT('%"projectId":', #{projectId}, ',%')
                    OR request_params LIKE CONCAT('%"projectId":', #{projectId}, '}%')
                    /* request_uri에서 projectId 확인 */
                    OR request_uri LIKE CONCAT('%/', #{projectId}, '/%')
                    OR request_uri LIKE CONCAT('%/project/', #{projectId}, '%')
                    OR request_uri LIKE CONCAT('%projectId=', #{projectId}, '%')
                )
            </if>
        </where>
        GROUP BY HOUR(request_time)
        ORDER BY HOUR(request_time)
    </select>

    <!-- 요일별 사용자 활동 통계 조회 -->
    <select id="selectWeekdayUserActivities" resultType="map">
        SELECT
            CASE DAYOFWEEK(request_time) 
                WHEN 1 THEN 'Sunday'
                WHEN 2 THEN 'Monday'
                WHEN 3 THEN 'Tuesday'
                WHEN 4 THEN 'Wednesday'
                WHEN 5 THEN 'Thursday'
                WHEN 6 THEN 'Friday'
                WHEN 7 THEN 'Saturday'
            END AS weekday,
            COUNT(*) AS activityCount
        FROM webservice_log 
        <where>
            <if test="startDate != null">
                AND request_time >= #{startDate} 
            </if>
            <if test="endDate != null">
                AND request_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY) 
            </if>
            <if test="projectId != null">
                AND (
                    /* request_params에서 projectId 확인 */
                    request_params LIKE CONCAT('%projectId=', #{projectId}, '%')
                    OR request_params LIKE CONCAT('%"projectId":"', #{projectId}, '"%')
                    OR request_params LIKE CONCAT('%"projectId":', #{projectId}, ',%')
                    OR request_params LIKE CONCAT('%"projectId":', #{projectId}, '}%')
                    /* request_uri에서 projectId 확인 */
                    OR request_uri LIKE CONCAT('%/', #{projectId}, '/%')
                    OR request_uri LIKE CONCAT('%/project/', #{projectId}, '%')
                    OR request_uri LIKE CONCAT('%projectId=', #{projectId}, '%')
                )
            </if>
        </where>
        GROUP BY DAYOFWEEK(request_time)
        ORDER BY DAYOFWEEK(request_time)
    </select>

    <update id="updateUserNewTokenId" parameterType="User">
        UPDATE users
           SET user_token_id = #{userTokenId}
         WHERE user_email = #{userEmail}
    </update>

    <update id="updateUserWebLog" parameterType="HashMap">
        UPDATE webservice_log
           SET user_token = #{after}, tracking = 'Y'
         WHERE user_token = #{before} AND tracking = 'N'
    </update>

    <insert id="insertUserWebLog" parameterType="WebServiceLog">
        INSERT INTO webservice_log
               (user_token, user_email,
                referer, request_uri, request_params, request_time,
                request_host, request_agent,
                session_id, response_status, tracking)
        VALUES (#{userToken}, #{userEmail},
                #{referer}, #{requestUri}, #{requestParams}, now(),
                #{requestHost}, #{requestAgent},
                #{sessionId}, #{responseStatus}, #{tracking})
    </insert>
</mapper>
