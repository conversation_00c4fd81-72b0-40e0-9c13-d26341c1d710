package kr.wayplus.wayplus_qr.dto.request;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonFormat;

import kr.wayplus.wayplus_qr.entity.LandingPageStatus;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class LandingPageCreateRequestDto {
    private Long projectId;
    private String pageTitle;
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime validFromDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime validToDate;

    private LandingPageStatus status;
    private Map<String, Object> contentJson;
}
