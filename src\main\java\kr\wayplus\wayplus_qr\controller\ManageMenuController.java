package kr.wayplus.wayplus_qr.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import kr.wayplus.wayplus_qr.dto.MenuDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.service.ManageMenuService;
import kr.wayplus.wayplus_qr.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/way/manage/menus")
@RequiredArgsConstructor
@Tag(name = "Menu Management", description = "메뉴 관리 API")
public class ManageMenuController {

    private final ManageMenuService manageMenuService;
    private final UserService userService;

    // ========== SUPER_ADMIN 전용 메뉴 관리 API ==========

    @Operation(summary = "신규 메뉴 생성 (관리자 전용)", description = "새로운 메뉴를 생성합니다. 메뉴 코드, 이름, URL, 계층구조를 설정할 수 있습니다. (SUPER_ADMIN 전용)")
    @PostMapping
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Long>> createMenu(
            @Valid @RequestBody MenuDto.CreateRequest requestDto,
            Authentication authentication) {

        log.info("Creating menu: {} by user: {}", requestDto.getMenuCode(), authentication.getName());
        ApiResponseDto<Long> response = manageMenuService.createMenu(requestDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "기존 메뉴 정보 수정 (관리자 전용)", description = "기존 메뉴의 이름, URL, 계층구조, 상태 등을 수정합니다. (SUPER_ADMIN 전용)")
    @PutMapping("/{menuId}")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> updateMenu(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable("menuId") Long menuId,
            @Valid @RequestBody MenuDto.UpdateRequest requestDto,
            Authentication authentication) {

        log.info("Updating menu ID: {} by user: {}", menuId, authentication.getName());
        ApiResponseDto<Void> response = manageMenuService.updateMenu(menuId, requestDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴 삭제 (관리자 전용)", description = "선택한 메뉴를 삭제합니다. 하위 메뉴가 있는 경우 삭제가 제한될 수 있습니다. (SUPER_ADMIN 전용)")
    @DeleteMapping("/{menuId}")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> deleteMenu(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable("menuId") Long menuId,
            Authentication authentication) {

        log.info("Deleting menu ID: {} by user: {}", menuId, authentication.getName());
        ApiResponseDto<Void> response = manageMenuService.deleteMenu(menuId, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "전체 메뉴 트리 구조 조회 (관리자 전용)", description = "시스템의 모든 메뉴를 계층구조로 조회합니다. 메뉴 관리 화면에서 사용됩니다. (SUPER_ADMIN 전용)")
    @GetMapping("/tree")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<List<MenuDto.Response>>> getMenuTree() {
        log.info("Getting menu tree");
        ApiResponseDto<List<MenuDto.Response>> response = manageMenuService.getMenuTree();
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "역할별 메뉴 접근 권한 설정 (관리자 전용)", description = "특정 역할(ROLE)이 특정 메뉴에 접근할 수 있는지 설정합니다. 역할 기반 기본 권한을 관리합니다. (SUPER_ADMIN 전용)")
    @PostMapping("/{menuId}/roles/{roleId}/permissions")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> setMenuRolePermission(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable("menuId") Long menuId,
            @Parameter(description = "역할 ID", required = true) @PathVariable("roleId") String roleId,
            @Parameter(description = "접근 가능 여부 (Y/N)", required = true) @RequestParam String isAccessible,
            Authentication authentication) {

        log.info("Setting menu role permission - Menu: {}, Role: {}, Accessible: {}", menuId, roleId, isAccessible);
        ApiResponseDto<Void> response = manageMenuService.setMenuRolePermission(menuId, roleId, isAccessible, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "개별 사용자 메뉴 접근 권한 설정 (관리자 전용)", description = "특정 사용자가 특정 메뉴에 접근할 수 있는지 개별적으로 설정합니다. 역할 권한보다 우선 적용됩니다. (SUPER_ADMIN 전용)")
    @PostMapping("/{menuId}/users/{userEmail}/permissions")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> setMenuUserPermission(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable("menuId") Long menuId,
            @Parameter(description = "사용자 이메일", required = true) @PathVariable("userEmail") String userEmail,
            @RequestBody MenuDto.UserPermissionRequest requestDto,
            Authentication authentication) {
        // PathVariable의 값으로 DTO 업데이트 (userEmail은 PathVariable에서 가져옴)
        MenuDto.UserPermissionRequest updatedRequestDto = MenuDto.UserPermissionRequest.builder()
                .menuId(menuId)
                .userEmail(userEmail)  // PathVariable에서 가져온 값 사용
                .isAccessible(requestDto.getIsAccessible())
                .permissionNote(requestDto.getPermissionNote())
                .build();

        log.info("Setting menu user permission - Menu: {}, User: {}", menuId, userEmail);
        ApiResponseDto<Void> response = manageMenuService.setMenuUserPermission(updatedRequestDto, authentication.getName());
        return ResponseEntity.ok(response);
    }

    // ========== 일반 사용자용 메뉴 조회 API ==========

    @Operation(summary = "내 접근 가능 메뉴 조회 (로그인 사용자)", description = "현재 로그인한 사용자가 접근 가능한 메뉴 트리를 조회합니다. 프론트엔드 네비게이션 메뉴 구성에 사용됩니다.")
    @GetMapping("/accessible")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponseDto<List<MenuDto.Response>>> getAccessibleMenuTree(Authentication authentication) {

        String userEmail = authentication.getName();
        log.info("Getting accessible menu tree for current user: {}", userEmail);

        // 사용자 정보 조회하여 역할 확인
        User user = userService.getUserByUserEmail(userEmail);
        String roleId = user.getRoleId();

        ApiResponseDto<List<MenuDto.Response>> response = manageMenuService.getAccessibleMenuTree(userEmail, roleId);
        return ResponseEntity.ok(response);
    }

    // ========== SUPER_ADMIN 전용 사용자별 메뉴 조회 API ==========

    @Operation(summary = "특정 사용자의 접근 가능 메뉴 조회 (관리자 전용)", description = "관리자가 특정 사용자의 접근 가능한 메뉴 트리를 조회합니다. 사용자 권한 확인 및 디버깅에 사용됩니다. (SUPER_ADMIN 전용)")
    @GetMapping("/users/{userEmail}/accessible")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<List<MenuDto.Response>>> getAccessibleMenuTreeForUser(
            @Parameter(description = "사용자 이메일", required = true) @PathVariable("userEmail") String userEmail) {

        log.info("Getting accessible menu tree for user: {}", userEmail);

        // 사용자 정보 조회하여 역할 확인
        User user = userService.getUserByUserEmail(userEmail);
        String roleId = user.getRoleId();

        ApiResponseDto<List<MenuDto.Response>> response = manageMenuService.getAccessibleMenuTree(userEmail, roleId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "사용자별 메뉴 접근 권한 검증 (관리자 전용)", description = "특정 사용자가 특정 메뉴에 접근 가능한지 확인합니다. 권한 설정 후 검증 및 디버깅에 사용됩니다. (SUPER_ADMIN 전용)")
    @GetMapping("/{menuId}/users/{userEmail}/access")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Boolean>> checkMenuAccessForUser(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable("menuId") Long menuId,
            @Parameter(description = "사용자 이메일", required = true) @PathVariable("userEmail") String userEmail) {

        log.info("Checking menu access for user: {} on menu: {}", userEmail, menuId);

        // 사용자 정보 조회하여 역할 확인
        User user = userService.getUserByUserEmail(userEmail);
        String roleId = user.getRoleId();

        // 메뉴 접근 권한 확인
        boolean hasAccess = manageMenuService.hasMenuAccess(menuId, userEmail, roleId);

        ApiResponseDto<Boolean> response = ApiResponseDto.success(hasAccess);
        return ResponseEntity.ok(response);
    }

    // ========== 권한 관리 API ==========

    @Operation(summary = "메뉴별 역할 권한 목록 조회 (관리자 전용)", description = "특정 메뉴에 대한 모든 역할의 접근 권한을 조회합니다. 메뉴 상세 페이지에서 역할별 권한 관리 테이블을 구성할 때 사용됩니다. (SUPER_ADMIN 전용)")
    @GetMapping("/{menuId}/permissions/roles")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<List<MenuDto.PermissionResponse.RolePermission>>> getMenuRolePermissions(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId) {

        log.info("Getting role permissions for menu: {}", menuId);
        ApiResponseDto<List<MenuDto.PermissionResponse.RolePermission>> response = manageMenuService.getMenuRolePermissions(menuId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴별 사용자 CRUD 권한 목록 조회 (관리자 전용)", description = "특정 메뉴에 대한 모든 사용자의 CRUD 권한을 조회합니다. 메뉴 상세 페이지에서 권한 관리 테이블을 구성할 때 사용됩니다. (SUPER_ADMIN 전용)")
    @GetMapping("/{menuId}/permissions/users")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<List<MenuDto.PermissionResponse.UserPermission>>> getMenuUserPermissions(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable Long menuId) {

        log.info("Getting user permissions for menu: {}", menuId);
        ApiResponseDto<List<MenuDto.PermissionResponse.UserPermission>> response = manageMenuService.getMenuUserPermissions(menuId);
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴별 사용자 CRUD 권한 설정/수정 (관리자 전용)", description = "특정 메뉴에 대한 개별 사용자의 CRUD 권한을 설정하거나 수정합니다. 읽기(R), 생성(C), 수정(U), 삭제(D) 권한을 개별적으로 제어할 수 있습니다. (SUPER_ADMIN 전용)")
    @PostMapping("/{menuId}/permissions/users")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> setMenuUserPermission(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable("menuId") Long menuId,
            @RequestBody MenuDto.UserPermissionRequest requestDto,
            Authentication authentication) {

        // 요청 DTO에 메뉴 ID 설정 (menuId는 PathVariable에서 가져옴)
        MenuDto.UserPermissionRequest updatedRequest = MenuDto.UserPermissionRequest.builder()
                .menuId(menuId)  // PathVariable에서 가져온 값 사용
                .userEmail(requestDto.getUserEmail())
                .isAccessible(requestDto.getIsAccessible())
                .canRead(requestDto.getCanRead())
                .canWrite(requestDto.getCanWrite())
                .canUpdate(requestDto.getCanUpdate())
                .canDelete(requestDto.getCanDelete())
                .permissionNote(requestDto.getPermissionNote())
                .build();

        log.info("Setting user permission for menu: {} and user: {} by admin: {}",
                menuId, requestDto.getUserEmail(), authentication.getName());

        ApiResponseDto<Void> response = manageMenuService.setMenuUserPermission(updatedRequest, authentication.getName());
        return ResponseEntity.ok(response);
    }

    @Operation(summary = "메뉴별 사용자 권한 삭제 (기본값 복원) (관리자 전용)", description = "특정 메뉴의 개별 사용자 권한을 삭제하여 기본 권한(역할 기반)으로 복원합니다. (SUPER_ADMIN 전용)")
    @DeleteMapping("/{menuId}/permissions/users/{userEmail}")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<String>> deleteMenuUserPermission(
            @Parameter(description = "메뉴 ID", required = true) @PathVariable("menuId") Long menuId,
            @Parameter(description = "사용자 이메일", required = true) @PathVariable("userEmail") String userEmail,
            Authentication authentication) {

        log.info("Deleting user permission for menu: {} and user: {} by admin: {}",
                menuId, userEmail, authentication.getName());

        ApiResponseDto<String> response = manageMenuService.deleteMenuUserPermission(menuId, userEmail, authentication.getName());
        return ResponseEntity.ok(response);
    }
}
