package kr.wayplus.wayplus_qr.dto.response;

import java.time.LocalDateTime;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QnaQuestionResponseDto {
    private Long qnaQuestionId;
    private Long projectId;
    private String title;
    private String content;
    private String createUserEmail;
    private LocalDateTime createDate;
    private String updateUserEmail;
    private LocalDateTime lastUpdateDate;
    private String deleteUserEmail;
    private LocalDateTime deleteDate;
    private String useYn;
    private String deleteYn;
    private Long viewCount;
    private QnaAnswerResponseDto answer; // 질문에 대한 답변 정보 포함
}
