package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.request.PreRegistrationFormCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.PreRegistrationFormFieldRequestDto;
import kr.wayplus.wayplus_qr.dto.request.PreRegistrationFormUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.PreRegistrationFormResponseDto;
import kr.wayplus.wayplus_qr.dto.response.PreRegistrationFormFieldResponseDto;
import kr.wayplus.wayplus_qr.entity.PreRegistrationFormField;
import kr.wayplus.wayplus_qr.entity.FormFieldDefinitionDto;
import kr.wayplus.wayplus_qr.entity.PreRegistrationForm;
import kr.wayplus.wayplus_qr.entity.PreRegistrationFormDefinitionDto;
import kr.wayplus.wayplus_qr.exception.CustomPreRegistrationFormException;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.mapper.PreRegistrationFormFieldMapper;
import kr.wayplus.wayplus_qr.mapper.PreRegistrationFormMapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class PreRegistrationFormService {
    private final PreRegistrationFormMapper formMapper;
    private final PreRegistrationFormFieldMapper fieldMapper;
    private final ObjectMapper objectMapper;

    // 사전 신청서 정렬 필드 매핑
    private static final Map<String, String> PRE_REGISTRATION_FORM_SORT_PROPERTY_MAP = Map.of(
            "formId", "form_id",
            "projectId", "project_id",
            "formName", "form_name",
            "createDate", "create_date",
            "lastUpdateDate", "last_update_date"
            // 필요에 따라 추가
    );

    @Transactional(readOnly = true)
    public Page<PreRegistrationFormResponseDto> getFormsByProjectId(
            Long projectId,
            String searchType,
            String searchKeyword,
            Pageable pageable) {

        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank() && searchType != null) {
            switch (searchType) {
                case "formName":
                    searchColumn = "form_name";
                    break;
                case "description":
                    searchColumn = "description";
                    break;
                case "createDate":
                    searchColumn = "create_date";
                    break;
                case "lastUpdateDate":
                    searchColumn = "last_update_date";
                    break;
                // 기본적으로는 searchColumn을 null로 유지하거나, 예외 처리를 할 수 있습니다.
            }
        }

        List<PreRegistrationForm> forms = formMapper.selectFormsByProjectId(projectId, searchColumn, searchKeyword, pageable);
        if (forms == null) {
            forms = Collections.emptyList();
        }

        List<PreRegistrationFormResponseDto> dtos = forms.stream()
                .map(PreRegistrationFormResponseDto::fromEntity)
                .collect(Collectors.toList());

        long total = formMapper.countFormsByProjectId(projectId, searchColumn, searchKeyword);

        return new PageImpl<>(dtos, pageable, total);
    }

    @Transactional(readOnly = true)
    public PreRegistrationFormResponseDto getFormById(Long formId) {
        PreRegistrationForm form = formMapper.selectFormById(formId)
                .orElseThrow(() -> new CustomPreRegistrationFormException(
                        ErrorCode.PRE_REG_FORM_NOT_FOUND,
                        "신청서 양식을 찾을 수 없습니다: ID=" + formId));
        // 폼 정보 매핑
        PreRegistrationFormResponseDto dto = PreRegistrationFormResponseDto.fromEntity(form);
        // 연결된 필드 조회 및 매핑
        List<PreRegistrationFormFieldResponseDto> fieldDtos = fieldMapper.selectFieldsByFormId(formId).stream()
                .map(PreRegistrationFormFieldResponseDto::fromEntity)
                .collect(Collectors.toList());
        dto.setFields(fieldDtos);
        return dto;
    }

    /**
     * ID로 폼의 모든 필드(삭제된 필드 포함) 조회 (데이터 변환용)
     * @param formId 조회할 폼 ID
     * @return 폼 필드 엔티티 목록 (삭제된 필드 포함)
     */
    @Transactional(readOnly = true)
    public List<PreRegistrationFormField> getAllFieldsForForm(Long formId) {
        return fieldMapper.selectAllFieldsByFormId(formId);
    }

    /**
     * ID로 폼 정의 조회 (동적 필드 검증용)
     * @param formId 조회할 폼 ID
     * @return 폼 정의 DTO (필수 필드 정보 포함)
     */
    @Transactional(readOnly = true)
    public PreRegistrationFormDefinitionDto getFormDefinitionById(Long formId) {
        PreRegistrationForm form = formMapper.selectFormById(formId)
                .orElseThrow(() -> new CustomPreRegistrationFormException(
                        ErrorCode.PRE_REG_FORM_NOT_FOUND,
                        "신청서 양식을 찾을 수 없습니다: ID=" + formId));

        List<PreRegistrationFormField> fields = fieldMapper.selectFieldsByFormId(formId);

        List<FormFieldDefinitionDto> fieldDefinitions = fields.stream()
                .map(field -> FormFieldDefinitionDto.builder()
                        .fieldName(field.getFieldName())
                        .fieldLabel(field.getFieldLabel()) // 레이블도 추가 (선택 사항)
                        .fieldType(field.getFieldType()) // 타입도 추가 (선택 사항)
                        .required("Y".equalsIgnoreCase(field.getIsRequiredYn())) // isRequiredYn ('Y'/'N') -> required (boolean)
                        .build())
                .collect(Collectors.toList());

        return PreRegistrationFormDefinitionDto.builder()
                .formId(form.getFormId())
                .formName(form.getFormName())
                .fieldDefinitions(fieldDefinitions)
                .build();
    }

    /**
     * SUPER_ADMIN: 모든 또는 특정 프로젝트의 사전 신청서 목록 조회 (페이징 및 동적 정렬)
     */
    @Transactional(readOnly = true)
    public Page<PreRegistrationFormResponseDto> getAllPreRegistrationFormsForSuperAdmin(
            Long projectId,
            String searchType,
            String searchKeyword,
            Pageable pageable) {

        List<PreRegistrationForm> forms;
        long total;
        //검색타입 설정. 실제 DB 컬럼과 연동.
        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "formName": searchColumn = "form_name"; break;
                case "description": searchColumn = "description"; break;
                case "createDate": searchColumn = "create_date"; break;
                case "lastUpdateDate": searchColumn = "last_update_date"; break;
            }
        }
 
        if (projectId != null) {
            // 특정 프로젝트 ID로 필터링
            forms = formMapper.selectFormsByProjectIdForSuperAdmin(projectId, searchColumn, searchKeyword, pageable);
            total = formMapper.countFormsByProjectIdForSuperAdmin(projectId, searchColumn, searchKeyword);
        } else {
            // 모든 사전 신청서 조회
            forms = formMapper.selectAllFormsForSuperAdmin(searchColumn, searchKeyword, pageable);
            total = formMapper.countAllFormsForSuperAdmin(searchColumn, searchKeyword);
        }

        List<PreRegistrationFormResponseDto> dtos = forms.stream()
                .map(PreRegistrationFormResponseDto::fromEntity)
                .collect(Collectors.toList());

        return new PageImpl<>(dtos, pageable, total);
    }

    @Transactional
    public Long createForm(PreRegistrationFormCreateRequestDto dto, String userEmail) {
        if (formMapper.existsByProjectIdAndFormName(dto.getProjectId(), dto.getFormName())) {
            throw new CustomPreRegistrationFormException(
                    ErrorCode.PRE_REG_FORM_NAME_DUPLICATION,
                    "양식 이름이 중복되었습니다: " + dto.getFormName());
        }
        PreRegistrationForm entity = dto.toEntity(userEmail);
        entity.setCreateDate(LocalDateTime.now());
        entity.setUseYn("Y");
        entity.setDeleteYn("N");
        entity.setAutoConfirmYn(dto.getAutoConfirmYn());
        entity.setRequireConsent(dto.getRequireConsent());
        int result = formMapper.insertForm(entity);
        if (result == 0) {
            log.error("Pre-registration form creation failed for project {}", dto.getProjectId());
            throw new CustomPreRegistrationFormException(
                    ErrorCode.PRE_REG_FORM_CREATE_FAILED,
                    "신청서 양식 생성 실패");
        }
        // Insert fields if provided
        if (dto.getFields() != null) {
            for (PreRegistrationFormFieldRequestDto fDto : dto.getFields()) {
                try {
                    PreRegistrationFormField field = PreRegistrationFormField.builder()
                            .formId(entity.getFormId())
                            .fieldLabel(fDto.getFieldLabel())
                            .fieldName(fDto.getFieldName())
                            .fieldType(fDto.getFieldType())
                            .isRequiredYn(fDto.getIsRequiredYn())
                            .helpText(fDto.getHelpText())
                            .options(objectMapper.writeValueAsString(fDto.getOptions()))
                            .displayOrder(fDto.getDisplayOrder())
                            .createUserEmail(userEmail)
                            .build();
                    fieldMapper.insertField(field);
                } catch (Exception e) {
                    log.error("Error inserting form fields: {}", e.getMessage(), e);
                    throw new CustomPreRegistrationFormException(ErrorCode.PRE_REG_FORM_CREATE_FAILED, "신청서 필드 생성 중 오류가 발생했습니다.");
                }
            }
        }
        return entity.getFormId();
    }

    @Transactional
    public PreRegistrationFormResponseDto updateForm(Long formId, PreRegistrationFormUpdateRequestDto dto, String userEmail) {
        PreRegistrationForm entity = formMapper.selectFormById(formId)
                .orElseThrow(() -> new CustomPreRegistrationFormException(
                        ErrorCode.PRE_REG_FORM_NOT_FOUND,
                        "신청서 양식을 찾을 수 없습니다: ID=" + formId));
        entity.setFormName(dto.getFormName());
        entity.setDescription(dto.getDescription());
        entity.setCompletionMessage(dto.getCompletionMessage());
        entity.setAutoConfirmYn(dto.getAutoConfirmYn());
        entity.setPrivacyPolicyAgreementText(dto.getPrivacyPolicyAgreementText());
        entity.setRequireConsent(dto.getRequireConsent());
        entity.setUpdateUserEmail(userEmail);
        entity.setLastUpdateDate(LocalDateTime.now());
        formMapper.updateForm(entity);
        // Update fields if provided
        if (dto.getFields() != null) {
            // Mark existing fields as deleted
            fieldMapper.deleteFieldsByFormId(formId, userEmail);
            for (PreRegistrationFormFieldRequestDto fDto : dto.getFields()) {
                try {
                    PreRegistrationFormField field = PreRegistrationFormField.builder()
                            .formId(formId)
                            .fieldLabel(fDto.getFieldLabel())
                            .fieldName(fDto.getFieldName())
                            .fieldType(fDto.getFieldType())
                            .isRequiredYn(fDto.getIsRequiredYn())
                            .helpText(fDto.getHelpText())
                            .options(objectMapper.writeValueAsString(fDto.getOptions()))
                            .displayOrder(fDto.getDisplayOrder())
                            .createUserEmail(userEmail)
                            .build();
                    fieldMapper.insertField(field);
                } catch (Exception e) {
                    log.error("Error updating form fields: {}", e.getMessage(), e);
                    throw new CustomPreRegistrationFormException(
                            ErrorCode.PRE_REG_FORM_CREATE_FAILED,
                            "신청서 필드 수정 중 오류가 발생했습니다.");
                }
            }
        }
        return getFormById(formId);
    }

    @Transactional
    public void deleteForm(Long formId, String userEmail) {
        int result = formMapper.deleteFormById(formId, userEmail);
        if (result == 0) {
            throw new CustomPreRegistrationFormException(
                    ErrorCode.PRE_REG_FORM_NOT_FOUND,
                    "신청서 양식을 삭제할 수 없거나 존재하지 않습니다: ID=" + formId);
        }
    }

    /**
     * 페이징 및 정렬 파라미터 검증 및 준비 (PreRegistrationForm 용)
     */
    private Map<String, Object> validateAndPreparePreRegistrationFormParams(Pageable pageable) {
        Map<String, Object> params = new HashMap<>();
        params.put("offset", pageable.getOffset());
        params.put("pageSize", pageable.getPageSize());

        String sortColumn = "form_id"; // 기본 정렬 컬럼
        String sortDirection = "DESC"; // 기본 정렬 방향

        if (pageable.getSort().isSorted()) {
            Sort.Order order = pageable.getSort().iterator().next(); // 첫 번째 정렬 조건만 사용
            String property = order.getProperty();
            if (PRE_REGISTRATION_FORM_SORT_PROPERTY_MAP.containsKey(property)) {
                sortColumn = PRE_REGISTRATION_FORM_SORT_PROPERTY_MAP.get(property);
                sortDirection = order.getDirection().name();
            } else {
                log.warn("Invalid sort property received for pre-registration form: {}. Using default sort.", property);
            }
        }

        params.put("sortColumn", sortColumn);
        params.put("sortDirection", sortDirection);
        log.debug("Prepared pre-registration form query params: {}", params);
        return params;
    }
    
    /**
     * 사전 신청서 양식 복사
     * @param originalFormId 복사할 원본 사전 신청서 양식 ID
     * @param userEmail 사용자 이메일
     * @return 복사된 사전 신청서 양식 정보
     */
    @Transactional
    public PreRegistrationFormResponseDto copyForm(Long originalFormId, String userEmail) {
        // 1. 원본 사전 신청서 양식 조회
        PreRegistrationForm originalForm = formMapper.selectFormById(originalFormId)
                .orElseThrow(() -> new CustomPreRegistrationFormException(
                        ErrorCode.PRE_REG_FORM_NOT_FOUND,
                        "사전 신청서 양식을 찾을 수 없습니다: ID=" + originalFormId));
        
        // 2. 원본 사전 신청서 필드 조회
        List<PreRegistrationFormField> originalFields = fieldMapper.selectFieldsByFormId(originalFormId);
        
        // 3. 새 사전 신청서 양식 생성 (이름은 "원본이름 - copy" 형식으로 지정)
        String newFormName = originalForm.getFormName() + " - copy";
        
        // 사전 신청서 이름 중복 검사
        if (formMapper.existsByProjectIdAndFormName(originalForm.getProjectId(), newFormName)) {
            // 이름에 일련번호 추가
            int counter = 1;
            String baseFormName = newFormName;
            while (formMapper.existsByProjectIdAndFormName(originalForm.getProjectId(), newFormName)) {
                newFormName = baseFormName + " (" + counter + ")";
                counter++;
            }
        }
        
        PreRegistrationForm newForm = PreRegistrationForm.builder()
                .projectId(originalForm.getProjectId())
                .formName(newFormName)
                .description(originalForm.getDescription())
                .completionMessage(originalForm.getCompletionMessage())
                .privacyPolicyAgreementText(originalForm.getPrivacyPolicyAgreementText())
                .autoConfirmYn(originalForm.getAutoConfirmYn()) 
                .requireConsent(originalForm.getRequireConsent())
                .createUserEmail(userEmail)
                .createDate(LocalDateTime.now())
                .useYn("Y")
                .deleteYn("N")
                .build();
        
        // 4. 새 사전 신청서 양식 저장
        formMapper.insertForm(newForm);
        Long newFormId = newForm.getFormId();
        
        // 5. 사전 신청서 필드 복사
        for (PreRegistrationFormField originalField : originalFields) {
            PreRegistrationFormField newField = PreRegistrationFormField.builder()
                    .formId(newFormId)
                    .fieldLabel(originalField.getFieldLabel())
                    .fieldName(originalField.getFieldName())
                    .fieldType(originalField.getFieldType())
                    .isRequiredYn(originalField.getIsRequiredYn())
                    .helpText(originalField.getHelpText())
                    .options(originalField.getOptions())
                    .displayOrder(originalField.getDisplayOrder())
                    .createUserEmail(userEmail)
                    .createDate(LocalDateTime.now())
                    .useYn("Y")
                    .deleteYn("N")
                    .build();
            
            fieldMapper.insertField(newField);
        }
        
        // 6. 복사된 사전 신청서 양식 조회 및 반환
        return getFormById(newFormId);
    }

    /**
     * 이벤트 ID로 사전 신청서 양식 조회
     * @param eventId 이벤트 ID
     * @return 사전 신청서 양식 정보
     */
    @Transactional(readOnly = true)
    public PreRegistrationFormResponseDto getPreRegistrationFormByEventId(Long eventId) {
        PreRegistrationForm form = formMapper.selectFormByEventId(eventId)
                .orElseThrow(() -> new CustomPreRegistrationFormException(
                        ErrorCode.PRE_REG_FORM_NOT_FOUND,
                        "해당 이벤트에 연결된 사전 신청서 양식을 찾을 수 없습니다: eventId=" + eventId));
        
        // 폼 정보 매핑
        PreRegistrationFormResponseDto dto = PreRegistrationFormResponseDto.fromEntity(form);
        // 연결된 필드 조회 및 매핑
        List<PreRegistrationFormFieldResponseDto> fieldDtos = fieldMapper.selectFieldsByFormId(form.getFormId()).stream()
                .map(PreRegistrationFormFieldResponseDto::fromEntity)
                .collect(Collectors.toList());
        dto.setFields(fieldDtos);
        return dto;
    }
}
