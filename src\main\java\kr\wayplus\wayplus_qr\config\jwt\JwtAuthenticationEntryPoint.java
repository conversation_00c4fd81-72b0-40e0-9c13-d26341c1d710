package kr.wayplus.wayplus_qr.config.jwt;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ErrorDetail;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void commence(HttpServletRequest request,
                         HttpServletResponse response,
                         AuthenticationException authException) throws IOException, ServletException {
        log.warn("Responding with unauthorized error. Message - {}", authException.getMessage());

        // 표준 에러 응답 생성
        ErrorDetail errorDetail = ErrorDetail.builder()
                .code("UNAUTHENTICATED") // 또는 "INVALID_TOKEN" 등 상황에 맞는 코드
                .message("인증되지 않은 사용자입니다. 유효한 토큰이 필요합니다.")
                // .details(authException.getMessage()) // 디버깅 시 상세 메시지 포함 가능
                .build();
        ApiResponseDto<Void> errorResponse = ApiResponseDto.error(errorDetail); // 데이터는 null

        response.setStatus(HttpStatus.UNAUTHORIZED.value()); // 401 상태 코드 설정
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());

        // 응답 본문에 JSON 쓰기
        objectMapper.writeValue(response.getWriter(), errorResponse);
    }
}