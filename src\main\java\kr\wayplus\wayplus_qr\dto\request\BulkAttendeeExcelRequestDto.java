package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * 엑셀 대량 사전 신청 등록 요청 DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BulkAttendeeExcelRequestDto {
    @NotNull(message = "이벤트 ID는 필수입니다.")
    private Long eventId;
    
    private Long teamId;
    
    private String attendedYn;
    
    private String attendedConfirmYn;
    
    @NotNull(message = "엑셀 파일은 필수입니다.")
    private MultipartFile excelFile;
}