<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.MessageSenderMapper">
    <select id="selectMailServer" parameterType="String" resultType="kr.wayplus.wayplus_qr.entity.SettingMailServer">
        SELECT id, alarm_type,
               smtp_email, smtp_username,
               smtp_server, smtp_port,
               smtp_authorize_required, smtp_secure_type,
               smtp_login_id, smtp_login_pw,
               smtp_use_yn, create_id, create_date
          FROM setting_mail_server
         WHERE delete_yn = 'N' AND smtp_use_yn = 'Y' AND alarm_type = #{value}
    </select>

    <select id="selectMailFormat" parameterType="Integer" resultType="kr.wayplus.wayplus_qr.entity.SettingMailFormat">
        SELECT id, server_id, title, mail_content
          FROM setting_mail_format
         WHERE delete_yn = 'N' AND use_yn = 'Y' AND id = #{value}
           AND (start_date IS NULL OR start_date &lt; now())
           AND (expire_date IS NULL OR expire_date &gt; now())
    </select>
</mapper>