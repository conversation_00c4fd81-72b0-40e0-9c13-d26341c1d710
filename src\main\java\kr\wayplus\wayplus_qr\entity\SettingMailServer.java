package kr.wayplus.wayplus_qr.entity;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SettingMailServer{
    private int id;
    private String alarmType;
    private String smtpEmail;
    private String smtpUsername;
    private String smtpServer;
    private int smtpPort;
    private String smtpAuthorizeRequired;
    private String smtpSecureType;
    private String smtpLoginId;
    private String smtpLoginPw;
    private String smtpUseYn;
    private String createId;
    private String createDate;
    private String createDateFormat;
    private String lastUpdateId;
    private String lastUpdateDate;
    private String deleteYn;
    private String deleteId;
    private String deleteDate;
}
