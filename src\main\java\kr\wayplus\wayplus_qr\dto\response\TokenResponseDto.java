package kr.wayplus.wayplus_qr.dto.response; // 패키지 경로 확인 및 필요시 수정

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL) // Null 필드는 응답에서 제외 (선택 사항)
public class TokenResponseDto {
    private String tokenType; // tokenType 필드 추가 (예: "Bearer")
    private String accessToken;
    private String refreshToken;
    private Long expiresIn; // Access Token 만료 시간 (초)
    private Long accessTokenExpiresIn; // 명확성을 위해 expiresIn 대신 사용 가능

    // 최초 비밀번호 변경 필요 여부 플래그 (로그인 응답에만 포함될 수 있음)
    private Boolean forcePasswordChange;
    private String message; // 상태 메시지 (예: "Password change required")
}
