package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.entity.QrScanLog;
import kr.wayplus.wayplus_qr.mapper.QrCodeMapper;
import kr.wayplus.wayplus_qr.mapper.QrScanLogMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

@Service
@RequiredArgsConstructor
@Slf4j
public class QrScanLogService {
    private final QrScanLogMapper qrScanLogMapper;
    private final QrCodeMapper qrCodeMapper;

    /**
     * QR 코드 스캔을 기록하고 관련 통계를 업데이트합니다.

     * @param qrCodeId       스캔된 QR 코드 ID
     * @param ipAddress      스캐너 IP 주소
     * @param userAgent      스캐너 User Agent
     * @param locationData   위치 정보 (JSON 형태의 문자열)
     * @param scannerUserEmail 스캔한 사용자 이메일 (인증된 경우)
     */
    @Transactional
    public void logScan(Long qrCodeId, String ipAddress, String userAgent, String locationData, String scannerUserEmail) {
        try {
            // 1. 스캔 로그 객체 생성
            QrScanLog scanLog = QrScanLog.builder()
                    .qrCodeId(qrCodeId)
                    .scanTime(LocalDateTime.now())
                    .ipAddress(ipAddress)
                    .userAgent(userAgent)
                    .locationData(locationData)
                    .scannerUserEmail(scannerUserEmail)
                    .isUniqueScan(true) // TODO: 유니크 스캔 로직 구현 필요 (예: 특정 시간 내 동일 IP/UserAgent 체크)
                    .build();

            // 2. 스캔 로그 DB 저장
            qrScanLogMapper.insertQrScanLog(scanLog);
            log.debug("QR Scan logged for qrCodeId: {}", qrCodeId);

            // 3. qrCodeId로 qrUuid 조회
            String qrUuid = qrCodeMapper.selectQrUuidById(qrCodeId);

            // 4. qrUuid가 존재하면 스캔 횟수 증가
            if (qrUuid != null && !qrUuid.isEmpty()) {
                qrCodeMapper.incrementScanCount(qrUuid);
                log.debug("Scan count incremented for qrUuid: {}", qrUuid);
            } else {
                log.warn("Could not find qrUuid for qrCodeId: {} to increment scan count.", qrCodeId);
            }

        } catch (Exception e) {
            log.error("Failed to log QR scan or increment count for qrCodeId: {}", qrCodeId, e);
        }
    }
}
