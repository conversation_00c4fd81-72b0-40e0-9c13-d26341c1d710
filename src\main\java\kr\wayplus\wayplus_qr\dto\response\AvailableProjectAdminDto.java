package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter // Mapper에서 isAssignedToCurrentProject 값을 설정하기 위해 Setter 추가
@NoArgsConstructor
@AllArgsConstructor
public class AvailableProjectAdminDto {
    private String userEmail;
    private String name; // settingDDL.sql 의 users 테이블 컬럼명과 일치
    private String roleId;
    private String status;
    private Boolean isAssignedToCurrentProject; // Boolean 사용 (nullable 고려)
}
