package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter // Controller에서 @RequestBody로 받을 때 Setter 또는 모든 필드를 받는 생성자가 필요할 수 있음
@NoArgsConstructor
public class ProjectCreateRequestDto {

    @NotBlank(message = "프로젝트 이름은 필수입니다.")
    @Size(max = 100, message = "프로젝트 이름은 최대 100자까지 가능합니다.")
    private String projectName;

    @Size(max = 500, message = "설명은 최대 500자까지 가능합니다.")
    private String description;

    @Size(max = 255, message = "관리자 이메일은 최대 255자까지 가능합니다.")
    private String projectAdminUserEmail; // 프로젝트 관리자 (선택)
}
