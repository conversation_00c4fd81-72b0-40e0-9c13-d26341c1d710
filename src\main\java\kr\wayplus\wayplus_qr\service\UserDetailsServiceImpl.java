package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class UserDetailsServiceImpl implements UserDetailsService {

    private final UserMapper userMapper;

    /**
     * 사용자 이름(여기서는 이메일)을 기반으로 사용자 정보를 로드합니다.
     *
     * @param username
     * @return
     * @throws UsernameNotFoundException
     */
    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // UserMapper를 통해 DB에서 사용자 정보 조회 (userEmail 기준)
        User user = userMapper.selectUserByEmail(username)
                .orElseThrow(() -> new UsernameNotFoundException("사용자를 찾을 수 없습니다: " + username));

        // User 엔티티가 UserDetails를 구현하고 있으므로 그대로 반환
        return user;
    }
}
