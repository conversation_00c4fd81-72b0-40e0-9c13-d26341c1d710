package kr.wayplus.wayplus_qr.dto.request;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor // Jackson 파싱을 위해 기본 생성자 추가
public class QrCodeDotsOptionsDto {
    private String color = "#000000"; // 기본값: 검정
    private String type = "square";   // 기본값: 사각형
    private String eyeColor;          // 기본값 없음 (null일 경우 color 값을 사용하도록 처리)
    private String eyeType = "square";    // 기본값: 사각형
}
