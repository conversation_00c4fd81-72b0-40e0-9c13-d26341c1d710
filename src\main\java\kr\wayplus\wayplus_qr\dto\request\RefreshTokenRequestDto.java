package kr.wayplus.wayplus_qr.dto.request; // 패키지 경로 수정

import jakarta.validation.constraints.NotBlank;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Getter
@Setter // Controller에서 @RequestBody로 받기 위해 필요
@NoArgsConstructor // 기본 생성자 추가 (JSON 역직렬화에 필요)
@AllArgsConstructor // 모든 필드를 파라미터로 받는 생성자 추가
@Builder // Builder 어노테이션 추가
public class RefreshTokenRequestDto {

    @NotBlank(message = "Refresh Token은 필수 입력값입니다.")
    private String refreshToken;
}
