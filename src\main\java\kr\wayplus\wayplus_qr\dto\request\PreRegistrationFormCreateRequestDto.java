package kr.wayplus.wayplus_qr.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import kr.wayplus.wayplus_qr.dto.request.PreRegistrationFormFieldRequestDto;
import kr.wayplus.wayplus_qr.entity.PreRegistrationForm;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.util.List;

@Data
@NoArgsConstructor
@Builder
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PreRegistrationFormCreateRequestDto {

    @NotNull(message = "Project ID는 필수입니다.")
    private Long projectId;

    @NotBlank(message = "양식 이름은 필수입니다.")
    @Size(max = 255, message = "양식 이름은 255자를 초과할 수 없습니다.")
    private String formName;

    @Size(max = 2000, message = "설명은 2000자를 초과할 수 없습니다.")
    private String description;

    @Size(max = 1000, message = "완료 메시지는 1000자를 초과할 수 없습니다.")
    private String completionMessage;

    private String autoConfirmYn;

    @Size(max = 1000, message = "개인정보 처리방침 동의 문구는 1000자를 초과할 수 없습니다.")
    private String privacyPolicyAgreementText;

    private Boolean requireConsent;
    private List<PreRegistrationFormFieldRequestDto> fields;

    public PreRegistrationForm toEntity(String userEmail) {
        return PreRegistrationForm.builder()
                .projectId(this.projectId)
                .formName(this.formName)
                .description(this.description)
                .completionMessage(this.completionMessage)
                .autoConfirmYn(this.autoConfirmYn)
                .privacyPolicyAgreementText(this.privacyPolicyAgreementText)
                .requireConsent(this.requireConsent)
                .createUserEmail(userEmail)
                .build();
    }
}
