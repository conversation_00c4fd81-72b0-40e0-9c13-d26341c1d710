package kr.wayplus.wayplus_qr.dto;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AttendeeManualCheckDto {
    private Long attendeeId;
    private String attendeeName;
    private String attendeeContact;
    private String attendeeEmail;
    private String attendedYn; // ENUM('Y', 'N', 'PENDING')
    private String attendedConfirmYn; // ENUM('Y', 'N')
    private LocalDateTime registrationDate;
}
