package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * QR 코드 상태별 분포 DTO
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QrStatusDistributionDto {
    private Map<String, Long> statusCounts; // 상태별 QR 코드 수
    private Map<String, Double> statusPercentage; // 상태별 비율(%)
}
