package kr.wayplus.wayplus_qr.service;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import org.jfree.graphics2d.svg.SVGGraphics2D;
import java.awt.Color;
import java.awt.Point;
import java.awt.image.BufferedImage;
import java.util.HashSet;
import java.util.Set;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;
import javax.imageio.ImageIO;
import java.io.IOException;
import java.io.InputStream;

@Service
@Slf4j
public class QrCodeGeneratorService {

    /**
     * 주어진 콘텐츠로 QR 코드 SVG 이미지를 생성하고 지정된 경로에 파일로 저장합니다.
     * 최적화된 SVG 생성 로직(사각형 병합) 사용.
     *
     * @param content          QR 코드로 인코딩할 텍스트 콘텐츠
     * @param width            생성할 QR 코드 이미지의 너비 (픽셀 단위, JFreeSVG는 이 값을 사용)
     * @param height           생성할 QR 코드 이미지의 높이 (픽셀 단위, JFreeSVG는 이 값을 사용)
     * @param filePath         저장할 SVG 파일의 전체 경로
     * @param foregroundColor  QR 코드 전경색 (#RRGGBB 형식)
     * @param backgroundColor  QR 코드 배경색 (#RRGGBB 형식)
     * @param finderColor      QR 코드 파인더 패턴 색상 (#RRGGBB 형식)
     * @param finderType       QR 코드 파인더 패턴 모양 (CIRCLE, SQUARE)
     * @param logoInputStream  로고 이미지의 InputStream (없으면 null)
     * @param logoRatio        로고 크기 비율 (0.0 ~ 1.0, 예: 0.2는 20%)
     * @param errorCorrectionLevel 오류 복원 수준 (L, M, Q, H)
     * @throws QRcodeException QR 코드 생성 또는 파일 저장 중 오류 발생 시
     */
    public void generateQrCodeSvgFile(String content, int width, int height, String filePath,
                                       String foregroundColor, String backgroundColor, String finderColor,
                                       String finderType, String logoImagePath, double logoRatio,
                                       ErrorCorrectionLevel errorCorrectionLevel) {
        try {
            Path path = Paths.get(filePath);
            Path parentDir = path.getParent();
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
                log.info("Created parent directory: {}", parentDir);
            }
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            Map<EncodeHintType, Object> hints = new HashMap<>();
            // 파라미터로 받은 오류 복원 수준 무조건 사용, 기본값은 H (최고 수준)
            // L: 약 7% 오류 복원 가능, M: 약 15% 오류 복원 가능, Q: 약 25% 오류 복원 가능, H: 약 30% 오류 복원 가능
            ErrorCorrectionLevel ecLevel;
            if (errorCorrectionLevel != null) {
                ecLevel = errorCorrectionLevel;
                log.info("[QrCodeGenerator] Using user-specified error correction level: {}", errorCorrectionLevel);
            } else {
                ecLevel = ErrorCorrectionLevel.H; // 기본값으로 가장 높은 오류 복원 수준 사용
                log.info("[QrCodeGenerator] No error correction level specified, using default level H");
            }
            hints.put(EncodeHintType.ERROR_CORRECTION, ecLevel);
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(EncodeHintType.MARGIN, 0);

            BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, 0, 0, hints);

            InputStream logoInputStream = null;
            try {
                if (logoImagePath != null && !logoImagePath.isEmpty()) {
                    logoInputStream = Files.newInputStream(Paths.get(logoImagePath));
                }
                String svgContent = generateOptimizedSvgFromBitMatrix(bitMatrix, width, height, foregroundColor, backgroundColor, finderColor, finderType != null ? finderType : "square", logoInputStream, logoRatio);

                Files.writeString(path, svgContent, StandardCharsets.UTF_8);

                log.info("Successfully generated optimized QR Code SVG file at: {}", filePath);
            } finally {
                if (logoInputStream != null) {
                    try {
                        logoInputStream.close();
                    } catch (IOException e) {
                        log.warn("Failed to close logo input stream: {}", e.getMessage());
                    }
                }
            }
        } catch (WriterException e) {
            log.error("Could not generate QR Code BitMatrix, WriterException :: {}", e.getMessage());
            throw new QRcodeException(ErrorCode.QR_CODE_GENERATION_FAILED, "QR 코드 BitMatrix 생성 중 오류 발생 (ZXing)");
        } catch (IOException e) {
            log.error("Could not create directory or write QR Code SVG file, IOException :: {}", e.getMessage());
            throw new QRcodeException(ErrorCode.QR_CODE_FILE_SAVE_FAILED, "QR 코드 SVG 파일 저장 또는 폴더 생성 중 오류 발생: " + filePath);
        } catch (Exception e) {
            log.error("Unexpected error during QR Code SVG generation/saving, Exception :: {}", e.getMessage(), e);
            throw new QRcodeException(ErrorCode.QR_CODE_GENERATION_FAILED, "QR 코드 SVG 생성/저장 중 예기치 않은 오류 발생.");
        }
    }

    /**
     * BitMatrix 객체로부터 최적화된 SVG 형식의 문자열을 생성합니다.
     * 인접한 검은색 모듈을 감지하여 더 큰 사각형으로 병합하여 그립니다.
     *
     * @param bitMatrix         QR 코드 데이터가 포함된 BitMatrix 객체
     * @param svgWidth          생성할 SVG 요소의 너비
     * @param svgHeight         생성할 SVG 요소의 높이
     * @param foregroundColorHex QR 코드 전경색 (#RRGGBB 형식)
     * @param backgroundColorHex QR 코드 배경색 (#RRGGBB 형식)
     * @param logoInputStream    로고 이미지의 InputStream (없으면 null)
     * @param logoRatio          로고 크기 비율 (0.0 ~ 1.0)
     * @return 최적화된 SVG 형식의 문자열
     */
    private String generateOptimizedSvgFromBitMatrix(BitMatrix bitMatrix, int svgWidth, int svgHeight,
                                                     String foregroundColorHex, String backgroundColorHex,
                                                     String finderPatternColorHex, String finderPatternType,
                                                     InputStream logoInputStream, double logoRatio) {
        int matrixWidth = bitMatrix.getWidth();
        int matrixHeight = bitMatrix.getHeight();
        Set<Point> visitedPoints = new HashSet<>();
        SVGGraphics2D g2 = new SVGGraphics2D(svgWidth, svgHeight);

        double moduleSize = Math.min((double) svgWidth / matrixWidth, (double) svgHeight / matrixHeight);
        double startOffsetX = (svgWidth - matrixWidth * moduleSize) / 2.0;
        double startOffsetY = (svgHeight - matrixHeight * moduleSize) / 2.0;
        Color bgColor, fgColor, finderColor;
        // 배경색
        try { bgColor = Color.decode(backgroundColorHex); } catch (Exception e) { bgColor = Color.WHITE; /* log */ }
        try { fgColor = Color.decode(foregroundColorHex); } catch (Exception e) { fgColor = Color.BLACK; /* log */ }
        try { finderColor = Color.decode(finderPatternColorHex != null && !finderPatternColorHex.isBlank() ? finderPatternColorHex : foregroundColorHex); } catch (Exception e) { finderColor = fgColor; /* log */ }
        g2.setColor(bgColor);
        g2.fillRect(0, 0, svgWidth, svgHeight);

        // 파인더 패턴 색상 (null이면 전경색 사용)
        if (finderPatternColorHex != null && !finderPatternColorHex.isBlank()) {
            try {
                finderColor = Color.decode(finderPatternColorHex);
            } catch (NumberFormatException e) {
                log.warn("Invalid finder pattern color format: '{}'. Using foreground color.", finderPatternColorHex);
                finderColor = fgColor;
            }
        } else {
            finderColor = fgColor;
        }


        // QR 코드 모듈 그리기
        // 루프 시작 전에 기본 전경색 설정
        g2.setColor(fgColor);

        for (int y = 0; y < matrixHeight; y++) {
            for (int x = 0; x < matrixWidth; x++) {
                Point currentPoint = new Point(x, y);

                if (visitedPoints.contains(currentPoint)) {
                    continue;
                }

                boolean isFinder = isFinderPattern(x, y, matrixWidth, matrixHeight);

                // 현재 모듈의 화면상 위치 계산 (moduleSize 및 오프셋 사용)
                int renderX = (int) Math.round(startOffsetX + x * moduleSize);
                int renderY = (int) Math.round(startOffsetY + y * moduleSize);

                if (isFinder) {
                    // 파인더 패턴 스타일 지원 확장
                    int patternStartX = -1, patternStartY = -1;
                    int patternSize = 7;

                    // 파인더 패턴의 시작 위치 결정
                    if (x < patternSize && y < patternSize) { 
                        patternStartX = 0; patternStartY = 0; // 좌상단
                    }
                    else if (x >= matrixWidth - patternSize && y < patternSize) { 
                        patternStartX = matrixWidth - patternSize; patternStartY = 0; // 우상단
                    }
                    else if (x < patternSize && y >= matrixHeight - patternSize) { 
                        patternStartX = 0; patternStartY = matrixHeight - patternSize; // 좌하단
                    }

                    if (patternStartX != -1) {
                        log.debug("Drawing styled finder pattern starting at ({}, {}) with type '{}'", patternStartX, patternStartY, finderPatternType);

                        // 파인더 패턴 크기 계산
                        double outerSize = patternSize * moduleSize; // 7x7 모듈
                        double outerX = startOffsetX + patternStartX * moduleSize;
                        double outerY = startOffsetY + patternStartY * moduleSize;

                        double innerHoleSize = (patternSize - 2) * moduleSize; // 5x5 모듈
                        double innerHoleX = startOffsetX + (patternStartX + 1) * moduleSize;
                        double innerHoleY = startOffsetY + (patternStartY + 1) * moduleSize;

                        double centerSize = (patternSize - 4) * moduleSize; // 3x3 모듈
                        double centerX = startOffsetX + (patternStartX + 2) * moduleSize;
                        double centerY = startOffsetY + (patternStartY + 2) * moduleSize;

                        // 두 가지 기본 스타일만 지원 (circle과 square)
                        if ("circle".equalsIgnoreCase(finderPatternType) || "dot".equalsIgnoreCase(finderPatternType) || "rounded".equalsIgnoreCase(finderPatternType)) {
                            // 원형 스타일
                            // 1. 바깥쪽 원
                            g2.setColor(finderColor);
                            g2.fillOval((int)Math.round(outerX), (int)Math.round(outerY), (int)Math.round(outerSize), (int)Math.round(outerSize));

                            // 2. 안쪽 구멍 (배경색)
                            g2.setColor(bgColor);
                            g2.fillOval((int)Math.round(innerHoleX), (int)Math.round(innerHoleY), (int)Math.round(innerHoleSize), (int)Math.round(innerHoleSize));

                            // 3. 중앙 점
                            g2.setColor(finderColor);
                            g2.fillOval((int)Math.round(centerX), (int)Math.round(centerY), (int)Math.round(centerSize), (int)Math.round(centerSize));
                        } else {
                            // 사각형 스타일 (기본값, "square"나 기타 인식할 수 없는 타입)
                            // 1. 바깥쪽 사각형
                            g2.setColor(finderColor);
                            g2.fillRect((int)Math.round(outerX), (int)Math.round(outerY), 
                                       (int)Math.round(outerSize), (int)Math.round(outerSize));
                            
                            // 2. 안쪽 구멍 (배경색)
                            g2.setColor(bgColor);
                            g2.fillRect((int)Math.round(innerHoleX), (int)Math.round(innerHoleY), 
                                       (int)Math.round(innerHoleSize), (int)Math.round(innerHoleSize));
                            
                            // 3. 중앙 점
                            g2.setColor(finderColor);
                            g2.fillRect((int)Math.round(centerX), (int)Math.round(centerY), 
                                       (int)Math.round(centerSize), (int)Math.round(centerSize));
                        }

                        // 방문 처리
                        for (int dy = 0; dy < patternSize; dy++) {
                            for (int dx = 0; dx < patternSize; dx++) {
                                visitedPoints.add(new Point(patternStartX + dx, patternStartY + dy));
                            }
                        }
                    }
                } else if (bitMatrix.get(x, y)) {
                    // --- 검은색 모듈 그리기 (SQUARE 파인더 또는 데이터 영역) ---

                     // 개별 모듈 렌더링 크기 (다음 모듈 시작점과의 차이로 계산하여 빈틈 최소화)
                    int renderWidth = (int) Math.round(startOffsetX + (x + 1) * moduleSize) - renderX;
                    int renderHeight = (int) Math.round(startOffsetY + (y + 1) * moduleSize) - renderY;


                    if (isFinder && "square".equals(finderPatternType)) {
                        // SQUARE 파인더 패턴: 개별 사각형 모듈
                        g2.setColor(finderColor);
                        g2.fillRect(renderX, renderY, renderWidth, renderHeight);
                        visitedPoints.add(currentPoint);
                        log.trace("Drawing SQUARE finder module at ({}, {})", x, y);

                    } else if (!isFinder) {
                        // 일반 데이터 영역: 최적화 또는 개별 사각형
                        g2.setColor(fgColor);
                        Point rectSize = getMaxRectangleSize(currentPoint, bitMatrix, visitedPoints, matrixWidth, matrixHeight);
                        if (rectSize != null) {
                            // 최적화된 사각형 크기 계산
                             int optimizedWidth = (int) Math.round(startOffsetX + (x + rectSize.x) * moduleSize) - renderX;
                             int optimizedHeight = (int) Math.round(startOffsetY + (y + rectSize.y) * moduleSize) - renderY;

                            g2.fillRect(renderX, renderY, optimizedWidth, optimizedHeight);
                             log.trace("Drawing optimized rect at ({},{}) size ({},{})", x, y, rectSize.x, rectSize.y);
                            // 방문 기록
                            for (int dy = 0; dy < rectSize.y; dy++) {
                                for (int dx = 0; dx < rectSize.x; dx++) {
                                    visitedPoints.add(new Point(x + dx, y + dy));
                                }
                            }
                        } else {
                            // 단일 사각형 모듈
                            g2.fillRect(renderX, renderY, renderWidth, renderHeight);
                            log.trace("Drawing single regular module at ({},{})", x, y);
                            visitedPoints.add(currentPoint);
                        }
                    }
                }
            }
        }

        // 3. 로고 추가
        if (logoInputStream != null && logoRatio > 0) {
            try {
                BufferedImage logo = ImageIO.read(logoInputStream);
                if (logo != null) {
                    int logoWidth = logo.getWidth();
                    int logoHeight = logo.getHeight();

                    // 로고 크기 계산 (QR 코드 크기의 비율)
                    double maxLogoWidth = svgWidth * logoRatio;
                    double maxLogoHeight = svgHeight * logoRatio;

                    double scale = Math.min(maxLogoWidth / logoWidth, maxLogoHeight / logoHeight);

                    int scaledLogoWidth = (int) (logoWidth * scale);
                    int scaledLogoHeight = (int) (logoHeight * scale);

                    int logoX = (svgWidth - scaledLogoWidth) / 2;
                    int logoY = (svgHeight - scaledLogoHeight) / 2;

                    // 로고 그리기
                    g2.drawImage(logo, logoX, logoY, scaledLogoWidth, scaledLogoHeight, null);
                    log.info("Logo successfully added to QR Code SVG.");
                }
            } catch (IOException e) {
                log.error("Could not read logo image from InputStream: {}", e.getMessage());
            }
        }

        // 4. SVG 문자열 반환
        String svgElement = g2.getSVGElement(null); // 루트 요소 ID는 null

        // 수동으로 style 추가 (rect에 crispEdges 적용)
        // viewBox 추가하여 스케일 가능하도록 함
        String finalSvg = svgElement.replaceFirst("<svg ",
                String.format("<svg viewBox=\"0 0 %d %d\" ", svgWidth, svgHeight))
                .replaceFirst(">",
                ">\n<style type=\"text/css\">rect { shape-rendering: crispEdges; }</style>\n");

        return finalSvg;
    }

    /**
     * 주어진 좌표가 QR 코드의 파인더 패턴 영역 내에 있는지 확인합니다.
     *
     * @param x           확인할 x 좌표
     * @param y           확인할 y 좌표
     * @param matrixWidth QR 코드 매트릭스의 너비
     * @param matrixHeight QR 코드 매트릭스의 높이
     * @return 파인더 패턴 영역 내에 있으면 true, 아니면 false
     */
    private boolean isFinderPattern(int x, int y, int matrixWidth, int matrixHeight) {
        int patternSize = 7; // 파인더 패턴 크기 (일반적으로 7x7)

        // 좌상단 패턴 영역 확인
        if (x >= 0 && x < patternSize && y >= 0 && y < patternSize) {
            return true;
        }
        // 우상단 패턴 영역 확인
        if (x >= matrixWidth - patternSize && x < matrixWidth && y >= 0 && y < patternSize) {
            return true;
        }
        // 좌하단 패턴 영역 확인
        if (x >= 0 && x < patternSize && y >= matrixHeight - patternSize && y < matrixHeight) {
            return true;
        }
        return false;
    }


    /**
     * 주어진 시작점에서 시작하는 가장 큰 검은색 사각형의 크기(너비, 높이)를 찾습니다.
     * 방문한 점이나 파인더 패턴 영역은 건너뜁니다.
     *
     * @param startPoint   시작점 좌표
     * @param bitMatrix    QR 코드 데이터
     * @param visitedPoints 방문한 점들의 집합
     * @param matrixWidth  매트릭스 너비 (파라미터 추가)
     * @param matrixHeight 매트릭스 높이 (파라미터 추가)
     * @return 최대 사각형의 크기 (Point.x = 너비, Point.y = 높이), 없으면 null
     */
    private Point getMaxRectangleSize(Point startPoint, BitMatrix bitMatrix, Set<Point> visitedPoints, int matrixWidth, int matrixHeight) {

        // 시작점 자체가 검은색이 아니거나, 이미 방문했거나, 파인더 패턴 영역이면 null 반환
        if (!bitMatrix.get(startPoint.x, startPoint.y)
            || visitedPoints.contains(startPoint)
            || isFinderPattern(startPoint.x, startPoint.y, matrixWidth, matrixHeight)) { // 파인더 패턴 체크 추가
            return null;
        }

        int maxWidth = 0;
        // 1. 최대 너비 찾기 (오른쪽으로 확장)
        while (startPoint.x + maxWidth < matrixWidth) {
            Point checkPoint = new Point(startPoint.x + maxWidth, startPoint.y);
            // 다음 점이 검은색이고, 방문하지 않았고, 파인더 패턴이 아닌 경우에만 확장
            if (bitMatrix.get(checkPoint.x, checkPoint.y)
                && !visitedPoints.contains(checkPoint)
                && !isFinderPattern(checkPoint.x, checkPoint.y, matrixWidth, matrixHeight)) { // 파인더 패턴 체크 추가
                 maxWidth++;
            } else {
                break; // 확장 중단
            }
        }

        if (maxWidth < 1) return null; // 1x1 조차 불가능하면 null

        int maxHeight = 1;
        // 2. 찾은 너비를 유지하면서 최대 높이 찾기 (아래쪽으로 확장)
        boolean canExtendHeight = true;
        while (startPoint.y + maxHeight < matrixHeight && canExtendHeight) {
            // 현재 높이(maxHeight)에서 너비(maxWidth)만큼의 모든 점이 조건을 만족하는지 확인
            for (int xOffset = 0; xOffset < maxWidth; xOffset++) {
                Point currentPoint = new Point(startPoint.x + xOffset, startPoint.y + maxHeight);
                // 다음 행의 점이 흰색이거나, 이미 방문했거나, 파인더 패턴 영역이면 높이 확장 중단
                if (!bitMatrix.get(currentPoint.x, currentPoint.y)
                    || visitedPoints.contains(currentPoint)
                    || isFinderPattern(currentPoint.x, currentPoint.y, matrixWidth, matrixHeight)) { // 파인더 패턴 체크 추가
                    canExtendHeight = false;
                    break;
                }
            }
            if (canExtendHeight) {
                maxHeight++;
            }
        }

        return new Point(maxWidth, maxHeight);
    }

}
