package kr.wayplus.wayplus_qr.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Size;
import jakarta.validation.Valid;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;
import kr.wayplus.wayplus_qr.dto.request.PreRegistrationFormFieldRequestDto;

// Removed @AllArgsConstructor and @Builder to use default constructor for Jackson
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@NoArgsConstructor
public class PreRegistrationFormUpdateRequestDto {

    @NotBlank(message = "양식 이름은 필수입니다.")
    @Size(max = 255, message = "양식 이름은 255자를 초과할 수 없습니다.")
    private String formName;

    @Size(max = 2000, message = "설명은 2000자를 초과할 수 없습니다.")
    private String description;

    @Size(max = 1000, message = "완료 메시지는 1000자를 초과할 수 없습니다.")
    private String completionMessage;

    private String autoConfirmYn;

    @Size(max = 1000, message = "개인정보 처리방침 동의 문구는 1000자를 초과할 수 없습니다.")
    private String privacyPolicyAgreementText;

    private Boolean requireConsent;

    @NotEmpty(message = "필드 목록은 비어 있을 수 없습니다.")
    @Valid
    private List<PreRegistrationFormFieldRequestDto> fields;
}
