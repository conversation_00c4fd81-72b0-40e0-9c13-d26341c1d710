package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.response.AttendeeRedemptionDetailsResponseDto;
import kr.wayplus.wayplus_qr.dto.response.BenefitStatusDto;
import kr.wayplus.wayplus_qr.entity.Attendee;
import kr.wayplus.wayplus_qr.entity.AttendeeBenefitRedemption;
import kr.wayplus.wayplus_qr.entity.EventBenefit;
import kr.wayplus.wayplus_qr.exception.CustomEventException;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.mapper.AttendeeBenefitRedemptionMapper;
import kr.wayplus.wayplus_qr.mapper.AttendeeMapper;
import kr.wayplus.wayplus_qr.mapper.EventBenefitMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class AdminAttendeeRedemptionService {

    private final AttendeeMapper attendeeMapper;
    private final EventBenefitMapper benefitMapper;
    private final AttendeeBenefitRedemptionMapper redemptionMapper;
    private static final Logger log = LoggerFactory.getLogger(AdminAttendeeRedemptionService.class);

    /**
     * QR 확인 코드로 참석자 + 혜택 사용 현황 조회
     */
    public AttendeeRedemptionDetailsResponseDto getRedemptionDetails(String confirmationCode) {
        Attendee attendee = attendeeMapper.selectAttendeeByConfirmationCodeSimple(confirmationCode);
        if (attendee == null) {
            throw new CustomEventException(ErrorCode.ATTENDEE_NOT_FOUND);
        }
        List<EventBenefit> benefits = benefitMapper.selectActiveBenefitsByEventId(attendee.getEventId());
        List<AttendeeBenefitRedemption> redemptions = redemptionMapper.selectRedemptionsByAttendeeId(attendee.getAttendeeId());
        Map<Long, AttendeeBenefitRedemption> redemptionMap = redemptions.stream()
                .collect(Collectors.toMap(AttendeeBenefitRedemption::getBenefitId, r -> r));
        List<BenefitStatusDto> benefitStatus = benefits.stream().map(b -> {
            AttendeeBenefitRedemption redemption = redemptionMap.get(b.getBenefitId());
            int used = benefitMapper.countRedeemedByBenefitId(b.getBenefitId());
            Integer remaining = b.getQuantity() == null ? null : Math.max(b.getQuantity() - used, 0);
            return BenefitStatusDto.builder()
                    .benefitId(b.getBenefitId())
                    .benefitCode(b.getBenefitCode())
                    .benefitName(b.getBenefitName())
                    .redeemed(redemption != null)
                    .redeemedAt(redemption != null ? redemption.getRedeemedAt() : null)
                    .remainingQuantity(remaining)
                    .build();
        }).toList();
        return AttendeeRedemptionDetailsResponseDto.builder()
                .attendeeId(attendee.getAttendeeId())
                .attendeeName(attendee.getAttendeeName())
                .eventId(attendee.getEventId())
                .eventName(attendee.getEventName())
                .confirmationCode(attendee.getConfirmationCode())
                .benefits(benefitStatus)
                .build();
    }

    /**
     * 혜택 사용 처리
     */
    @Transactional
    public void redeemBenefit(String confirmationCode, Long benefitId, String adminEmail) {
        Attendee attendee = attendeeMapper.selectAttendeeByConfirmationCodeSimple(confirmationCode);
        if (attendee == null) {
            throw new CustomEventException(ErrorCode.ATTENDEE_NOT_FOUND);
        }
        // 참석 확정 여부 및 실제 참석 여부 검증
        if (!"Y".equals(attendee.getAttendedConfirmYn())) {
            throw new CustomEventException(ErrorCode.ATTENDEE_NOT_CONFIRMED);
        }
        if (!"Y".equals(attendee.getAttendedYn())) {
            throw new CustomEventException(ErrorCode.ATTENDANCE_NOT_PERMITTED);
        }
        EventBenefit benefit = benefitMapper.selectBenefitById(benefitId);
        if (benefit == null) {
            throw new CustomEventException(ErrorCode.BENEFIT_NOT_FOUND);
        }
        // ----- 재고 확인 -----
        Integer initialQty = benefit.getQuantity();
        if (initialQty != null) {
            int usedCount = benefitMapper.countRedeemedByBenefitId(benefitId);
            if (usedCount >= initialQty) {
                throw new CustomEventException(ErrorCode.BENEFIT_OUT_OF_STOCK, "혜택 재고가 부족합니다.");
            }
        }
        Optional<AttendeeBenefitRedemption> existing = redemptionMapper.selectByAttendeeAndBenefit(attendee.getAttendeeId(), benefitId);
        if (existing.isPresent()) {
            throw new CustomEventException(ErrorCode.BENEFIT_ALREADY_REDEEMED, "이미 사용된 혜택입니다.");
        }
        AttendeeBenefitRedemption redemption = AttendeeBenefitRedemption.builder()
                .attendeeId(attendee.getAttendeeId())
                .benefitId(benefitId)
                .redeemedAt(LocalDateTime.now())
                .redeemedByUserEmail(adminEmail)
                .build();
        redemptionMapper.insertRedemption(redemption);
    }

    /**
     * 혜택 사용 취소 처리
     */
    @Transactional
    public void cancelBenefitRedemption(String confirmationCode, Long benefitId, String adminEmail) {
        log.info("Admin {} requests cancellation of benefit {} for confirmationCode {}", adminEmail, benefitId, confirmationCode);
        Attendee attendee = attendeeMapper.selectAttendeeByConfirmationCodeSimple(confirmationCode);
        if (attendee == null) {
            throw new CustomEventException(ErrorCode.ATTENDEE_NOT_FOUND, "참석자를 찾을 수 없습니다.");
        }
        // 참석 확정 여부 및 실제 참석 여부 검증
        if (!"Y".equals(attendee.getAttendedConfirmYn())) {
            throw new CustomEventException(ErrorCode.ATTENDEE_NOT_CONFIRMED);
        }
        if (!"Y".equals(attendee.getAttendedYn())) {
            throw new CustomEventException(ErrorCode.ATTENDANCE_NOT_PERMITTED);
        }

        Optional<AttendeeBenefitRedemption> existing = redemptionMapper.selectByAttendeeAndBenefit(attendee.getAttendeeId(), benefitId);
        if (existing.isEmpty()) {
            throw new CustomEventException(ErrorCode.REDEMPTION_NOT_FOUND, "혜택 사용 기록을 찾을 수 없습니다.");
        }

        int deleted = redemptionMapper.deleteRedemption(attendee.getAttendeeId(), benefitId);
        log.info("Benefit redemption cancellation result - deleted rows: {}", deleted);
        
        if (deleted == 0) {
            throw new CustomEventException(ErrorCode.INTERNAL_SERVER_ERROR, "혜택 사용 취소에 실패했습니다.");
        }
    }
}
