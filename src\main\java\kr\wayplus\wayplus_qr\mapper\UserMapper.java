package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.dto.response.AvailableProjectAdminDto;
import kr.wayplus.wayplus_qr.dto.response.UserListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.UserResponseDto;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.entity.WebServiceLog;
import kr.wayplus.wayplus_qr.dto.response.DailyRoleCountDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Mapper
public interface UserMapper {
    /**
     * 사용자 이메일로 사용자 정보를 조회합니다.
     *
     * @param userEmail 사용자 이메일
     * @return Optional<User> 사용자 정보
     */
    Optional<User> selectUserByEmail(String userEmail);

    /**
     * 사용자 이메일로 사용자 역할을 조회합니다.
     *
     * @param userEmail 사용자 이메일
     * @return Optional<String> 사용자 역할
     */
    Optional<String> selectUserRoleByEmail(String userEmail);

    /**
     * 새로운 사용자 정보를 데이터베이스에 삽입합니다.
     *
     * @param user 삽입할 사용자 정보 엔티티
     * @return 삽입된 행 수 (성공 시 1)
     */
    int insertUser(User user);

    /**
     * 사용자 정보를 데이터베이스에 업데이트 합니다.
     *
     * @param user 업데이트할 사용자 정보 엔티티
     * @return 업데이트된 행 수 (성공 시 1)
     */
    int updateUser(User user);

    /**
     * 삭제되지 않은 모든 사용자 목록을 조회합니다.
     *
     * @return 사용자 목록 (UserListResponseDto)
     */
    List<UserListResponseDto> selectUserList();

    /**
     * 사용자를 논리적으로 삭제합니다. (delete_yn = 'Y', use_yn = 'N' 업데이트)
     *
     * @param userEmail 삭제할 사용자의 이메일
     * @param deleterEmail 삭제를 수행하는 관리자의 이메일
     * @return 업데이트된 행 수 (성공 시 1)
     */
    int deleteUserLogically(@Param("userEmail") String userEmail, @Param("deleterEmail") String deleterEmail);

    /**
     * 주어진 이메일 주소가 데이터베이스에 존재하는지 확인합니다. (삭제된 사용자 포함)
     *
     * @param userEmail 확인할 이메일 주소
     * @return 존재하면 true, 그렇지 않으면 false
     */
    boolean existsByUserEmail(@Param("userEmail") String userEmail);

    // 사용자 이메일로 사용자 정보 조회 (업데이트용, 비관적 락 사용 가능성 고려)
    Optional<User> selectUserByEmailForUpdate(@Param("userEmail") String userEmail);

    // 사용자 이메일로 속한 프로젝트 ID 목록 조회
    List<Long> selectProjectIdsByUserEmail(String userEmail);

    // 사용자 비밀번호 업데이트
    int updateUserPassword(@Param("userEmail") String userEmail, @Param("encodedPassword") String encodedPassword);

    // 프로젝트 관리자 본인 및 관리 프로젝트 소속 사용자 목록 조회
    List<UserListResponseDto> selectUsersByProjectIdsAndSelf(@Param("adminEmail") String adminEmail, @Param("projectIds") List<Long> projectIds);

    // 할당 가능한 프로젝트 관리자 목록 조회
    List<UserResponseDto> selectAvailableProjectAdmins();

    // 특정 프로젝트 수정 시 할당 가능한 프로젝트 관리자 목록 조회
    List<AvailableProjectAdminDto> selectAvailableProjectAdminsForProject(@Param("projectId") Long projectId);

    // 사용자-프로젝트 매핑 정보 삽입 (UserService에서 사용)
    int insertUserProjectMapping(Map<String, Object> params);

    // 사용자-프로젝트 매핑 논리적 삭제 (Update)
    int deleteUserProjectMappingLogically(Map<String, Object> params);

    // 특정 사용자가 특정 프로젝트의 멤버인지 확인 (레코드 수 반환)
    int countUserProjectMembership(@Param("userEmail") String userEmail, @Param("projectId") Long projectId);

    /**
     * 특정 사용자가 특정 프로젝트에서 지정된 역할 중 하나를 가지고 있는지 확인합니다. (레코드 수 반환)
     *
     * @param userEmail 사용자 이메일
     * @param projectId 프로젝트 ID
     * @param roleNames 확인할 역할 이름 목록 (UserRole.name())
     * @return 조건에 맞는 멤버십 레코드 수 (0 또는 1 이상)
     */
    int countUserProjectMembershipWithRoles(@Param("userEmail") String userEmail, @Param("projectId") Long projectId, @Param("roleNames") List<String> roleNames);

    // 특정 프로젝트 ID에 속한 사용자 목록 조회
    List<UserListResponseDto> selectUsersByProjectId(@Param("projectId") Long projectId);

    // 프로젝트 사용자 목록 페이징 조회
    List<UserListResponseDto> selectProjectUserListWithPaging(@Param("projectId") Long projectId,  @Param("searchColumn") String searchColumn,
                                                                @Param("searchKeyword") String searchKeyword,
                                                                @Param("pageable") Pageable pageable);

    // 프로젝트 총 사용자 수 조회
    long countProjectUsers(@Param("projectId") Long projectId,  @Param("searchColumn") String searchColumn, @Param("searchKeyword") String searchKeyword);

    // 전체 사용자 목록 페이징 조회
    List<UserListResponseDto> selectAllUsersWithPaging(@Param("searchColumn") String searchColumn, @Param("searchKeyword") String searchKeyword, @Param("pageable") Pageable pageable);

    // 전체 총 사용자 수 조회
    long countAllUsers(@Param("searchColumn") String searchColumn, @Param("searchKeyword") String searchKeyword);

    // 사용자가 특정 프로젝트에 할당되었는지 확인
    Integer checkUserProjectAssignment(@Param("userEmail") String userEmail, @Param("projectId") Long projectId);
    
    /**
     * 특정 역할을 가진 사용자 목록 조회
     * 
     * @param role 사용자 역할명
     * @return 해당 역할을 가진 사용자 목록
     */
    List<User> selectUserListByRole(String role);

    /**
     * 활성 상태이고 삭제되지 않았으며, SUPER_ADMIN이 아닌 관리자들의 역할별 인원수를 조회합니다.
     * 결과는 역할 이름(role)과 해당 역할의 사용자 수(count)를 포함하는 Map의 List 형태로 반환됩니다.
     * 예: [{role=ADMIN, count=5}, {role=PROJECT_MANAGER, count=10}]
     *
     * @param projectId 프로젝트 ID (선택적)
     * @return 역할별 관리자 수 목록 (List<Map<String, Object>>)
     */
    List<Map<String, Object>> selectAdminRoleCounts(@Param("projectId") Long projectId);



    // 일별 관리자 생성 수를 조회합니다.
    List<DailyRoleCountDto> selectDailyAdminCreationCounts(@Param("projectId") Long projectId, @Param("startDate") String startDate, @Param("endDate") String endDate);

    // 관리자별 가장 많이 접속한 URI 조회
    List<Map<String, Object>> selectAdminMostVisitedUri(@Param("projectId") Long projectId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 사용자 기본 통계 조회
     * @param projectId 프로젝트 ID (선택적)
     * @param startDate 시작일
     * @param endDate 종료일
     * @return 사용자 기본 통계를 담은 Map
     */
    Map<String, Object> selectUserBasicStats(@Param("projectId") Long projectId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 역할별 사용자 수 조회
     * @param projectId 프로젝트 ID (선택적)
     * @param startDate 시작일
     * @param endDate 종료일
     * @return 역할별 사용자 수를 담은 Map 리스트
     */
    List<Map<String, Object>> selectUserCountByRole(@Param("projectId") Long projectId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 상위 QR 코드 생성자 통계 조회
     * @param projectId 프로젝트 ID (선택적)
     * @param limit 조회할 상위 사용자 수
     * @return 상위 QR 코드 생성자 통계를 담은 Map 리스트
     */
    List<Map<String, Object>> selectTopQrCreators(
            @Param("projectId") Long projectId,
            @Param("limit") int limit);

    /**
     * 사용자별 QR 코드 타입 분포 조회
     * @param projectId 프로젝트 ID (선택적)
     * @param userEmail 사용자 이메일 (선택적)
     * @return 사용자별 QR 코드 타입 분포를 담은 Map 리스트
     */
    List<Map<String, Object>> selectUserQrTypeDistribution(
            @Param("projectId") Long projectId,
            @Param("userEmail") String userEmail);

    /**
     * 사용자당 평균 QR 코드 수 조회
     * @param projectId 프로젝트 ID (선택적)
     * @return 사용자당 평균 QR 코드 수
     */
    double selectAvgQrCodesPerUser(@Param("projectId") Long projectId);

    /**
     * 상위 이벤트 생성자 통계 조회
     * @param projectId 프로젝트 ID (선택적)
     * @param limit 조회할 상위 사용자 수
     * @return 상위 이벤트 생성자 통계를 담은 Map 리스트
     */
    List<Map<String, Object>> selectTopEventCreators(
            @Param("projectId") Long projectId,
            @Param("limit") int limit);

    /**
     * 사용자당 평균 이벤트 수 조회
     * @param projectId 프로젝트 ID (선택적)
     * @return 사용자당 평균 이벤트 수
     */
    double selectAvgEventsPerUser(@Param("projectId") Long projectId);

    /**
     * 상위 랜딩 페이지 생성자 통계 조회
     * @param projectId 프로젝트 ID (선택적)
     * @param limit 조회할 상위 사용자 수
     * @return 상위 랜딩 페이지 생성자 통계를 담은 Map 리스트
     */
    List<Map<String, Object>> selectTopLandingPageCreators(
            @Param("projectId") Long projectId,
            @Param("limit") int limit);

    /**
     * 사용자당 평균 랜딩 페이지 수 조회
     * @param projectId 프로젝트 ID (선택적)
     * @return 사용자당 평균 랜딩 페이지 수
     */
    double selectAvgLandingPagesPerUser(@Param("projectId") Long projectId);

    /**
     * 상위 교환권 승인자 통계 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @param limit 조회할 상위 승인자 수
     * @return 상위 교환권 승인자 통계를 담은 Map 리스트
     */
    List<Map<String, Object>> selectTopExchangeApprovers(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId,
            @Param("limit") int limit);

    /**
     * 총 교환권 승인 횟수 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return 총 교환권 승인 횟수
     */
    long selectTotalExchangeApprovals(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);

    /**
     * 승인자당 평균 승인 횟수 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return 승인자당 평균 승인 횟수
     */
    double selectAvgApprovalsPerApprover(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);

    /**
     * 일별 사용자 활동 통계 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return 일별 사용자 활동 통계를 담은 Map 리스트
     */
    List<Map<String, Object>> selectDailyUserActivities(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);

    /**
     * 시간대별 사용자 활동 통계 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return 시간대별 사용자 활동 통계를 담은 Map 리스트
     */
    List<Map<String, Object>> selectHourlyUserActivities(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);

    /**
     * 요일별 사용자 활동 통계 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return 요일별 사용자 활동 통계를 담은 Map 리스트
     */
    List<Map<String, Object>> selectWeekdayUserActivities(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);


    void updateUserNewTokenId(User user);

    void insertUserWebLog(WebServiceLog webLog);
}
