package kr.wayplus.wayplus_qr.controller;

import java.time.LocalDate;
import java.util.Map;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.Pattern;
import kr.wayplus.wayplus_qr.dto.request.UsageStatisticsRequestDto;
import kr.wayplus.wayplus_qr.dto.response.AdminChartDataDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.AttendeeChartDataDto;
import kr.wayplus.wayplus_qr.dto.response.ComprehensiveQrStatsDto;
import kr.wayplus.wayplus_qr.dto.response.EventChartDataDto;
import kr.wayplus.wayplus_qr.dto.response.QrChartDataDto;
import kr.wayplus.wayplus_qr.dto.response.TotalQrStatusDto;
import kr.wayplus.wayplus_qr.dto.response.UsageStatisticsResponseDto;
import kr.wayplus.wayplus_qr.dto.response.UserActivityStatsDto;
import kr.wayplus.wayplus_qr.service.StatisticsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/way/statistics")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Statistics Controller", description = "통계 관련 API")
public class StatisticsController {

    private final StatisticsService statisticsService;

    /**
     * 전체 QR 코드 현황 (총 생성 수, 총 스캔 수) 조회 - Super Admin 전용
     * @return ResponseEntity<ApiResponseDto<TotalQrStatusDto>>
     */
    @Operation(summary = "전체 QR 코드 현황 조회", description = "총 생성 수와 총 스캔 수를 반환합니다.")
    @GetMapping("/qr/total")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<TotalQrStatusDto>> getTotalQrStatus() {
        log.info("Received request for total QR code statistics.");
        TotalQrStatusDto totalQrStatus = statisticsService.getTotalQrStatus(null, null);
        log.info("Successfully retrieved total QR code statistics.");
        return ResponseEntity.ok(ApiResponseDto.success(totalQrStatus));
    }

    /**
     * 기간별 QR 코드 현황 (총 생성 수, 총 스캔 수) 조회 - 프론트엔드 차트용, Super Admin 전용
     * @param startDate 조회 시작일 (선택, yyyy-MM-dd)
     * @param endDate 조회 종료일 (선택, yyyy-MM-dd)
     * @return ResponseEntity<ApiResponseDto<QrChartDataDto>>
     */
    @Operation(summary = "기간별 QR 코드 현황 조회", description = "총 생성 수와 총 스캔 수를 반환합니다.")
    @GetMapping("/qr/chart")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<QrChartDataDto>> getQrCodeStatusForChart(
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        if (startDate == null) {
            startDate = LocalDate.now();
        }
        if (endDate == null) {
            endDate = LocalDate.now().plusMonths(1);
        }
        log.info("Received request for QR code chart statistics. StartDate: {}, EndDate: {}", startDate, endDate);
        QrChartDataDto qrChartData = statisticsService.getQrCodeStatusForChart(startDate, endDate);
        log.info("Successfully retrieved QR code chart statistics.");
        return ResponseEntity.ok(ApiResponseDto.success(qrChartData));
    }

    /**
     * 기간별 관리자 생성 현황 조회 - 프론트엔드 차트용
     * @param startDate 조회 시작일 (yyyy-MM-dd)
     * @param endDate 조회 종료일 (yyyy-MM-dd)
     * @param projectId 프로젝트 ID (선택적, null인 경우 전체 관리자 통계 조회)
     * @return ResponseEntity<ApiResponseDto<AdminChartDataDto>>
     */
    @Operation(summary = "기간별 관리자 생성 현황 조회", description = "총 생성 수와 총 스캔 수를 반환합니다. projectId가 제공되면 해당 프로젝트의 관리자만 조회합니다.")
    @GetMapping("/admin/chart")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN', 'PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<AdminChartDataDto>> getAdminCreationStatsForChart(
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "projectId", required = false) Long projectId) {
        if (startDate == null) {
            startDate = LocalDate.now();
        }
        if (endDate == null) {
            endDate = LocalDate.now().plusMonths(1);
        }
        log.info("Received request for admin creation chart statistics. StartDate: {}, EndDate: {}, ProjectId: {}", startDate, endDate, projectId);
        AdminChartDataDto adminChartData = statisticsService.getAdminCreationStatsForChart(startDate, endDate, projectId);
        log.info("Successfully retrieved admin creation chart statistics.");
        return ResponseEntity.ok(ApiResponseDto.success(adminChartData));
    }

    /**
     * 기간별 일일 이벤트 생성 통계 조회 (차트용)
     */
    @Operation(summary = "기간별 이벤트 생성 통계 조회", description = "지정된 기간 동안의 일일 이벤트 생성 수를 반환합니다.")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    @GetMapping("/event/chart")
    public ResponseEntity<ApiResponseDto<EventChartDataDto>> getEventCreationStatsForChart(
            @Parameter(description = "조회 시작 날짜 (YYYY-MM-DD)", example = "2024-01-01")
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "조회 종료 날짜 (YYYY-MM-DD)", example = "2024-01-31")
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        if (startDate == null) {
            startDate = LocalDate.now();
        }
        if (endDate == null) {
            endDate = LocalDate.now().plusMonths(1);
        }
        EventChartDataDto data = statisticsService.getEventCreationStatsForChart(startDate, endDate);
        return ResponseEntity.ok(ApiResponseDto.success(data));
    }

    /**
     * 기간별 일일 참가자 등록 통계 조회 (차트용)
     */
    @Operation(summary = "기간별 참가자 등록 통계 조회", description = "지정된 기간 동안의 일일 참가자 등록 수를 반환합니다.")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    @GetMapping("/attendee/chart")
    public ResponseEntity<ApiResponseDto<AttendeeChartDataDto>> getAttendeeRegistrationStatsForChart(
            @Parameter(description = "조회 시작 날짜 (YYYY-MM-DD)", example = "2024-01-01")
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @Parameter(description = "조회 종료 날짜 (YYYY-MM-DD)", example = "2024-01-31")
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        if (startDate == null) {
            startDate = LocalDate.now();
        }
        if (endDate == null) {
            endDate = LocalDate.now().plusMonths(1);
        }
        AttendeeChartDataDto data = statisticsService.getAttendeeRegistrationStatsForChart(startDate, endDate);
        return ResponseEntity.ok(ApiResponseDto.success(data));
    }

    /**
     * 관리자 현황 (역할별 인원 수, SUPER_ADMIN 제외) 조회
     * @param projectId 프로젝트 ID (선택적, null인 경우 전체 관리자 통계 조회)
     * @return ResponseEntity<ApiResponseDto<Map<String, Long>>>
     */
    @Operation(summary = "관리자 현황 조회", description = "역할별 인원 수를 반환합니다. projectId가 제공되면 해당 프로젝트의 관리자만 조회합니다.")
    @GetMapping("/admins/total")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN', 'PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<Map<String, Long>>> getAdminStatistics(
            @RequestParam(value = "projectId", required = false) Long projectId) {
        log.info("Received request for admin statistics. ProjectId: {}", projectId);
        Map<String, Long> adminStats = statisticsService.getAdminStatistics(projectId);
        log.info("Successfully retrieved admin statistics.");
        return ResponseEntity.ok(ApiResponseDto.success(adminStats));
    }

    /**
     * 전체 이벤트 수 조회
     * @return ResponseEntity<ApiResponseDto<Map<String, Long>>> "totalEventCount" 키와 전체 이벤트 수를 값으로 가지는 Map
     */
    @Operation(summary = "전체 이벤트 수 조회", description = "총 이벤트 수를 반환합니다.")
    @GetMapping("/events/total")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Map<String, Long>>> getTotalEventCount() {
        Map<String, Long> result = statisticsService.getTotalEventCount();
        return ResponseEntity.ok(ApiResponseDto.success(result));
    }

    /**
     * 전체 참가자 수 조회
     * @return ResponseEntity<ApiResponseDto<Map<String, Long>>> "totalAttendeeCount" 키와 전체 참가자 수를 값으로 가지는 Map
     */
    @Operation(summary = "전체 참가자 수 조회", description = "총 참가자 수를 반환합니다.")
    @GetMapping("/attendees/total")
    @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    public ResponseEntity<ApiResponseDto<Map<String, Long>>> getTotalAttendeeCount() {
        Map<String, Long> result = statisticsService.getTotalAttendeeCount();
        return ResponseEntity.ok(ApiResponseDto.success(result));
    }

    // ================= 프로젝트별 통계 (PROJECT_ADMIN 권한) =================

    @Operation(summary = "프로젝트별 QR 코드 현황 조회", description = "프로젝트별 QR 코드 생성 수와 스캔 수를 반환합니다.")
    @GetMapping("/qr/project/chart")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<QrChartDataDto>> getProjectQrChart(
            @RequestParam("projectId") Long projectId,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        if (startDate == null) {
            startDate = LocalDate.now();
        }
        if (endDate == null) {
            endDate = LocalDate.now().plusMonths(1);
        }
        QrChartDataDto dto = statisticsService.getProjectQrCodeStatusForChart(projectId, startDate, endDate);
        return ResponseEntity.ok(ApiResponseDto.success(dto));
    }

    @Operation(summary = "프로젝트별 이벤트 생성 현황 조회", description = "프로젝트별 이벤트 생성 수를 반환합니다.")
    @GetMapping("/event/project/chart")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<EventChartDataDto>> getProjectEventChart(
            @RequestParam("projectId") Long projectId,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        if (startDate == null) {
            startDate = LocalDate.now();
        }
        if (endDate == null) {
            endDate = LocalDate.now().plusMonths(1);
        }
        EventChartDataDto dto = statisticsService.getProjectEventCreationStatsForChart(projectId, startDate, endDate);
        return ResponseEntity.ok(ApiResponseDto.success(dto));
    }

    @Operation(summary = "프로젝트별 참석자 현황 조회", description = "프로젝트별 참석자 수를 반환합니다.")
    @GetMapping("/attendee/project/chart")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<AttendeeChartDataDto>> getProjectAttendeeChart(
            @RequestParam("projectId") Long projectId,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        if (startDate == null) {
            startDate = LocalDate.now();
        }
        if (endDate == null) {
            endDate = LocalDate.now().plusMonths(1);
        }
        AttendeeChartDataDto dto = statisticsService.getProjectAttendeeRegistrationStatsForChart(projectId, startDate, endDate);
        return ResponseEntity.ok(ApiResponseDto.success(dto));
    }

    @Operation(summary = "프로젝트별 관리자 현황 조회", description = "프로젝트별 관리자자 수를 반환합니다.")
    @GetMapping("/admin/project/chart")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<AdminChartDataDto>> getProjectAdminChart(
            @RequestParam("projectId") Long projectId,
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        if (startDate == null) {
            startDate = LocalDate.now();
        }
        if (endDate == null) {
            endDate = LocalDate.now().plusMonths(1);
        }
        AdminChartDataDto dto = statisticsService.getProjectAdminCreationStatsForChart(projectId, startDate, endDate);
        return ResponseEntity.ok(ApiResponseDto.success(dto));
    }

    // ---------- 프로젝트별 총계 ----------
    @Operation(summary = "프로젝트별 QR 코드 현황 조회", description = "프로젝트별 QR 코드 생성 수와 스캔 수를 반환합니다.")
    @GetMapping("/qr/project/total")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<TotalQrStatusDto>> getProjectTotalQr(
            @RequestParam("projectId") Long projectId) {
        TotalQrStatusDto dto = statisticsService.getProjectTotalQrStatus(projectId, null, null);
        return ResponseEntity.ok(ApiResponseDto.success(dto));
    }

    @Operation(summary = "프로젝트별 이벤트 수 조회", description = "프로젝트별 이벤트 수를 반환합니다.")
    @GetMapping("/events/project/total")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<Map<String, Long>>> getProjectTotalEvents(
            @RequestParam("projectId") Long projectId) {
        Map<String, Long> dto = statisticsService.getProjectTotalEventCount(projectId);
        return ResponseEntity.ok(ApiResponseDto.success(dto));
    }

    @Operation(summary = "프로젝트별 참석자 수 조회", description = "프로젝트별 참석자 수를 반환합니다.")
    @GetMapping("/attendees/project/total")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<Map<String, Long>>> getProjectTotalAttendees(
            @RequestParam("projectId") Long projectId) {
        Map<String, Long> dto = statisticsService.getProjectTotalAttendeeCount(projectId);
        return ResponseEntity.ok(ApiResponseDto.success(dto));
    }

    // ================= QR 코드 통계 API (통합) =================

    /**
     * 통합 QR 코드 통계 조회
     * @param startDate 시작일 (선택, yyyy-MM-dd)
     * @param endDate 종료일 (선택, yyyy-MM-dd)
     * @param projectId 프로젝트 ID (선택적)
     * @param includeTypes 포함할 통계 유형 (쉼표로 구분된 문자열, 기본값: 모든 유형)
     * @return ResponseEntity<ApiResponseDto<ComprehensiveQrStatsDto>>
     */
    @Operation(summary = "통합 QR 코드 통계 조회",
            description = "QR 코드 관련 모든 통계 정보를 한 번에 반환합니다. includeTypes 파라미터로 필요한 통계 유형만 선택할 수 있습니다.")
    @GetMapping("/qr/stats")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN', 'PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<ComprehensiveQrStatsDto>> getComprehensiveQrStats(
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "includeTypes", required = false) String includeTypes) {

        if (startDate == null) {
            startDate = LocalDate.now().minusMonths(1);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }
        
        // scannedQrCodes 포함 처리
        if (includeTypes == null || includeTypes.trim().isEmpty()) {
            includeTypes = "basic,type,status,device,time,exchange,scannedQrCodes";
        } else if (!includeTypes.contains("scannedQrCodes")) {
            includeTypes = includeTypes + ",scannedQrCodes";
        }

        log.info("Received request for comprehensive QR statistics. StartDate: {}, EndDate: {}, ProjectId: {}, IncludeTypes: {}",
                startDate, endDate, projectId, includeTypes);

        ComprehensiveQrStatsDto result = statisticsService.getComprehensiveQrStats(startDate, endDate, projectId, includeTypes);

        log.info("Successfully retrieved comprehensive QR statistics.");
        return ResponseEntity.ok(ApiResponseDto.success(result));
    }

    /**
     * 사용자 활동 통계 조회
     * @param startDate 시작일 (선택, yyyy-MM-dd)
     * @param endDate 종료일 (선택, yyyy-MM-dd)
     * @param projectId 프로젝트 ID (선택적)
     * @param includeTypes 포함할 통계 유형 (쉼표로 구분된 문자열, 기본값: 모든 유형)
     * @param topN 상위 몇 명의 사용자 통계를 조회할지 (기본값: 5)
     * @return ResponseEntity<ApiResponseDto<UserActivityStatsDto>>
     */
    @Operation(summary = "사용자 통계 조회",
            description = "사용자 기본 통계 정보를 반환합니다. includeTypes 파라미터는 'basic'만 지원합니다.")
    @GetMapping("/user/stats")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN', 'PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<UserActivityStatsDto>> getUserActivityStats(
            @RequestParam(value = "startDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam(value = "endDate", required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "includeTypes", required = false) String includeTypes,
            @RequestParam(value = "topN", required = false, defaultValue = "5") int topN) {

        if (startDate == null) {
            startDate = LocalDate.now().minusMonths(1);
        }
        if (endDate == null) {
            endDate = LocalDate.now();
        }

        log.info("Received request for user activity statistics. StartDate: {}, EndDate: {}, ProjectId: {}, IncludeTypes: {}, TopN: {}",
                startDate, endDate, projectId, includeTypes, topN);

        UserActivityStatsDto result = statisticsService.getUserActivityStats(startDate, endDate, projectId, includeTypes, topN);

        log.info("Successfully retrieved user activity statistics.");
        return ResponseEntity.ok(ApiResponseDto.success(result));
    }

    @Operation(summary = "서비스 사용 통계 조회",
            description = "웹서비스 로그를 기반으로 서비스 전체 사용 통계를 제공합니다. 기간(startDate, endDate)은 YYYY-MM-DD 형식으로 필수입니다.")
    @GetMapping("/usage")
    @PreAuthorize("hasAnyAuthority('SUPER_ADMIN', 'PROJECT_ADMIN', 'SUB_ADMIN')")
    public ResponseEntity<ApiResponseDto<UsageStatisticsResponseDto>> getUsageStatistics(
            @Parameter(description = "조회 시작일 (YYYY-MM-DD)", required = true, example = "2023-01-01")
            @RequestParam("startDate") 
            @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "시작일은 YYYY-MM-DD 형식이어야 합니다.") 
            String startDate,
            @Parameter(description = "조회 종료일 (YYYY-MM-DD)", required = true, example = "2023-01-31")
            @RequestParam("endDate") 
            @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "종료일은 YYYY-MM-DD 형식이어야 합니다.") 
            String endDate,
            @RequestParam(value = "projectId", required = false) Long projectId) {
        
        UsageStatisticsRequestDto requestDto = new UsageStatisticsRequestDto();
        requestDto.setStartDate(startDate);
        requestDto.setEndDate(endDate);
        requestDto.setProjectId(projectId);

        log.info("Received GET request for usage statistics: startDate={}, endDate={}, projectId={}", startDate, endDate, projectId);
        UsageStatisticsResponseDto usageStatistics = statisticsService.getUsageStatistics(requestDto);
        return ResponseEntity.ok(ApiResponseDto.success(usageStatistics));
    }
}
