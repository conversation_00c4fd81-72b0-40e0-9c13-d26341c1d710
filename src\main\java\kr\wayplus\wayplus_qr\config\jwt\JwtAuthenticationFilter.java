package kr.wayplus.wayplus_qr.config.jwt;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import kr.wayplus.wayplus_qr.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * JWT 토큰을 사용하여 사용자를 인증하는 필터입니다.
 * 각 요청마다 실행되어 헤더에서 JWT를 추출하고 유효성을 검사합니다.
 */
@Slf4j
@Component // Spring Bean으로 등록
@RequiredArgsConstructor // final 필드에 대한 생성자 자동 생성
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    private final JwtUtil jwtUtil;
    private final UserDetailsService userDetailsService; // 실제 구현체(예: CustomUserDetailsService)가 주입됩니다.

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String BEARER_PREFIX = "Bearer ";

    @Override
    protected void doFilterInternal(@NonNull HttpServletRequest request,
                                    @NonNull HttpServletResponse response,
                                    @NonNull FilterChain filterChain)
            throws ServletException, IOException {

        try {
            // 1. 요청 헤더에서 JWT 토큰 추출
            String jwt = resolveToken(request);

            // 2. 토큰 유효성 검사
            if (StringUtils.hasText(jwt) && jwtUtil.validateToken(jwt)) {
                // 3. 토큰에서 사용자 이메일(또는 식별자) 추출
                String userEmail = jwtUtil.getUserEmailFromToken(jwt); // JwtUtil에 해당 메서드가 구현되어 있어야 함

                // 4. UserDetailsService를 통해 UserDetails 조회
                UserDetails userDetails = userDetailsService.loadUserByUsername(userEmail);

                // 5. SecurityContext에 인증 정보 설정
                if (userDetails != null) {
                    UsernamePasswordAuthenticationToken authentication =
                            new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                    SecurityContextHolder.getContext().setAuthentication(authentication);
                    log.debug("Security Context에 '{}' 인증 정보를 저장했습니다, uri: {}", authentication.getName(), request.getRequestURI());
                }
            } else {
                if (StringUtils.hasText(jwt)) {
                    log.debug("유효한 JWT 토큰이 없습니다, uri: {}", request.getRequestURI());
                }
                // 토큰이 없거나 유효하지 않으면 다음 필터로 넘어감 (인증되지 않은 상태)
            }
        } catch (Exception e) {
            // JwtUtil.validateToken 등에서 예외 발생 시 처리
            log.error("JWT 인증 필터 처리 중 예외 발생: {}", e.getMessage());
            SecurityContextHolder.clearContext(); // 혹시 모를 컨텍스트 정리
            // 필요에 따라 여기서 특정 응답을 보낼 수도 있지만, 보통은 다음 필터나 ExceptionTranslationFilter에서 처리
        }

        filterChain.doFilter(request, response); // 다음 필터 실행
    }

    /**
     * Request Header에서 토큰 정보를 추출합니다.
     * @param request HttpServletRequest
     * @return 토큰 문자열 (Bearer 접두사 제외) 또는 null
     */
    private String resolveToken(HttpServletRequest request) {
        String bearerToken = request.getHeader(AUTHORIZATION_HEADER);
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith(BEARER_PREFIX)) {
            return bearerToken.substring(BEARER_PREFIX.length());
        }
        return null;
    }
}
