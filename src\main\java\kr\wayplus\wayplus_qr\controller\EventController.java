package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.config.SearchTypeRegistry;
import kr.wayplus.wayplus_qr.dto.request.EventCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.EventUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.AttendeeSummaryDto;
import kr.wayplus.wayplus_qr.dto.response.EventResponseDto;
import kr.wayplus.wayplus_qr.dto.response.EventWithAttendeesResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.SearchTypeInfo;
import kr.wayplus.wayplus_qr.dto.request.BulkAttendeeExcelRequestDto;
import kr.wayplus.wayplus_qr.dto.response.BulkRegistrationResultDto;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.exception.CustomEventException;
import kr.wayplus.wayplus_qr.exception.CustomAttendeeException;
import kr.wayplus.wayplus_qr.service.AttendeeService;
import kr.wayplus.wayplus_qr.service.EventService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/api/way/events")
@RequiredArgsConstructor
public class EventController {
    private final EventService eventService;
    private final AttendeeService attendeeService;
    private final SearchTypeRegistry searchTypeRegistry;

    /**
     * 이벤트 목록 조회 (페이징/검색/필터/정렬)
     */
    @GetMapping
    public ResponseEntity<ApiResponseDto<ListResponseDto<EventResponseDto>>> getEvents(
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "createDate", direction = org.springframework.data.domain.Sort.Direction.DESC) Pageable pageable,
            @AuthenticationPrincipal User userDetails) {
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }

        // searchType 유효성 검사
        if (!searchTypeRegistry.isValidSearchType("event", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }

        Page<EventResponseDto> eventPage = eventService.getEvents(
                projectId, status, searchType, searchKeyword, pageable, userDetails.getUserEmail()
        );
        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("event");

        ListResponseDto<EventResponseDto> responseDto = new ListResponseDto<>(eventPage, availableSearchTypes);
        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    /**
     * 이벤트 생성
     */
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponseDto<Long>> createEvent(
            @Valid @ModelAttribute EventCreateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }
        Long eventId = eventService.createEvent(requestDto, userDetails.getUserEmail());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success(eventId));
    }

    /**
     * 이벤트 상세 조회
     */
    @GetMapping("/{eventId}")
    public ResponseEntity<ApiResponseDto<EventResponseDto>> getEvent(
            @PathVariable("eventId") Long eventId,
            @AuthenticationPrincipal User userDetails) {
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }
        EventResponseDto dto = eventService.getEventById(eventId, userDetails.getUserEmail());
        return ResponseEntity.ok(ApiResponseDto.success(dto));
    }

    /**
     * 이벤트 수정
     */
    @PutMapping(value = "/{eventId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponseDto<EventResponseDto>> updateEvent(
            @PathVariable("eventId") Long eventId,
            @Valid @ModelAttribute EventUpdateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }
        EventResponseDto dto = eventService.updateEvent(eventId, requestDto, userDetails.getUserEmail());
        return ResponseEntity.ok(ApiResponseDto.success(dto));
    }

    /**
     * 이벤트 삭제
     */
    @DeleteMapping("/{eventId}")
    public ResponseEntity<ApiResponseDto<Void>> deleteEvent(
            @PathVariable("eventId") Long eventId,
            @AuthenticationPrincipal User userDetails) {
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }
        eventService.deleteEvent(eventId, userDetails.getUserEmail());
        return ResponseEntity.ok(ApiResponseDto.success(null));
    }

    /**
     * 특정 이벤트 상세 정보와 참석자 목록 조회 (페이지네이션)
     * @param eventId 이벤트 ID
     * @param pageable 페이지 정보 (page, size, sort)
     * @return 이벤트 정보 및 참석자 목록 페이지
     */
    @GetMapping("/{eventId}/attendees")
    public ResponseEntity<EventWithAttendeesResponseDto> getEventWithAttendees(
            @PathVariable("eventId") Long eventId,
            @PageableDefault(size = 10) Pageable pageable) {

        EventWithAttendeesResponseDto responseDto = attendeeService.getEventWithAttendees(eventId, pageable);
        return ResponseEntity.ok(responseDto);
    }

    /**
     * 관리자가 선택한 특정 프로젝트의 모든 이벤트 참석자 목록 조회 (페이지네이션)
     * @param projectId   프로젝트 ID
     * @param userDetails 현재 로그인한 관리자 정보
     * @param pageable    페이지 정보
     * @return 특정 프로젝트의 모든 참석자 목록 페이지
     */
    @GetMapping("/{projectId}/all-attendees")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponseDto<ListResponseDto<AttendeeSummaryDto>>> getAttendeesForAdminProject(
            @PathVariable("projectId") Long projectId,
            @AuthenticationPrincipal User userDetails,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "createDate,desc") Pageable pageable) {
                

        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }
        if (searchType != null && !searchTypeRegistry.isValidSearchType("attendee", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }

        Page<AttendeeSummaryDto> attendeePage = attendeeService.getAttendeesForAdminProject(userDetails.getUserEmail(), projectId, searchType, searchKeyword, pageable);
        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("attendee");

        ListResponseDto<AttendeeSummaryDto> responseDto = new ListResponseDto<>(attendeePage, availableSearchTypes);

        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    /**
     * 관리자가 특정 이벤트의 참석자를 삭제합니다.
     *
     * @param eventId     이벤트 ID
     * @param attendeeId  삭제할 참석자 ID
     * @param userDetails 현재 로그인한 관리자 정보
     * @return 성공 시 204 No Content
     */
    @DeleteMapping("/{eventId}/attendees/{attendeeId}")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponseDto<Void>> deleteAttendee(
            @PathVariable("eventId") Long eventId,
            @PathVariable("attendeeId") Long attendeeId,
            @AuthenticationPrincipal User userDetails) {

        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }

        attendeeService.deleteAttendee(userDetails.getUserEmail(), eventId, attendeeId);
        return ResponseEntity.ok(ApiResponseDto.success(null));
    }

    /**
     * 이벤트 혜택 삭제
     */
    @DeleteMapping("/{eventId}/benefits/{benefitId}")
    public ResponseEntity<ApiResponseDto<Void>> deleteEventBenefit(
            @PathVariable("eventId") Long eventId,
            @PathVariable("benefitId") Long benefitId,
            @AuthenticationPrincipal User userDetails) {
        log.info("Received request to delete benefit {} for event ID: {}", benefitId, eventId);
        eventService.deleteEventBenefit(eventId, benefitId, userDetails.getUserEmail());
        return ResponseEntity.ok(ApiResponseDto.success(null));
    }

    /**
     * 이벤트 복사
     * @param eventId 복사할 이벤트 ID
     * @param userDetails 현재 로그인한 사용자 정보
     * @return 복사된 이벤트 정보
     */
    @PostMapping("/{eventId}/copy")
    public ResponseEntity<ApiResponseDto<EventResponseDto>> copyEvent(
            @PathVariable("eventId") Long eventId,
            @AuthenticationPrincipal User userDetails) {
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }
        EventResponseDto dto = eventService.copyEvent(eventId, userDetails.getUserEmail());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success(dto));
    }

    /**
     * 엑셀을 이용한 대량 사전 신청 등록
     * @param eventId 이벤트 ID
     * @param teamId 팀 ID (선택사항)
     * @param attendedYn 참석 여부 (선택사항)
     * @param attendedConfirmYn 참석 확정 여부 (선택사항)
     * @param excelFile 엑셀 파일
     * @param authentication 인증 정보
     * @return 대량 등록 결과
     */
    @PostMapping("/{eventId}/attendees/bulk")
    public ResponseEntity<ApiResponseDto<BulkRegistrationResultDto>> bulkRegisterAttendees(
            @PathVariable Long eventId,
            @RequestParam(value = "teamId", required = false) Long teamId,
            @RequestParam(value = "attendedYn", required = false) String attendedYn,
            @RequestParam(value = "attendedConfirmYn", required = false) String attendedConfirmYn,
            @RequestParam("file") MultipartFile excelFile,
            Authentication authentication) {
        
        try {
            // 인증 확인
            if (authentication == null || !authentication.isAuthenticated()) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                        .body(ApiResponseDto.error("UNAUTHORIZED", "인증이 필요합니다."));
            }
            
            // 파일 검증
            if (excelFile.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(ApiResponseDto.error("INVALID_FILE", "엑셀 파일이 비어있습니다."));
            }
            
            // 파일 형식 검증
            String fileName = excelFile.getOriginalFilename();
            if (fileName == null || (!fileName.endsWith(".xlsx") && !fileName.endsWith(".xls"))) {
                return ResponseEntity.badRequest()
                        .body(ApiResponseDto.error("INVALID_FILE", "지원하지 않는 파일 형식입니다. (.xls, .xlsx만 지원)"));
            }
            
            // 요청 DTO 생성
            BulkAttendeeExcelRequestDto requestDto = BulkAttendeeExcelRequestDto.builder()
                    .eventId(eventId)
                    .teamId(teamId)
                    .attendedYn(attendedYn)
                    .attendedConfirmYn(attendedConfirmYn)
                    .excelFile(excelFile)
                    .build();
            
            // 대량 등록 처리
            BulkRegistrationResultDto result = attendeeService.registerAttendeesFromExcel(requestDto);
            
            // 결과에 따른 응답 상태 코드 결정
            if (result.getFailureCount() == 0) {
                // 모든 등록 성공
                return ResponseEntity.ok(ApiResponseDto.success(result));
            } else if (result.getSuccessCount() > 0) {
                // 일부 성공, 일부 실패
                return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT).body(ApiResponseDto.success(result));
            } else {
                // 모든 등록 실패
                return ResponseEntity.badRequest().body(ApiResponseDto.error("INVALID_DATA", result.getFailedRegistrations()));
            }
            
        } catch (CustomEventException e) {
            log.error("Custom event error during bulk registration: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_DATA", e.getMessage()));
        } catch (CustomAttendeeException e) {
            log.error("Custom attendee error during bulk registration: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_DATA", e.getMessage()));
        } catch (Exception e) {
            log.error("Unexpected error during bulk registration: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponseDto.error("INTERNAL_SERVER_ERROR", "서버 내부 오류가 발생했습니다."));
        }
    }
}
