package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.FileUploadResponseDto;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.service.FileStorageService;
import kr.wayplus.wayplus_qr.service.InquiryService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import jakarta.servlet.http.HttpServletRequest;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

import java.util.Objects;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("/api/way/download")
@Slf4j
public class FileDownloadController {

    @Autowired
    private FileStorageService fileStorageService;
    
    @Autowired
    private InquiryService inquiryService;
    
    //파일명으로 직접 다운로드
    @GetMapping("/{filename:.+}")
    public ResponseEntity<Resource> downloadFile(@PathVariable("filename") String filename, HttpServletRequest request) {
        // Load file as Resource
        Resource resource = fileStorageService.loadFileAsResource(filename);

        // Try to determine file's content type
        String contentType = null;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            log.info("Could not determine file type.");
        }

        // Fallback to the default content type if type could not be determined
        if(contentType == null) {
            contentType = "application/octet-stream";
        }

        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }
    
    //문의사항 첨부파일 다운로드
    @GetMapping("/inquiry-attachment/{attachmentId}")
    public ResponseEntity<Resource> downloadInquiryAttachment(
            @PathVariable("attachmentId") Long attachmentId,
            @AuthenticationPrincipal User userDetails,
            HttpServletRequest request) {
        
        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        
        try {
            // InquiryService를 통해 첨부파일 리소스 로드
            Resource resource = inquiryService.downloadAttachment(attachmentId, userEmail);
            return prepareFileResponse(resource, request);
        } catch (Exception e) {
            log.error("Error downloading inquiry attachment: {}", e.getMessage(), e);
            return ResponseEntity.notFound().build();
        }
    }
    
    // 공통 응답 처리 메서드
    private ResponseEntity<Resource> prepareFileResponse(Resource resource, HttpServletRequest request) {
        String contentType = null;
        try {
            contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
        } catch (IOException ex) {
            log.info("Could not determine file type.");
        }
        
        if(contentType == null) {
            contentType = "application/octet-stream";
        }
        
        return ResponseEntity.ok()
                .contentType(MediaType.parseMediaType(contentType))
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"" + resource.getFilename() + "\"")
                .body(resource);
    }
    
    /**
     * 문의사항 첨부파일 삭제 (ID 기준)
     * 해당 문의사항에 대한 권한이 있는 사용자만 삭제 가능
     * @param attachmentId 첨부파일 ID
     * @param userDetails 인증된 사용자 정보
     * @return 삭제 결과
     */
    @DeleteMapping("/inquiry-attachment/{attachmentId}")
    public ResponseEntity<?> deleteInquiryAttachment(
            @PathVariable("attachmentId") Long attachmentId,
            @AuthenticationPrincipal User userDetails) {
        
        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        
        try {
            // InquiryService를 통해 첨부파일 삭제
            inquiryService.deleteAttachment(attachmentId, userEmail);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "첨부파일이 성공적으로 삭제되었습니다.");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error deleting inquiry attachment: {}", e.getMessage(), e);
            
            Map<String, String> response = new HashMap<>();
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
    
    /**
     * 파일 삭제 (파일명 기준)
     * 관리자 권한을 가진 사용자만 삭제 가능
     * @param filename 파일명
     * @return 삭제 결과
     */
    @DeleteMapping("/{filename:.+}")
    @PreAuthorize("hasRole('ADMIN')")
    public ResponseEntity<?> deleteFile(
            @PathVariable("filename") String filename) {
        
        try {
            fileStorageService.deleteFile(filename);
            
            Map<String, String> response = new HashMap<>();
            response.put("message", "파일이 성공적으로 삭제되었습니다.");
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("Error deleting file: {}", e.getMessage(), e);
            
            Map<String, String> response = new HashMap<>();
            response.put("error", e.getMessage());
            
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }
}
