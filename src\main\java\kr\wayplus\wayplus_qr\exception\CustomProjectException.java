package kr.wayplus.wayplus_qr.exception;

import lombok.Getter;

@Getter
public class CustomProjectException extends RuntimeException {

    private final ErrorCode errorCode;

    public CustomProjectException(ErrorCode errorCode) {
        super(errorCode.getMessage()); // 부모 클래스(RuntimeException)의 생성자를 호출하여 메시지 설정
        this.errorCode = errorCode;
    }

    // 필요에 따라 메시지를 직접 설정하는 생성자 추가 가능
    public CustomProjectException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}
