package kr.wayplus.wayplus_qr.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.LocalDateTime;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Team {
    
    private Long projectId;
    private Long teamId;
    private Long eventId;
    private String eventName;
    private String teamName;
    private String teamCode;
    private String teamStatus;
    private Integer maxMembers;
    private String description;
    private String profileImagePath;
    private Long joinQrCodeId;
    private Long leaderAttendeeId;
    private String leaderName;
    private String leaderPhone;
    private String useYn;
    private String deleteYn;
    private String createUserEmail;
    private LocalDateTime createDate;
    private String updateUserEmail;
    private LocalDateTime lastUpdateDate;
    private String deleteUserEmail;
    private LocalDateTime deleteDate;
}