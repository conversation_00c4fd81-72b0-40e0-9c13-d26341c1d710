package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.dto.response.PreRegistrationFormResponseDto;
import kr.wayplus.wayplus_qr.entity.PreRegistrationForm;
import kr.wayplus.wayplus_qr.entity.PreRegistrationFormField;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Mapper
public interface PreRegistrationFormMapper {

    Optional<PreRegistrationForm> selectFormById(@Param("formId") Long formId);

    /**
     * ID로 사전 신청서 정보 조회 (DTO 반환)
     * AttendeeService의 transformSubmissionData에서 사용
     */
    PreRegistrationFormResponseDto selectFormResponseDtoById(@Param("formId") Long formId);

    /**
     * 특정 폼에 속한 모든 필드 조회 (삭제된 필드 포함)
     * AttendeeService의 transformSubmissionData에서 사용
     */
    List<PreRegistrationFormField> selectAllFieldsByFormId(@Param("formId") Long formId);

    List<PreRegistrationForm> selectFormsByProjectId(
            @Param("projectId") Long projectId,
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword,
            @Param("pageable") Pageable pageable);

    long countFormsByProjectId(
            @Param("projectId") Long projectId,
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword);

    boolean existsByProjectIdAndFormName(@Param("projectId") Long projectId, @Param("formName") String formName);

    int insertForm(PreRegistrationForm form);

    int updateForm(PreRegistrationForm form);

    int deleteFormById(@Param("formId") Long formId, @Param("userEmail") String userEmail);

    // --- Super Admin용 메서드 --- 

    /**
     * SUPER_ADMIN: 모든 사전 신청서 목록 조회 (페이징 및 동적 정렬)
     * @return 사전 신청서 목록
     */
    List<PreRegistrationForm> selectAllFormsForSuperAdmin(
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword,
            @Param("pageable") Pageable pageable);

    /**
     * SUPER_ADMIN: 모든 사전 신청서 개수 조회
     * @param searchColumn 검색 컬럼
     * @param searchKeyword 검색 키워드
     * @return 사전 신청서 총 개수
     */
    long countAllFormsForSuperAdmin(
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword);

    /**
     * SUPER_ADMIN: 특정 프로젝트의 사전 신청서 목록 조회 (페이징 및 동적 정렬)
     * @return 사전 신청서 목록
     */
    List<PreRegistrationForm> selectFormsByProjectIdForSuperAdmin(
            @Param("projectId") Long projectId,
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword,
            @Param("pageable") Pageable pageable);

    /**
     * SUPER_ADMIN: 특정 프로젝트의 사전 신청서 개수 조회
     * @param projectId 프로젝트 ID
     * @param searchColumn 검색 컬럼
     * @param searchKeyword 검색 키워드
     * @return 사전 신청서 총 개수
     */
    long countFormsByProjectIdForSuperAdmin(
            @Param("projectId") Long projectId,
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword);

    /**
     * 이벤트 ID로 사전 신청서 양식 조회
     * @param eventId 이벤트 ID
     * @return 사전 신청서 양식 정보
     */
    Optional<PreRegistrationForm> selectFormByEventId(@Param("eventId") Long eventId);
}
