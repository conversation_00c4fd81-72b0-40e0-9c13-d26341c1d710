-- =====================================================
-- 메뉴 시스템 초기 데이터 삽입 스크립트
-- =====================================================

-- 1. 기본 메뉴 데이터 삽입
-- =====================================================

-- 1-1. 최상위 메뉴들
INSERT INTO manage_menus (parent_menu_id, menu_code, menu_name, menu_url, menu_icon, menu_level, display_order, status, create_user_email, create_date, update_user_email, last_update_date, use_yn, delete_yn) VALUES
(NULL, 'DASHBOARD', '대시보드', '/dashboard', 'fas fa-tachometer-alt', 1, 1, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
(NULL, 'QR_MANAGEMENT', 'QR 관리', '/qr', 'fas fa-qrcode', 1, 2, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
(NULL, 'PROJECT_MANAGEMENT', '프로젝트 관리', '/projects', 'fas fa-project-diagram', 1, 3, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
(NULL, 'USER_MANAGEMENT', '사용자 관리', '/users', 'fas fa-users', 1, 4, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
(NULL, 'MENU_MANAGEMENT', '메뉴 관리', '/menus', 'fas fa-bars', 1, 5, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
(NULL, 'SYSTEM_SETTINGS', '시스템 설정', '/settings', 'fas fa-cog', 1, 6, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N');

-- 1-2. QR 관리 하위 메뉴들
INSERT INTO manage_menus (parent_menu_id, menu_code, menu_name, menu_url, menu_icon, menu_level, display_order, status, create_user_email, create_date, update_user_email, last_update_date, use_yn, delete_yn) VALUES
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), 'QR_LIST', 'QR 목록', '/qr/list', 'fas fa-list', 2, 1, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), 'QR_CREATE', 'QR 생성', '/qr/create', 'fas fa-plus', 2, 2, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), 'QR_STATISTICS', 'QR 통계', '/qr/statistics', 'fas fa-chart-bar', 2, 3, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N');

-- 1-3. 프로젝트 관리 하위 메뉴들
INSERT INTO manage_menus (parent_menu_id, menu_code, menu_name, menu_url, menu_icon, menu_level, display_order, status, create_user_email, create_date, update_user_email, last_update_date, use_yn, delete_yn) VALUES
((SELECT menu_id FROM manage_menus WHERE menu_code = 'PROJECT_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), 'PROJECT_LIST', '프로젝트 목록', '/projects/list', 'fas fa-list', 2, 1, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'PROJECT_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), 'PROJECT_CREATE', '프로젝트 생성', '/projects/create', 'fas fa-plus', 2, 2, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N');

-- 1-4. 사용자 관리 하위 메뉴들
INSERT INTO manage_menus (parent_menu_id, menu_code, menu_name, menu_url, menu_icon, menu_level, display_order, status, create_user_email, create_date, update_user_email, last_update_date, use_yn, delete_yn) VALUES
((SELECT menu_id FROM manage_menus WHERE menu_code = 'USER_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), 'USER_LIST', '사용자 목록', '/users/list', 'fas fa-list', 2, 1, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'USER_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), 'USER_ROLES', '역할 관리', '/users/roles', 'fas fa-user-tag', 2, 2, 'ACTIVE', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N');

-- 2. 기본 역할별 권한 설정
-- =====================================================

-- 2-1. SUPER_ADMIN 역할에 모든 메뉴 접근 권한 부여
INSERT INTO manage_menu_role_permissions (menu_id, role_id, is_accessible, create_user_email, create_date, update_user_email, last_update_date, use_yn, delete_yn)
SELECT menu_id, 'SUPER_ADMIN', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'
FROM manage_menus 
WHERE delete_yn = 'N';

-- 2-2. PROJECT_ADMIN 역할에 프로젝트 및 QR 관리 권한 부여
INSERT INTO manage_menu_role_permissions (menu_id, role_id, is_accessible, create_user_email, create_date, update_user_email, last_update_date, use_yn, delete_yn) VALUES
((SELECT menu_id FROM manage_menus WHERE menu_code = 'DASHBOARD' AND delete_yn = 'N' LIMIT 1), 'PROJECT_ADMIN', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), 'PROJECT_ADMIN', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_LIST' AND delete_yn = 'N' LIMIT 1), 'PROJECT_ADMIN', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_CREATE' AND delete_yn = 'N' LIMIT 1), 'PROJECT_ADMIN', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_STATISTICS' AND delete_yn = 'N' LIMIT 1), 'PROJECT_ADMIN', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'PROJECT_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), 'PROJECT_ADMIN', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'PROJECT_LIST' AND delete_yn = 'N' LIMIT 1), 'PROJECT_ADMIN', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'PROJECT_CREATE' AND delete_yn = 'N' LIMIT 1), 'PROJECT_ADMIN', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N');

-- 2-3. PROJECT_USER 역할에 기본 조회 권한 부여
INSERT INTO manage_menu_role_permissions (menu_id, role_id, is_accessible, create_user_email, create_date, update_user_email, last_update_date, use_yn, delete_yn) VALUES
((SELECT menu_id FROM manage_menus WHERE menu_code = 'DASHBOARD' AND delete_yn = 'N' LIMIT 1), 'PROJECT_USER', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), 'PROJECT_USER', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_LIST' AND delete_yn = 'N' LIMIT 1), 'PROJECT_USER', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_STATISTICS' AND delete_yn = 'N' LIMIT 1), 'PROJECT_USER', 'Y', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N');

-- 3. 예시 사용자별 권한 설정
-- =====================================================

-- 3-1. 특정 사용자에게 QR 관리 CRUD 권한 부여 (예시)
-- 실제 운영 시에는 관리자가 UI를 통해 설정
INSERT INTO manage_menu_user_permissions (menu_id, user_email, is_accessible, can_read, can_write, can_update, can_delete, permission_note, create_user_email, create_date, update_user_email, last_update_date, use_yn, delete_yn) VALUES
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '<EMAIL>', 'Y', 'Y', 'Y', 'Y', 'Y', '테스트 사용자 QR 관리 전체 권한', '<EMAIL>', NOW(), '<EMAIL>', NOW(), 'Y', 'N');

-- 스크립트 실행 완료 메시지
SELECT 'Menu system initial data insertion completed successfully!' as message;
