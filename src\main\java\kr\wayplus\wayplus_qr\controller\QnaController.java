package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.config.SearchTypeRegistry;
import kr.wayplus.wayplus_qr.dto.request.QnaAnswerCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.QnaAnswerUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.QnaQuestionCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.QnaQuestionUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.QnaAnswerResponseDto;
import kr.wayplus.wayplus_qr.dto.response.QnaQuestionResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ProjectResponseDto;
import kr.wayplus.wayplus_qr.dto.response.SearchTypeInfo;
import kr.wayplus.wayplus_qr.dto.response.AnswerTypeInfo;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.service.QnaService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/way/qna")
@RequiredArgsConstructor
@Slf4j
public class QnaController {

    private final QnaService qnaService;
    private final SearchTypeRegistry searchTypeRegistry;

    /**
     * QnA 질문 생성
     * @param requestDto 질문 생성 요청 DTO
     * @param userDetails 인증된 사용자 정보
     * @return 생성된 질문 정보
     */
    @PostMapping("/questions")
    public ResponseEntity<ApiResponseDto<QnaQuestionResponseDto>> createQuestion(
            @RequestBody QnaQuestionCreateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        log.info("QnA 질문 생성 요청 수신: {}, 사용자: {}", requestDto, userEmail);

        QnaQuestionResponseDto createdQuestion = qnaService.createQuestion(requestDto, userEmail);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success(createdQuestion));
    }

    /**
     * QnA 질문 목록 조회 (페이징, 필터링)
     * @param projectId 프로젝트 ID (SUPER_ADMIN이 아닌 경우 필수)
     * @param searchType 검색 유형 (선택)
     * @param searchKeyword 검색 키워드 (선택)
     * @param pageable 페이징 정보
     * @param userDetails 인증된 사용자 정보
     * @return 질문 목록 (페이징)
     */
    @GetMapping("/questions")
    public ResponseEntity<ApiResponseDto<ListResponseDto<QnaQuestionResponseDto>>> getQuestions(
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "answerType", required = false) String answerType,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "createDate,desc") Pageable pageable,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        String userRole = userDetails != null ? userDetails.getRoleId() : null;
        
        // 검색 타입 유효성 검사
        if (searchType != null && !searchTypeRegistry.isValidSearchType("qna", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }
        
        // 사용 가능한 검색 타입 목록 조회
        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("qna");
        
        // SUPER_ADMIN이 아니면서 projectId가 제공되지 않은 경우, 사용자가 속한 프로젝트 ID 사용
        if (projectId == null && userDetails != null && !"SUPER_ADMIN".equals(userRole)) {
            // 사용자가 속한 프로젝트 중 첫 번째 프로젝트만 사용
            List<ProjectResponseDto> userProjects = userDetails.getProjects();
            if (userProjects != null && !userProjects.isEmpty()) {
                projectId = userProjects.get(0).getProjectId();
            }
        }
        
        // 서비스 호출하여 질문 목록 조회
        Page<QnaQuestionResponseDto> questionPage = qnaService.getQuestions(
                 answerType, searchType, searchKeyword, pageable);

        // 응답 생성
        ListResponseDto<QnaQuestionResponseDto> responseDto = new ListResponseDto<>(questionPage, availableSearchTypes);
        
        // answerType 정보 추가
        if (answerType != null && !answerType.isEmpty()) {
            responseDto.setAnswerType(answerType);
        } else {
            responseDto.setAnswerType("ALL"); // 기본값
        }
        
        // answerSearchTypes 목록 추가
        List<AnswerTypeInfo> answerTypeList = List.of(
            new AnswerTypeInfo("ALL", "전체"),
            new AnswerTypeInfo("UNCOMPLETED", "답변 대기중"),
            new AnswerTypeInfo("COMPLETED", "답변 완료")
        );
        responseDto.setAnswerSearchTypes(answerTypeList);

        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    /**
     * QnA 질문 상세 조회
     * @param questionId 질문 ID
     * @param userDetails 인증된 사용자 정보
     * @return 질문 상세 정보
     */
    @GetMapping("/questions/{questionId}")
    public ResponseEntity<ApiResponseDto<QnaQuestionResponseDto>> getQuestionById(
            @PathVariable("questionId") Long questionId,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        QnaQuestionResponseDto question = qnaService.getQuestionById(questionId, userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(question));
    }

    /**
     * QnA 질문 수정
     * @param questionId 질문 ID
     * @param requestDto 수정 요청 DTO
     * @param userDetails 인증된 사용자 정보
     * @return 수정된 질문 정보
     */
    @PutMapping("/questions/{questionId}")
    public ResponseEntity<ApiResponseDto<QnaQuestionResponseDto>> updateQuestion(
            @PathVariable("questionId") Long questionId,
            @RequestBody QnaQuestionUpdateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        QnaQuestionResponseDto updatedQuestion = qnaService.updateQuestion(questionId, requestDto, userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(updatedQuestion));
    }

    /**
     * QnA 질문 삭제
     * @param questionId 질문 ID
     * @param userDetails 인증된 사용자 정보
     * @return 성공 응답 (데이터 없음)
     */
    @DeleteMapping("/questions/{questionId}")
    public ResponseEntity<ApiResponseDto<Void>> deleteQuestion(
            @PathVariable("questionId") Long questionId,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        qnaService.deleteQuestion(questionId, userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(null));
    }

    /**
     * QnA 답변 생성
     * @param requestDto 답변 생성 요청 DTO
     * @param userDetails 인증된 사용자 정보
     * @return 생성된 답변 정보
     */
    @PostMapping("/answers")
    public ResponseEntity<ApiResponseDto<QnaAnswerResponseDto>> createAnswer(
            @RequestBody QnaAnswerCreateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        log.info("QnA 답변 생성 요청 수신: {}, 사용자: {}", requestDto, userEmail);

        QnaAnswerResponseDto createdAnswer = qnaService.createAnswer(requestDto, userEmail);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success(createdAnswer));
    }

    /**
     * QnA 답변 수정
     * @param answerId 답변 ID
     * @param requestDto 수정 요청 DTO
     * @param userDetails 인증된 사용자 정보
     * @return 수정된 답변 정보
     */
    @PutMapping("/answers/{answerId}")
    public ResponseEntity<ApiResponseDto<QnaAnswerResponseDto>> updateAnswer(
            @PathVariable("answerId") Long answerId,
            @RequestBody QnaAnswerUpdateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        QnaAnswerResponseDto updatedAnswer = qnaService.updateAnswer(answerId, requestDto, userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(updatedAnswer));
    }

    /**
     * QnA 답변 삭제
     * @param answerId 답변 ID
     * @param userDetails 인증된 사용자 정보
     * @return 성공 응답 (데이터 없음)
     */
    @DeleteMapping("/answers/{answerId}")
    public ResponseEntity<ApiResponseDto<Void>> deleteAnswer(
            @PathVariable("answerId") Long answerId,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        qnaService.deleteAnswer(answerId, userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(null));
    }
}
