package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.dto.response.AttendeeSummaryDto;
import kr.wayplus.wayplus_qr.entity.Attendee;
import kr.wayplus.wayplus_qr.dto.AttendeeManualCheckDto;
import kr.wayplus.wayplus_qr.dto.response.DailyCountDto; 
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.springframework.data.domain.Pageable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Mapper
public interface AttendeeMapper {
    int insertAttendee(Attendee attendee);
    /**
     * 이벤트별 현재 신청자 수 조회
     */
    long countAttendeesByEventId(@Param("eventId") Long eventId);

    /**
     * 팀별 현재 참석자 수 조회
     */
    long countAttendeesByTeamId(@Param("teamId") Long teamId);

    Optional<Attendee> findById(@Param("attendeeId") Long attendeeId);
    void updateAttendee(Attendee attendee);

    // 새로 추가될 메소드들
    List<AttendeeSummaryDto> selectAttendeesByEventId(@Param("eventId") Long eventId, @Param("pageable") Pageable pageable);

    // 프로젝트 ID와 페이지 정보로 참석자 목록 조회 (요약 정보)
    List<AttendeeSummaryDto> selectAttendeesByProjectId(@Param("projectId") Long projectId, @Param("searchColumn") String searchColumn, @Param("searchKeyword") String searchKeyword, @Param("pageable") Pageable pageable);

    // 프로젝트 ID로 전체 참석자 수 조회
    long countAttendeesByProjectId(@Param("projectId") Long projectId, @Param("searchColumn") String searchColumn, @Param("searchKeyword") String searchKeyword);

    // 참석자 ID로 상세 정보 조회
    Optional<Attendee> selectAttendeeById(@Param("attendeeId") Long attendeeId);

    // 참석 확인 코드로 상세 정보 조회
    Optional<AttendeeSummaryDto> selectAttendeeByConfirmationCode(String confirmationCode);

    // confirmationCode 로 Attendee 엔티티 조회 (혜택 사용 등 단순 조회용)
    Attendee selectAttendeeByConfirmationCodeSimple(@Param("confirmationCode") String confirmationCode);

    // 참석자 ID로 삭제
    int deleteAttendeeById(@Param("attendeeId") Long attendeeId);

    // 참석 상태 업데이트
    void updateAttendedConfirmYn(@Param("attendeeId") Long attendeeId, @Param("attendedConfirmYn") String attendedConfirmYn);

    // 이름으로 참석자 검색 (수동 확인용)
    List<AttendeeManualCheckDto> selectAttendeesForManualCheck(@Param("eventId") Long eventId, @Param("name") String name);

    // QR 코드 경로 업데이트
    void updateQrCodePath(@Param("attendeeId") Long attendeeId, @Param("qrCodePath") String qrCodePath);

    // 참석자 논리적 삭제 (deleteYn='Y', deleteDate, deleteUserEmail 업데이트)
    int markAttendeeAsDeleted(@Param("attendeeId") Long attendeeId, @Param("adminEmail") String adminEmail);

    // 참석 상태 및 참석 시간 업데이트
    void updateAttendedConfirmYnAndDate(@Param("attendeeId") Long attendeeId, @Param("attendedConfirmYn") String attendedConfirmYn, @Param("attendedDate") LocalDateTime attendedDate);

    // 참석 상태 및 시간 업데이트 (ID 기준) - 수동 출석 처리용
    int updateAttendanceStatus(@Param("attendeeId") Long attendeeId, @Param("attendedYn") String attendedYn, @Param("attendedDate") LocalDateTime attendedDate);

    // 참석 상태 및 시간 업데이트 (Confirmation Code 기준)
    void updateAttendedConfirmYnAndDateByCode(@Param("confirmationCode") String confirmationCode, @Param("attendedYn") String attendedYn, @Param("attendedDate") LocalDateTime attendedDate);

    // 여러 참가자의 attended_confirm_yn 상태 업데이트
    void updateAttendeesConfirmStatus(@Param("attendeeIds") List<Long> attendeeIds, @Param("attendedConfirmYn") String attendedConfirmYn);

    // 수동 참석 확인을 위한 참석자 검색
    List<Attendee> searchAttendeesForManualCheck(@Param("eventId") Long eventId, @Param("name") String name);

    // ********** 전체 참가자 수 조회 추가 **********
    long selectTotalAttendeeCount();

    // 기간별 일일 참가자 등록 수 조회
    List<DailyCountDto> selectDailyAttendeeRegistrationCounts(@Param("startDate") String startDate, @Param("endDate") String endDate);

    // 기간별 일일 참가자 등록 수 (프로젝트별) 조회
    List<DailyCountDto> selectDailyAttendeeRegistrationCountsByProject(@Param("projectId") Long projectId,
                                                                      @Param("startDate") String startDate,
                                                                      @Param("endDate") String endDate);

    // --- Super Admin Specific Methods ---

    /**
     * 슈퍼 관리자용 참석자 목록 조회 (필터링, 페이징, 정렬 포함)
     * @return List of AttendeeSummaryDto
     */
    List<AttendeeSummaryDto> selectAttendeesForSuperAdmin(
        @Param("projectId") Long projectId,
        @Param("eventId") Long eventId,
        @Param("formId") Long formId,
        @Param("searchColumn") String searchColumn,
        @Param("searchKeyword") String searchKeyword,
        @Param("pageable") Pageable pageable);

    /**
     * 슈퍼 관리자용 참석자 수 조회 (필터링 포함)
     * @param projectId
     * @param eventId
     * @param formId
     * @param searchColumn
     * @param searchKeyword
     * @param pageable
     * @return Total count of matching attendees
     */
    long countAttendeesForSuperAdmin(
        @Param("projectId") Long projectId,
        @Param("eventId") Long eventId,
        @Param("formId") Long formId,
        @Param("searchColumn") String searchColumn,
        @Param("searchKeyword") String searchKeyword);

    List<DailyCountDto> getDailyAttendeeCountsByEventId(@Param("eventId") Long eventId);

}
