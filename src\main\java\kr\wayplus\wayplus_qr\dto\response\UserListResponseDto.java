package kr.wayplus.wayplus_qr.dto.response;

import kr.wayplus.wayplus_qr.entity.enums.UserStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserListResponseDto {
    private Long userIdx;
    private String userEmail;
    private String name;
    private String roleId; // roles 테이블의 role_id
    private UserStatus status;
    private LocalDateTime createDate;
    private String createUseEmail; // 생성자
    private String description;
    private String projectName;
}
