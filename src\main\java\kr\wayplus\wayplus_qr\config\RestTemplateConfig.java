package kr.wayplus.wayplus_qr.config;

import kr.wayplus.wayplus_qr.config.external.QrQuizMappingApiProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

@Configuration
@RequiredArgsConstructor
public class RestTemplateConfig {

    private final QrQuizMappingApiProperties thymeleafApiProperties;

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder
                .setConnectTimeout(Duration.ofMillis(thymeleafApiProperties.getTimeout().getConnect()))
                .setReadTimeout(Duration.ofMillis(thymeleafApiProperties.getTimeout().getRead()))
                .build();
    }
}
