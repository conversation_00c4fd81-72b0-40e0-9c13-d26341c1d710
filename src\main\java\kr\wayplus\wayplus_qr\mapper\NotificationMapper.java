package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.entity.Notification;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

@Mapper
public interface NotificationMapper {

    /**
     * 알림 생성
     * 
     * @param notification 알림 정보
     * @return 생성된 행 수
     */
    int insertNotification(Notification notification);

    /**
     * ID로 알림 조회
     * 
     * @param notificationId 알림 ID
     * @return 알림 정보 (Optional)
     */
    Optional<Notification> selectNotificationById(Long notificationId);

    /**
     * 사용자별 알림 목록 조회 (페이징)
     * 
     * @param userEmail 사용자 이메일
     * @param pageable 페이징 정보
     * @return 알림 목록
     */
    List<Notification> selectNotificationsByUserEmail(
            @Param("userEmail") String userEmail,
            @Param("pageable") Pageable pageable);

    /**
     * 사용자별 읽지 않은 알림 개수 조회
     * 
     * @param userEmail 사용자 이메일
     * @return 읽지 않은 알림 개수
     */
    long countUnreadNotificationsByUserEmail(String userEmail);

    /**
     * 알림 읽음 처리
     * 
     * @param notificationId 알림 ID
     * @return 업데이트된 행 수
     */
    int markNotificationAsRead(Long notificationId);

    /**
     * 사용자의 모든 알림 읽음 처리
     * 
     * @param userEmail 사용자 이메일
     * @return 업데이트된 행 수
     */
    int markAllNotificationsAsRead(String userEmail);

    /**
     * 알림 삭제
     * 
     * @param notificationId 알림 ID
     * @return 삭제된 행 수
     */
    int deleteNotification(Long notificationId);

    /**
     * 문의에 속한 모든 알림 삭제
     * 
     * @param inquiryId 문의 ID
     * @return 삭제된 행 수
     */
    int deleteNotificationsByInquiryId(Long inquiryId);
}
