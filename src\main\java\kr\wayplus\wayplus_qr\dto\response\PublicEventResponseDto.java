package kr.wayplus.wayplus_qr.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;

import kr.wayplus.wayplus_qr.entity.EventStatus;
import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PublicEventResponseDto {
    private Long eventId;
    private Long projectId;
    private String eventName;
    private String teamName;
    private String teamCode;
    private String description;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime startDate;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime endDate;
    private String location;
    private Integer participantLimit;
    private Long preRegistrationFormId;
    private PreRegistrationFormResponseDto preRegistrationForm;
    private Long linkedQrCodeId;
    private String eventImagePath;
    private EventStatus status;
    private String createUserEmail;
    private LocalDateTime createDate;
}
