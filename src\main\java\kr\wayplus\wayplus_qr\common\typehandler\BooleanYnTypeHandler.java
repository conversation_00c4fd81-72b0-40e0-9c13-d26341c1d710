package kr.wayplus.wayplus_qr.common.typehandler;

import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * MyBatis TypeHandler for converting between Java Boolean and Database ENUM('Y', 'N') or CHAR(1) ('Y'/'N').
 */
@MappedJdbcTypes(JdbcType.CHAR) // Primarily target CHAR or ENUM represented as CHAR
@MappedTypes(Boolean.class)
public class BooleanYnTypeHandler extends BaseTypeHandler<Boolean> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Boolean parameter, JdbcType jdbcType) throws SQLException {
        // Boolean true -> 'Y', false -> 'N'
        ps.setString(i, parameter ? "Y" : "N");
    }

    @Override
    public Boolean getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String sqlValue = rs.getString(columnName);
        // 'Y' -> true, others (including null and 'N') -> false
        // Consider throwing an exception for unexpected values if needed.
        return "Y".equalsIgnoreCase(sqlValue);
    }

    @Override
    public Boolean getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String sqlValue = rs.getString(columnIndex);
        return "Y".equalsIgnoreCase(sqlValue);
    }

    @Override
    public Boolean getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String sqlValue = cs.getString(columnIndex);
        return "Y".equalsIgnoreCase(sqlValue);
    }
}
