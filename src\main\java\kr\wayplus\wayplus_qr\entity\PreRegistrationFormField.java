package kr.wayplus.wayplus_qr.entity;

import lombok.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreRegistrationFormField {
    private Long fieldId;
    private Long formId;
    private String fieldLabel;
    private String fieldName;
    private String fieldType;
    private String isRequiredYn;
    private String helpText;
    private String options;
    private Integer displayOrder;
    private String createUserEmail;
    private LocalDateTime createDate;
    private String updateUserEmail;
    private LocalDateTime lastUpdateDate;
    private String deleteUserEmail;
    private LocalDateTime deleteDate;
    private String useYn;
    private String deleteYn;
}
