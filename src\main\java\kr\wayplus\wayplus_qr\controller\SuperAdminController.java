package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.config.SearchTypeRegistry;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.AttendeeSummaryDto;
import kr.wayplus.wayplus_qr.dto.response.EventResponseDto;
import kr.wayplus.wayplus_qr.dto.response.LandingPageResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.PreRegistrationFormResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ProjectResponseDto;
import kr.wayplus.wayplus_qr.dto.response.QrCodeResponseDto;
import kr.wayplus.wayplus_qr.dto.response.SearchTypeInfo;
import kr.wayplus.wayplus_qr.service.AttendeeService;
import kr.wayplus.wayplus_qr.service.EventService;
import kr.wayplus.wayplus_qr.service.LandingPageService;
import kr.wayplus.wayplus_qr.service.PreRegistrationFormService;
import kr.wayplus.wayplus_qr.service.ProjectService;
import kr.wayplus.wayplus_qr.service.QrCodeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/way/super")
@RequiredArgsConstructor
@PreAuthorize("hasAuthority('SUPER_ADMIN')") // 클래스 레벨 권한 설정
public class SuperAdminController {

    private final QrCodeService qrCodeService;
    private final ProjectService projectService; // ProjectService 주입
    private final LandingPageService landingPageService; // LandingPageService 주입
    private final EventService eventService; // EventService 주입
    private final PreRegistrationFormService preRegistrationFormService; // Service 필드 추가
    private final AttendeeService attendeeService; // AttendeeService 주입 추가
    private final SearchTypeRegistry searchTypeRegistry;

    /**
     * SUPER_ADMIN: 모든 QR 코드 목록 조회 (페이징)
     */
    @GetMapping("/qr-codes/all")
    public ResponseEntity<ApiResponseDto<ListResponseDto<QrCodeResponseDto>>> getAllQrCodes(
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10) Pageable pageable) {
        log.info("SUPER_ADMIN - QR 코드 목록 조회 요청. Project ID: {}, 페이지: {}, 크기: {}", projectId, pageable.getPageNumber(), pageable.getPageSize());
        if (searchType != null && !searchTypeRegistry.isValidSearchType("qrcode", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }

        Page<QrCodeResponseDto> qrCodePage = qrCodeService.getAllQrCodes(projectId, searchType, searchKeyword, pageable);

        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("qrcode");
        ListResponseDto<QrCodeResponseDto> responseDto = new ListResponseDto<>(qrCodePage, availableSearchTypes);

        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    /**
     * SUPER_ADMIN: 모든 프로젝트 목록 조회 (페이징)
     */
    @GetMapping("/projects/all")
    public ResponseEntity<ApiResponseDto<ListResponseDto<ProjectResponseDto>>> getAllProjects(
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10) Pageable pageable) {
        log.info("SUPER_ADMIN - 모든 프로젝트 목록 조회 요청. 페이지: {}, 크기: {}", pageable.getPageNumber(), pageable.getPageSize());

        if (searchType != null && !searchTypeRegistry.isValidSearchType("project", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }

        Page<ProjectResponseDto> projectPage = projectService.getAllProjectsForSuperAdmin(searchType, searchKeyword, pageable);

        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("project");
        ListResponseDto<ProjectResponseDto> responseDto = new ListResponseDto<>(projectPage, availableSearchTypes);

        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    /**
     * SUPER_ADMIN: 모든 랜딩 페이지 목록 조회 (페이징 및 동적 정렬)
     */
    @GetMapping("/landing-pages/all")
    public ResponseEntity<ApiResponseDto<ListResponseDto<LandingPageResponseDto>>> getAllLandingPages(
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "createDate", direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("SUPER_ADMIN - 랜딩 페이지 목록 조회 요청. Project ID: {}, 페이지: {}, 크기: {}, 정렬: {}",
                projectId, pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort());

        if (searchType != null && !searchTypeRegistry.isValidSearchType("landingPage", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }

        Page<LandingPageResponseDto> landingPagePage = landingPageService.getAllLandingPagesForSuperAdmin(projectId, searchType, searchKeyword, pageable);

        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("landingPage");
        ListResponseDto<LandingPageResponseDto> responseDto = new ListResponseDto<>(landingPagePage, availableSearchTypes);

        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    /**
     * SUPER_ADMIN: 모든 이벤트 목록 조회 (페이징 및 프로젝트 필터링)
     */
    @GetMapping("/events/all")
    public ResponseEntity<ApiResponseDto<ListResponseDto<EventResponseDto>>> getAllEvents(
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "createDate", direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("SUPER_ADMIN - 이벤트 목록 조회 요청. Project ID: {}, 페이지: {}, 크기: {}, 정렬: {}",
                projectId, pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort());
        if (searchType != null && !searchTypeRegistry.isValidSearchType("event", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }

        Page<EventResponseDto> eventPage = eventService.getAllEventsForSuperAdmin(projectId, searchType, searchKeyword, pageable);

        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("event");
        ListResponseDto<EventResponseDto> responseDto = new ListResponseDto<>(eventPage, availableSearchTypes);

        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    /**
     * SUPER_ADMIN: 모든 사전 신청서 목록 조회 (페이징 및 프로젝트 필터링)
     */
    @GetMapping("/pre-registration-forms/all")
    public ResponseEntity<ApiResponseDto<ListResponseDto<PreRegistrationFormResponseDto>>> getAllPreRegistrationForms(
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "createDate", direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("SUPER_ADMIN - 사전 신청서 목록 조회 요청. Project ID: {}, 페이지: {}, 크기: {}, 정렬: {}",
                projectId, pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort());

        if (searchType != null && !searchTypeRegistry.isValidSearchType("preRegistrationForm", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }

        Page<PreRegistrationFormResponseDto> formPage = preRegistrationFormService.getAllPreRegistrationFormsForSuperAdmin(projectId, searchType, searchKeyword, pageable);

        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("preRegistrationForm");
        ListResponseDto<PreRegistrationFormResponseDto> responseDto = new ListResponseDto<>(formPage, availableSearchTypes);

        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    /**
     * SUPER_ADMIN: 모든 참가자 목록 조회 (페이징 및 프로젝트/이벤트/신청서 필터링)
     */
    @GetMapping("/attendees/all")
    public ResponseEntity<ApiResponseDto<ListResponseDto<AttendeeSummaryDto>>> getAllAttendees(
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "eventId", required = false) Long eventId,
            @RequestParam(value = "formId", required = false) Long formId,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "registrationDate", direction = Sort.Direction.DESC) Pageable pageable) {
        log.info("SUPER_ADMIN - 참가자 목록 조회 요청. Project ID: {}, Event ID: {}, Form ID: {}, 페이지: {}, 크기: {}, 정렬: {}",
                projectId, eventId, formId, pageable.getPageNumber(), pageable.getPageSize(), pageable.getSort());


        if (searchType != null && !searchTypeRegistry.isValidSearchType("attendee", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }
        
        Page<AttendeeSummaryDto> attendeePage = attendeeService.getAllAttendeesForSuperAdmin(projectId, eventId, formId, searchType, searchKeyword, pageable);

        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("attendee");
        ListResponseDto<AttendeeSummaryDto> responseDto = new ListResponseDto<>(attendeePage, availableSearchTypes);

        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }
}
