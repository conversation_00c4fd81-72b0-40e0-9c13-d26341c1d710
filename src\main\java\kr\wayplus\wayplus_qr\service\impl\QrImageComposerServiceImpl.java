package kr.wayplus.wayplus_qr.service.impl;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import kr.wayplus.wayplus_qr.dto.QrCodeDesignOptionsDto;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import kr.wayplus.wayplus_qr.service.QrImageComposerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.geom.AffineTransform;
import java.awt.geom.Ellipse2D;
import java.awt.geom.RoundRectangle2D;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
@RequiredArgsConstructor
public class QrImageComposerServiceImpl implements QrImageComposerService {

    // 상수 정의
    private static final int DEFAULT_CANVAS_WIDTH = 1000;
    private static final int DEFAULT_CANVAS_HEIGHT = 1000;
    private static final String DEFAULT_FOREGROUND_COLOR = "#000000";
    private static final String DEFAULT_BACKGROUND_COLOR = "#FFFFFF";
    private static final double DEFAULT_QR_SIZE_RATIO = 0.5;
    private static final double DEFAULT_LOGO_RATIO = 0.2;
    private static final String DEFAULT_ERROR_CORRECTION_LEVEL = "L";
    private static final String DEFAULT_BG_FIT = "COVER";
    private static final double DEFAULT_QR_POSITION_X = 0.5;
    private static final double DEFAULT_QR_POSITION_Y = 0.5;

    @Override
    public void generateQrWithBackgroundAsPng(String contentToEncode, QrCodeDesignOptionsDto designOptions,
                                              String backgroundImagePath, String logoImagePath, String outputPngPath) {

        try {
            // 레이아웃 옵션 로깅
            logLayoutOptions(designOptions);

            // 1. 배경 이미지 로드
            BufferedImage backgroundImage = ImageIO.read(new File(backgroundImagePath));
            if (backgroundImage == null) {
                log.error("Failed to load background image: {}", backgroundImagePath);
                throw new QRcodeException(ErrorCode.QR_CODE_GENERATION_FAILED, "배경 이미지를 로드할 수 없습니다.");
            }

            // 2. 캔버스 크기 결정 (디자인 옵션 또는 배경 이미지 기반)
            int canvasWidth = getCanvasWidth(designOptions, backgroundImage);
            int canvasHeight = getCanvasHeight(designOptions, backgroundImage);

            // 3. QR 코드 생성
            // 참고: 프론트엔드 여백 정보는 현재 DTO 구조에서 제공되지 않음
            // 프론트엔드 개발자와 협의하여 DTO 구조 조정 후 여백 처리 로직 추가 필요
            log.info("[QrImageComposer] Using default QR code generation. Custom pixel margin will be implemented after DTO structure update.");
            BitMatrix bitMatrix = generateQrCodeBitMatrix(contentToEncode, designOptions);
            BufferedImage qrCodeImage = createQrCodeImage(bitMatrix, designOptions);

            // 5. 로고 적용 (로고가 있다면)
            if (logoImagePath != null && !logoImagePath.isEmpty()) {
                applyLogoToQrCode(qrCodeImage, logoImagePath, designOptions);
            }

            // 6. 최종 합성 이미지 생성
            BufferedImage composedImage = new BufferedImage(canvasWidth, canvasHeight, BufferedImage.TYPE_INT_ARGB);
            Graphics2D g2d = composedImage.createGraphics();
            try {
                setupHighQualityRendering(g2d);

                // 7. 배경 이미지 배치 (fit 적용)
                drawBackgroundImage(g2d, backgroundImage, composedImage, designOptions);

                // 8. QR 코드 배치
                drawQrCodeOnCanvas(g2d, qrCodeImage, composedImage, designOptions);
            } finally {
                g2d.dispose();
            }

            // 9. 파일로 저장
            saveImageAsPng(composedImage, outputPngPath);

            log.info("Successfully generated QR code with background image at: {}", outputPngPath);
        } catch (IOException e) {
            log.error("IOException during QR code with background generation: {}", e.getMessage(), e);
            throw new QRcodeException(ErrorCode.QR_CODE_FILE_SAVE_FAILED, "QR 코드 이미지 파일 생성 중 I/O 오류 발생", e);
        } catch (WriterException e) {
            log.error("WriterException during QR code generation: {}", e.getMessage(), e);
            throw new QRcodeException(ErrorCode.QR_CODE_GENERATION_FAILED, "QR 코드 생성 중 오류 발생", e);
        } catch (Exception e) {
            log.error("Unexpected error during QR code generation: {}", e.getMessage(), e);
            throw new QRcodeException(ErrorCode.QR_CODE_GENERATION_FAILED, "QR 코드 생성 중 예기치 않은 오류 발생", e);
        }
    }

    private int getCanvasWidth(QrCodeDesignOptionsDto designOptions, BufferedImage backgroundImage) {
        if (designOptions != null && designOptions.getLayoutOptions() != null &&
                designOptions.getLayoutOptions().getCanvas() != null &&
                designOptions.getLayoutOptions().getCanvas().getWidthPx() != null) {
            return designOptions.getLayoutOptions().getCanvas().getWidthPx();
        }
        return backgroundImage.getWidth();
    }

    private int getCanvasHeight(QrCodeDesignOptionsDto designOptions, BufferedImage backgroundImage) {
        if (designOptions != null && designOptions.getLayoutOptions() != null &&
                designOptions.getLayoutOptions().getCanvas() != null &&
                designOptions.getLayoutOptions().getCanvas().getHeightPx() != null) {
            return designOptions.getLayoutOptions().getCanvas().getHeightPx();
        }
        return backgroundImage.getHeight();
    }

    private BitMatrix generateQrCodeBitMatrix(String contentToEncode, QrCodeDesignOptionsDto designOptions) throws WriterException {
        return generateQrCodeBitMatrix(contentToEncode, designOptions, 300, 300);
    }

    private BitMatrix generateQrCodeBitMatrix(String contentToEncode, QrCodeDesignOptionsDto designOptions, int width, int height) throws WriterException {
        QRCodeWriter qrCodeWriter = new QRCodeWriter();
        Map<EncodeHintType, Object> hints = new HashMap<>();

        // 오류 복원 수준 설정
        String errorCorrectionLevelStr = (designOptions != null && designOptions.getErrorCorrectionLevel() != null)
                ? designOptions.getErrorCorrectionLevel().toUpperCase()
                : DEFAULT_ERROR_CORRECTION_LEVEL;

        ErrorCorrectionLevel errorCorrectionLevel;
        switch (errorCorrectionLevelStr) {
            case "H":
                errorCorrectionLevel = ErrorCorrectionLevel.H;
                break;
            case "Q":
                errorCorrectionLevel = ErrorCorrectionLevel.Q;
                break;
            case "M":
                errorCorrectionLevel = ErrorCorrectionLevel.M;
                break;
            default:
                errorCorrectionLevel = ErrorCorrectionLevel.L;
        }

        hints.put(EncodeHintType.ERROR_CORRECTION, errorCorrectionLevel);
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        hints.put(EncodeHintType.MARGIN, 0); // 마진 제거 (수동으로 여백을 제어하기 위함)

        // 지정된 크기로 QR 코드 생성
        return qrCodeWriter.encode(contentToEncode, BarcodeFormat.QR_CODE, width, height, hints);
    }

    private BufferedImage createQrCodeImage(BitMatrix bitMatrix, QrCodeDesignOptionsDto designOptions) {
        int matrixWidth = bitMatrix.getWidth();
        int matrixHeight = bitMatrix.getHeight();

        log.info("[QrImageComposer] BitMatrix dimensions: {}x{}", matrixWidth, matrixHeight);

        BufferedImage qrImage = new BufferedImage(matrixWidth, matrixHeight, BufferedImage.TYPE_INT_ARGB);

        // 점 색상과 배경색 파싱
        String foregroundColorStr = getForegroundColor(designOptions);
        String backgroundColorStr = getBackgroundColor(designOptions);
        String dotsType = getDotsType(designOptions);

        Color foregroundColor = parseColor(foregroundColorStr, Color.BLACK);
        Color backgroundColor = parseColor(backgroundColorStr, Color.WHITE);

        Graphics2D g2d = qrImage.createGraphics();
        try {
            setupHighQualityRendering(g2d);

            // 배경색으로 전체 채우기
            g2d.setColor(backgroundColor);
            g2d.fillRect(0, 0, matrixWidth, matrixHeight);

            // 파인더 패턴 위치 (좌상단, 우상단, 좌하단)
            boolean[][] finderPatternsToSkip = new boolean[matrixWidth][matrixHeight];
            markFinderPatternAreas(finderPatternsToSkip, matrixWidth, matrixHeight); // 이 영역은 나중에 건너뛸 것임

            // 새로운 스타일로 파인더 패턴(눈) 그리기
            int finderModuleSize = 7; // 파인더 패턴은 7x7 모듈
            // 좌상단
            log.info("[QrImageComposer] Attempting to draw TOP-LEFT finder pattern at (0, 0) with size {}.", finderModuleSize);
            drawStyledFinderPattern(g2d, 0, 0, finderModuleSize, designOptions, backgroundColor, foregroundColor);
            // 우상단
            log.info("[QrImageComposer] Attempting to draw TOP-RIGHT finder pattern at ({}, 0) with size {}.", matrixWidth - finderModuleSize, finderModuleSize);
            drawStyledFinderPattern(g2d, matrixWidth - finderModuleSize, 0, finderModuleSize, designOptions, backgroundColor, foregroundColor);
            // 좌하단
            log.info("[QrImageComposer] Attempting to draw BOTTOM-LEFT finder pattern at (0, {}) with size {}.", matrixHeight - finderModuleSize, finderModuleSize);
            drawStyledFinderPattern(g2d, 0, matrixHeight - finderModuleSize, finderModuleSize, designOptions, backgroundColor, foregroundColor);

            // QR 코드 나머지 점들 그리기
            for (int y = 0; y < matrixHeight; y++) {
                for (int x = 0; x < matrixWidth; x++) {
                    if (finderPatternsToSkip[x][y]) {
                        continue; // 스타일된 파인더 패턴 영역이므로 건너뛰기
                    }

                    if (bitMatrix.get(x, y)) {
                        g2d.setColor(foregroundColor);

                        // 일반 QR 코드 모듈 타입 적용
                        if ("circle".equalsIgnoreCase(dotsType)) {
                            g2d.fill(new Ellipse2D.Double(x, y, 1, 1));
                        } else if ("rounded".equalsIgnoreCase(dotsType)) {
                            g2d.fill(new RoundRectangle2D.Double(x, y, 1, 1, 0.5, 0.5));
                        } else {
                            g2d.fillRect(x, y, 1, 1);
                        }
                    }
                }
            }
        } finally {
            g2d.dispose();
        }

        log.info("[QrImageComposer] Created source qrImage dimensions: {}x{}", qrImage.getWidth(), qrImage.getHeight());
        return qrImage;
    }

    // 새로운 메소드: 스타일이 적용된 파인더 패턴(눈) 그리기
    private void drawStyledFinderPattern(Graphics2D g, int x, int y, int moduleCount, QrCodeDesignOptionsDto designOptions, Color defaultBackgroundColor, Color defaultForegroundColor) {
        log.info("[QrImageComposer] drawStyledFinderPattern called for position ({}, {}), moduleCount: {}", x, y, moduleCount);

        QrCodeDesignOptionsDto.DotsOptions dotsOptions = (designOptions != null) ? designOptions.getDotsOptions() : null;
        QrCodeDesignOptionsDto.CornersSquareOptions csOpts = (dotsOptions != null) ? dotsOptions.getCornersSquareOptions() : null;
        QrCodeDesignOptionsDto.CornersDotOptions cdOpts = (dotsOptions != null) ? dotsOptions.getCornersDotOptions() : null;

        // 기본값 설정
        Color outerSquareColor = defaultForegroundColor;
        String outerSquareType = "square"; // 기본 외부 사각형 타입
        Color innerDotColor = defaultForegroundColor;
        String innerDotType = "dot";    // 기본 내부 점 타입

        if (csOpts != null) {
            outerSquareColor = parseColor(csOpts.getColor(), defaultForegroundColor);
            outerSquareType = (csOpts.getType() != null && !csOpts.getType().isEmpty()) ? csOpts.getType() : "square";
            log.info("[QrImageComposer] Using CornersSquareOptions: Color='{}', Type='{}'", csOpts.getColor(), csOpts.getType());
        } else {
            log.info("[QrImageComposer] CornersSquareOptions not provided, using default foreground color and square type for outer square.");
        }

        if (cdOpts != null) {
            innerDotColor = parseColor(cdOpts.getColor(), defaultForegroundColor);
            innerDotType = (cdOpts.getType() != null && !cdOpts.getType().isEmpty()) ? cdOpts.getType() : "dot";
            log.info("[QrImageComposer] Using CornersDotOptions: Color='{}', Type='{}'", cdOpts.getColor(), cdOpts.getType());
        } else {
            log.info("[QrImageComposer] CornersDotOptions not provided, using default foreground color and dot type for inner dot.");
        }

        int outerSquareSize = moduleCount; // 모듈 수만큼 크기 (예: 7x7)
        int innerDotSize = 3;          // 내부 점은 보통 3x3 모듈
        int innerDotOffset = (moduleCount - innerDotSize) / 2; // 내부 점을 중앙에 위치시키기 위한 오프셋 (7-3)/2 = 2

        // 1. 외부 형태 그리기
        g.setColor(outerSquareColor);
        log.info("[QrImageComposer] Drawing outer shape at ({}, {}) with size {} and color {}", x, y, outerSquareSize, outerSquareColor);
        
        // 둘 중 하나만 지원: circle 또는 square
        if ("circle".equalsIgnoreCase(outerSquareType) || "dot".equalsIgnoreCase(outerSquareType) || "rounded".equalsIgnoreCase(outerSquareType)) {
            // 원형 파인더 패턴
            g.fill(new Ellipse2D.Double(x, y, outerSquareSize, outerSquareSize));
        } else {
            // 사각형 파인더 패턴 (기본값 및 square 타입)
            g.fillRect(x, y, outerSquareSize, outerSquareSize);
        }

        // 2. 내부 점 그리기 (외부 형태 위에 덮어쓰기)
        g.setColor(innerDotColor);
        log.info("[QrImageComposer] Drawing inner dot at ({}, {}) with size {} and color {}", x + innerDotOffset, y + innerDotOffset, innerDotSize, innerDotColor);
        
        // 내부 점도 둘 중 하나만 지원: circle 또는 square
        if ("circle".equalsIgnoreCase(innerDotType) || "dot".equalsIgnoreCase(innerDotType) || "rounded".equalsIgnoreCase(innerDotType)) {
            // 원형 내부 점
            g.fill(new Ellipse2D.Double(x + innerDotOffset, y + innerDotOffset, innerDotSize, innerDotSize));
        } else {
            // 사각형 내부 점 (기본값 및 square 타입)
            g.fillRect(x + innerDotOffset, y + innerDotOffset, innerDotSize, innerDotSize);
        }
        log.info("[QrImageComposer] drawStyledFinderPattern finished for position ({}, {}).", x, y);
    }

    private void applyLogoToQrCode(BufferedImage qrImage, String logoPath, QrCodeDesignOptionsDto designOptions) {
        if (logoPath == null || logoPath.isEmpty()) {
            return;
        }

        BufferedImage logoImage = null;
        try {
            logoImage = ImageIO.read(new File(logoPath));
        } catch (IOException e) {
            log.warn("Failed to load logo image: {}", logoPath);
            return;
        }

        if (logoImage == null) {
            log.warn("Failed to load logo image: {}", logoPath);
            return;
        }

        // 로고 비율 결정
        double logoRatio = (designOptions != null && designOptions.getLogoRatio() != null)
                ? designOptions.getLogoRatio()
                : DEFAULT_LOGO_RATIO;

        int qrWidth = qrImage.getWidth();
        int qrHeight = qrImage.getHeight();

        int logoWidth = (int) Math.round(qrWidth * logoRatio);
        int logoHeight = (int) Math.round(qrHeight * logoRatio);

        // 로고 위치 계산 (QR 코드 중앙)
        int logoX = (qrWidth - logoWidth) / 2;
        int logoY = (qrHeight - logoHeight) / 2;

        Graphics2D g2d = qrImage.createGraphics();
        try {
            setupHighQualityRendering(g2d);

            // 로고 배경에 흰색 원 그리기
            g2d.setColor(Color.WHITE);
            g2d.fill(new Ellipse2D.Double(logoX, logoY, logoWidth, logoHeight));

            // 로고 그리기
            g2d.drawImage(logoImage, logoX, logoY, logoWidth, logoHeight, null);
        } finally {
            g2d.dispose();
        }
    }

    private void drawBackgroundImage(Graphics2D g2d, BufferedImage backgroundImage, BufferedImage composedImage, QrCodeDesignOptionsDto designOptions) {
        int canvasWidth = composedImage.getWidth();
        int canvasHeight = composedImage.getHeight();

        String backgroundFit = getBackgroundFit(designOptions);

        double scaleX = 1.0;
        double scaleY = 1.0;
        int x = 0;
        int y = 0;

        switch (backgroundFit.toUpperCase()) {
            case "COVER":
                // 이미지가 캔버스를 완전히 덮도록 (잘릴 수 있음)
                scaleX = Math.max((double) canvasWidth / backgroundImage.getWidth(),
                        (double) canvasHeight / backgroundImage.getHeight());
                scaleY = scaleX;

                x = (int) ((canvasWidth - backgroundImage.getWidth() * scaleX) / 2);
                y = (int) ((canvasHeight - backgroundImage.getHeight() * scaleY) / 2);
                break;

            case "CONTAIN":
                // 이미지 전체가 보이도록 (여백이 생길 수 있음)
                scaleX = Math.min((double) canvasWidth / backgroundImage.getWidth(),
                        (double) canvasHeight / backgroundImage.getHeight());
                scaleY = scaleX;

                x = (int) ((canvasWidth - backgroundImage.getWidth() * scaleX) / 2);
                y = (int) ((canvasHeight - backgroundImage.getHeight() * scaleY) / 2);
                break;

            case "STRETCH":
            default:
                // 이미지를 캔버스에 맞게 늘림
                scaleX = (double) canvasWidth / backgroundImage.getWidth();
                scaleY = (double) canvasHeight / backgroundImage.getHeight();
                break;
        }

        // 변환 적용하여 이미지 그리기
        AffineTransform transform = new AffineTransform();
        transform.translate(x, y);
        transform.scale(scaleX, scaleY);

        g2d.drawImage(backgroundImage, transform, null);
    }

    private void drawQrCodeOnCanvas(Graphics2D g2d, BufferedImage qrCodeImage, BufferedImage composedImage, QrCodeDesignOptionsDto designOptions) {
        int canvasWidth = composedImage.getWidth();
        int canvasHeight = composedImage.getHeight();

        // QR 코드 위치 정보
        Double fePositionXRatio = null; // 프론트엔드에서 받은 X 비율
        Double fePositionYRatio = null; // 프론트엔드에서 받은 Y 비율
        Double feSizeRatio = null;    // 프론트엔드에서 받은 크기 비율

        if (designOptions != null && designOptions.getLayoutOptions() != null &&
                designOptions.getLayoutOptions().getQrPlacement() != null) {

            // 위치 정보 추출
            if (designOptions.getLayoutOptions().getQrPlacement().getPosition() != null) {
                fePositionXRatio = designOptions.getLayoutOptions().getQrPlacement().getPosition().getX();
                fePositionYRatio = designOptions.getLayoutOptions().getQrPlacement().getPosition().getY();
            }

            // 크기 비율 추출
            feSizeRatio = designOptions.getLayoutOptions().getQrPlacement().getSizeRatio();
        }

        // QR 코드 크기 비율 (캔버스 대비) - 프론트엔드 값 또는 기본값 사용
        double actualSizeRatio = feSizeRatio != null ? feSizeRatio : DEFAULT_QR_SIZE_RATIO;

        int qrWidth = (int) Math.round(canvasWidth * actualSizeRatio);
        int qrHeight = qrWidth; // QR 코드는 정사각형

        // QR 코드의 위치 계산 (x, y는 0.0~1.0 사이의 값) - 프론트엔드 값 또는 기본값 사용
        double actualPositionXRatio = fePositionXRatio != null ? fePositionXRatio : DEFAULT_QR_POSITION_X;
        double actualPositionYRatio = fePositionYRatio != null ? fePositionYRatio : DEFAULT_QR_POSITION_Y;

        // QR 코드의 좌상단 좌표를 캔버스 전체 너비/높이에 대한 직접 비율로 계산합니다.
        double exactXCalc = canvasWidth * actualPositionXRatio;
        double exactYCalc = canvasHeight * actualPositionYRatio;
        int finalX = (int) Math.round(exactXCalc);
        int finalY = (int) Math.round(exactYCalc);

        // Log received eye styling options
        if (designOptions != null && designOptions.getDotsOptions() != null) {
            QrCodeDesignOptionsDto.DotsOptions dotsOptions = designOptions.getDotsOptions();
            QrCodeDesignOptionsDto.CornersSquareOptions csOpts = dotsOptions.getCornersSquareOptions();
            QrCodeDesignOptionsDto.CornersDotOptions cdOpts = dotsOptions.getCornersDotOptions();

            if (csOpts != null) {
                log.info("[QrImageComposer] Received CornersSquareOptions: Color='{}', Type='{}'",
                        csOpts.getColor(), csOpts.getType());
            } else {
                log.info("[QrImageComposer] Received CornersSquareOptions: Not provided");
            }

            if (cdOpts != null) {
                log.info("[QrImageComposer] Received CornersDotOptions: Color='{}', Type='{}'",
                        cdOpts.getColor(), cdOpts.getType());
            } else {
                log.info("[QrImageComposer] Received CornersDotOptions: Not provided");
            }
        } else {
            log.info("[QrImageComposer] DotsOptions for eye styling not available in designOptions.");
        }

        // 상세 좌표 로깅 추가
        log.info("[QrImageComposer] Coordinate Calculation Details:");
        log.info("[QrImageComposer]   Canvas Dimensions: width={}, height={}", canvasWidth, canvasHeight);
        log.info("[QrImageComposer]   Frontend Provided Ratios: positionXRatio={}, positionYRatio={}, sizeRatio={}",
                (fePositionXRatio != null ? fePositionXRatio : "N/A"),
                (fePositionYRatio != null ? fePositionYRatio : "N/A"),
                (feSizeRatio != null ? feSizeRatio : "N/A"));
        log.info("[QrImageComposer]   Actual Ratios Used: positionXRatio={}, positionYRatio={}, sizeRatio={}",
                actualPositionXRatio,
                actualPositionYRatio,
                actualSizeRatio);
        log.info("[QrImageComposer]   Calculated Pre-rounding Pixel Coords: exactXCalc={}, exactYCalc={}",
                exactXCalc, exactYCalc);
        log.info("[QrImageComposer]   Final Rounded Pixel Coords (Top-Left): finalX={}, finalY={}", finalX, finalY);
        log.info("[QrImageComposer]   Calculated QR Size: qrWidth={}, qrHeight={}", qrWidth, qrHeight);

        log.info("[QrImageComposer] Drawing qrCodeImage (source {}x{}) onto canvas at ({},{}) with target draw size {}x{}",
                qrCodeImage.getWidth(), qrCodeImage.getHeight(), finalX, finalY, qrWidth, qrHeight);

        g2d.drawImage(qrCodeImage, finalX, finalY, qrWidth, qrHeight, null);
    }

    private void saveImageAsPng(BufferedImage image, String outputPath) throws IOException {
        Path path = Paths.get(outputPath);
        Path parentDir = path.getParent();

        if (parentDir != null && !Files.exists(parentDir)) {
            Files.createDirectories(parentDir);
        }

        File outputFile = new File(outputPath);
        ImageIO.write(image, "PNG", outputFile);
    }

    private void setupHighQualityRendering(Graphics2D g2d) {
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
        g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
    }

    private Color parseColor(String colorStr, Color defaultColor) {
        if (colorStr == null || colorStr.isEmpty()) {
            return defaultColor;
        }

        try {
            return Color.decode(colorStr);
        } catch (NumberFormatException e) {
            log.warn("Invalid color format: {}. Using default color.", colorStr);
            return defaultColor;
        }
    }

    // 헬퍼 메소드들 - 안전하게 디자인 옵션에서 값을 추출

    private String getForegroundColor(QrCodeDesignOptionsDto designOptions) {
        if (designOptions != null && designOptions.getDotsOptions() != null &&
                designOptions.getDotsOptions().getColor() != null) {
            return designOptions.getDotsOptions().getColor();
        }
        return DEFAULT_FOREGROUND_COLOR;
    }

    private String getBackgroundColor(QrCodeDesignOptionsDto designOptions) {
        if (designOptions != null && designOptions.getBackgroundOptions() != null &&
                designOptions.getBackgroundOptions().getColor() != null) {
            return designOptions.getBackgroundOptions().getColor();
        }
        return DEFAULT_BACKGROUND_COLOR;
    }

    private String getDotsType(QrCodeDesignOptionsDto designOptions) {
        if (designOptions != null && designOptions.getDotsOptions() != null &&
                designOptions.getDotsOptions().getType() != null) {
            return designOptions.getDotsOptions().getType();
        }
        return "square";
    }

    private String getBackgroundFit(QrCodeDesignOptionsDto designOptions) {
        if (designOptions != null && designOptions.getLayoutOptions() != null &&
                designOptions.getLayoutOptions().getBackgroundImageFit() != null) {
            return designOptions.getLayoutOptions().getBackgroundImageFit();
        }
        return DEFAULT_BG_FIT;
    }

    private double getQrSizeRatio(QrCodeDesignOptionsDto designOptions) {
        if (designOptions != null && designOptions.getLayoutOptions() != null &&
                designOptions.getLayoutOptions().getQrPlacement() != null &&
                designOptions.getLayoutOptions().getQrPlacement().getSizeRatio() != null) {
            return designOptions.getLayoutOptions().getQrPlacement().getSizeRatio();
        }
        return DEFAULT_QR_SIZE_RATIO;
    }

    private double getQrPositionX(QrCodeDesignOptionsDto designOptions) {
        if (designOptions != null && designOptions.getLayoutOptions() != null &&
                designOptions.getLayoutOptions().getQrPlacement() != null &&
                designOptions.getLayoutOptions().getQrPlacement().getPosition() != null &&
                designOptions.getLayoutOptions().getQrPlacement().getPosition().getX() != null) {
            return designOptions.getLayoutOptions().getQrPlacement().getPosition().getX();
        }
        return 0.5; // 기본값은 중앙
    }

    private double getQrPositionY(QrCodeDesignOptionsDto designOptions) {
        if (designOptions != null && designOptions.getLayoutOptions() != null &&
                designOptions.getLayoutOptions().getQrPlacement() != null &&
                designOptions.getLayoutOptions().getQrPlacement().getPosition() != null &&
                designOptions.getLayoutOptions().getQrPlacement().getPosition().getY() != null) {
            return designOptions.getLayoutOptions().getQrPlacement().getPosition().getY();
        }
        return 0.5; // 기본값은 중앙
    }

    private void logLayoutOptions(QrCodeDesignOptionsDto designOptions) {
        // QR 코드 위치 정보
        Double positionX = null;
        Double positionY = null;
        Double sizeRatio = null;

        if (designOptions != null && designOptions.getLayoutOptions() != null &&
                designOptions.getLayoutOptions().getQrPlacement() != null) {

            // 위치 정보 추출
            if (designOptions.getLayoutOptions().getQrPlacement().getPosition() != null) {
                positionX = designOptions.getLayoutOptions().getQrPlacement().getPosition().getX();
                positionY = designOptions.getLayoutOptions().getQrPlacement().getPosition().getY();
            }

            // 크기 비율 추출
            sizeRatio = designOptions.getLayoutOptions().getQrPlacement().getSizeRatio();
        }

        // 로그 출력
        log.info("QR 코드 레이아웃 옵션 정보:");
        log.info("Position X: {} (기본값: 0.5)", positionX != null ? positionX : "설정되지 않음");
        log.info("Position Y: {} (기본값: 0.5)", positionY != null ? positionY : "설정되지 않음");
        log.info("Size Ratio: {} (기본값: {})", sizeRatio != null ? sizeRatio : "설정되지 않음", DEFAULT_QR_SIZE_RATIO);

        // 최종적으로 사용될 값 출력
        log.info("실제 적용될 값 - Position X: {}, Position Y: {}, Size Ratio: {}",
                getQrPositionX(designOptions),
                getQrPositionY(designOptions),
                getQrSizeRatio(designOptions));
    }

    private void markFinderPatternAreas(boolean[][] finderPatterns, int width, int height) {
        int finderSize = 7; // Finder patterns are 7x7 modules

        // Top-left
        markRegion(finderPatterns, 0, 0, finderSize, finderSize, width, height);
        
        // Top-right
        markRegion(finderPatterns, width - finderSize, 0, finderSize, finderSize, width, height);
        
        // Bottom-left
        markRegion(finderPatterns, 0, height - finderSize, finderSize, finderSize, width, height);
    }

    private void markRegion(boolean[][] array, int startX, int startY, int regionWidth, int regionHeight, int matrixWidth, int matrixHeight) {
        for (int y = 0; y < regionHeight; y++) {
            for (int x = 0; x < regionWidth; x++) {
                int posX = startX + x;
                int posY = startY + y;
                // Ensure we are within the bounds of the array (matrixWidth, matrixHeight)
                if (posX >= 0 && posX < matrixWidth && posY >= 0 && posY < matrixHeight) {
                    array[posX][posY] = true;
                }
            }
        }
    }
}
