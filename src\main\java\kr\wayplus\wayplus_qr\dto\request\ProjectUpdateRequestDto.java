package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ProjectUpdateRequestDto {

    @NotBlank(message = "프로젝트 이름은 필수입니다.")
    @Size(max = 100, message = "프로젝트 이름은 최대 100자까지 가능합니다.")
    private String projectName;

    @Size(max = 500, message = "설명은 최대 500자까지 가능합니다.")
    private String description;

    @Size(max = 255, message = "관리자 이메일은 최대 255자까지 가능합니다.")
    private String projectAdminUserEmail;

    // 필요하다면 상태(status) 등 다른 필드도 추가 가능
    // private String status;
}
