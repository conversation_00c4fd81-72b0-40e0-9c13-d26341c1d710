package kr.wayplus.wayplus_qr.util;

import kr.wayplus.wayplus_qr.service.LoggerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Properties;
import java.util.stream.Collectors;

@Component
public class LoggerUtil {
    private final Logger logger = LoggerFactory.getLogger(getClass());
    private final LoggerService loggerService;

    public LoggerUtil(LoggerService loggerService) {
        this.loggerService = loggerService;
    }

    public String getPropertiesAsString(Properties properties) {
        return properties.entrySet().stream().map(e -> e.getKey() + ":" + e.getValue())
                .collect(Collectors.joining(", "));
    }
}


