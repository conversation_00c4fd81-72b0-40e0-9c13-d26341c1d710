package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.mapper.QrCodeMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
@RequiredArgsConstructor
public class QrCodeScanService {

    private final QrCodeMapper qrCodeMapper;

    /**
     * QR 코드 스캔 횟수를 증가시킵니다.
     * 이 메소드는 항상 새로운 트랜잭션에서 실행됩니다.
     * @param qrUuid 스캔 횟수를 증가시킬 QR 코드의 UUID
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void incrementScanCount(String qrUuid) {
        try {
            log.info("Attempting to increment scan count for QR code UUID: {} in a new transaction.", qrUuid);
            int updatedRows = qrCodeMapper.incrementScanCount(qrUuid);
            if (updatedRows > 0) {
                log.info("Successfully incremented scan count for QR code UUID: {}", qrUuid);
            } else {
                // 실패 시 경고 로깅 (DB 제약조건, 동시성 문제 등 가능성 있음)
                log.warn("Scan count increment attempt returned 0 updated rows for QR code UUID: {}. QR code might be inactive, deleted, or already processed in a concurrent request.", qrUuid);
            }
        } catch (Exception e) {
            // 실패 시 에러 로깅. 예외를 다시 던지지 않아 호출부의 트랜잭션에 영향을 주지 않음.
            log.error("Error incrementing scan count for QR code UUID: {} in new transaction.", qrUuid, e);
            // 여기서 예외를 다시 throw하면 REQUIRES_NEW 트랜잭션도 롤백될 수 있으나,
            // 스캔 수 증가는 최대한 유지하는 것이 목적이므로 로깅만 하고 넘어갑니다.
        }
    }
}
