<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.PreRegistrationFormMapper">

  <resultMap id="FormResultMap" type="kr.wayplus.wayplus_qr.entity.PreRegistrationForm">
    <id property="formId" column="form_id"/>
    <result property="projectId" column="project_id"/>
    <result property="formName" column="form_name"/>
    <result property="description" column="description"/>
    <result property="completionMessage" column="completion_message"/>
    <result property="autoConfirmYn" column="auto_confirm_yn"/>
    <result property="privacyPolicyAgreementText" column="privacy_policy_agreement_text"/>
    <result property="requireConsent" column="require_consent"/>
    <result property="createUserEmail" column="create_user_email"/>
    <result property="createDate" column="create_date"/>
    <result property="updateUserEmail" column="update_user_email"/>
    <result property="lastUpdateDate" column="last_update_date"/>
    <result property="deleteUserEmail" column="delete_user_email"/>
    <result property="deleteDate" column="delete_date"/>
    <result property="useYn" column="use_yn"/>
    <result property="deleteYn" column="delete_yn"/>
  </resultMap>

  <select id="selectFormById" resultMap="FormResultMap">
    SELECT * FROM pre_registration_forms
    WHERE form_id = #{formId}
      AND delete_yn = 'N'
  </select>

  <select id="selectFormByEventId" resultMap="FormResultMap">
    SELECT prf.* FROM pre_registration_forms prf
    INNER JOIN events e ON prf.project_id = e.project_id
    WHERE e.event_id = #{eventId}
      AND prf.delete_yn = 'N'
      AND e.delete_yn = 'N'
  </select>

  <select id="selectFormsByProjectId" resultMap="FormResultMap">
    SELECT * FROM pre_registration_forms
    WHERE project_id = #{projectId}
      AND delete_yn = 'N'
      <if test="searchColumn != null and searchColumn != '' and searchKeyword != null and searchKeyword != ''">
        AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
      </if>
    <!-- 동적 정렬 -->
    <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
        ORDER BY
        <foreach item="order" collection="pageable.sort" separator=", ">
            <choose>
                <when test="order.property == 'formName'">form_name</when>
                <when test="order.property == 'createDate'">create_date</when>
                <when test="order.property == 'lastUpdateDate'">last_update_date</when>
                <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                <otherwise>create_date</otherwise> <!-- 기본 정렬 기준 -->
            </choose>
            <choose>
                <when test="order.direction.name() == 'ASC'">ASC</when>
                <when test="order.direction.name() == 'DESC'">DESC</when>
                <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
            </choose>
        </foreach>
    </if>
    <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
        <!-- 기본 정렬: 생성일 내림차순 -->
        ORDER BY create_date DESC
    </if>
    <!-- 페이징 -->
    <if test="pageable != null">
        LIMIT #{pageable.offset}, #{pageable.pageSize}
    </if>
  </select>

  <select id="countFormsByProjectId" parameterType="long" resultType="long">
    SELECT count(*)
    FROM pre_registration_forms
    WHERE project_id = #{projectId}
      AND delete_yn = 'N'
      <if test="searchColumn != null and searchColumn != '' and searchKeyword != null and searchKeyword != ''">
        AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
      </if>
  </select>

  <select id="existsByProjectIdAndFormName" resultType="boolean">
    SELECT EXISTS(
      SELECT 1 FROM pre_registration_forms
      WHERE project_id = #{projectId}
        AND form_name = #{formName}
        AND delete_yn = 'N'
    )
  </select>

  <insert id="insertForm" useGeneratedKeys="true" keyProperty="formId">
    INSERT INTO pre_registration_forms
      (project_id, form_name, description, completion_message, privacy_policy_agreement_text
      , auto_confirm_yn, require_consent, create_user_email, create_date, use_yn, delete_yn)
    VALUES
      (#{projectId}, #{formName}, #{description}, #{completionMessage}, #{privacyPolicyAgreementText}
      , #{autoConfirmYn}, #{requireConsent, typeHandler=kr.wayplus.wayplus_qr.common.typehandler.BooleanYnTypeHandler}, #{createUserEmail}, NOW(), 'Y', 'N')
  </insert>

  <update id="updateForm">
    UPDATE pre_registration_forms
    SET form_name = #{formName},
        description = #{description},
        completion_message = #{completionMessage},
        privacy_policy_agreement_text = #{privacyPolicyAgreementText},
        auto_confirm_yn = #{autoConfirmYn},
        require_consent = #{requireConsent, typeHandler=kr.wayplus.wayplus_qr.common.typehandler.BooleanYnTypeHandler},
        update_user_email = #{updateUserEmail},
        last_update_date = NOW()
    WHERE form_id = #{formId}
      AND delete_yn = 'N'
  </update>

  <update id="deleteFormById">
    UPDATE pre_registration_forms
    SET delete_yn = 'Y',
        delete_user_email = #{userEmail},
        delete_date = NOW()
    WHERE form_id = #{formId}
      AND delete_yn = 'N'
  </update>

  <!-- ==================== SUPER ADMIN Queries ==================== -->

  <select id="selectAllFormsForSuperAdmin" parameterType="map" resultMap="FormResultMap">
      SELECT * 
      FROM pre_registration_forms
      WHERE
          delete_yn = 'N'
      <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
          AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
      </if>
      <!-- Pageable을 사용한 동적 정렬 -->
      <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
          ORDER BY
          <foreach item="order" collection="pageable.sort" separator=", ">
              <choose>
                  <when test="order.property == 'formName'">form_name</when>
                  <when test="order.property == 'description'">description</when>
                  <when test="order.property == 'createDate'">create_date</when>
                  <when test="order.property == 'lastUpdateDate'">last_update_date</when>
                  <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                  <otherwise>create_date</otherwise> <!-- 기본 정렬 기준 -->
              </choose>
              <choose>
                  <when test="order.direction.name() == 'ASC'">ASC</when>
                  <when test="order.direction.name() == 'DESC'">DESC</when>
                  <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
              </choose>
          </foreach>
      </if>
      <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
          ORDER BY create_date DESC
      </if>
      <!-- Pageable을 사용한 페이징 -->
      <if test="pageable != null">
          LIMIT #{pageable.offset}, #{pageable.pageSize}
      </if>
  </select>

  <select id="countAllFormsForSuperAdmin" resultType="long">
      SELECT
          COUNT(*)
      FROM
          pre_registration_forms
      WHERE
          delete_yn = 'N'
      <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
        AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
      </if>
  </select>

  <select id="selectFormsByProjectIdForSuperAdmin" parameterType="map" resultMap="FormResultMap">
      SELECT * 
      FROM pre_registration_forms prt
      WHERE
        project_id = #{projectId}
        AND delete_yn = 'N'
        <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
          AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
        </if>
        <!-- Pageable을 사용한 동적 정렬 -->
        <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
            ORDER BY
            <foreach item="order" collection="pageable.sort" separator=", ">
                <choose>
                    <when test="order.property == 'formName'">form_name</when>
                    <when test="order.property == 'description'">description</when>
                    <when test="order.property == 'createDate'">create_date</when>
                    <when test="order.property == 'lastUpdateDate'">last_update_date</when>
                    <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                    <otherwise>create_date</otherwise> <!-- 기본 정렬 기준 -->
                </choose>
                <choose>
                    <when test="order.direction.name() == 'ASC'">ASC</when>
                    <when test="order.direction.name() == 'DESC'">DESC</when>
                    <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                </choose>
            </foreach>
        </if>
        <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
            ORDER BY create_date DESC
        </if>
        <!-- Pageable을 사용한 페이징 -->
        <if test="pageable != null">
            LIMIT #{pageable.offset}, #{pageable.pageSize}
        </if>
  </select>

  <select id="countFormsByProjectIdForSuperAdmin" parameterType="long" resultType="long">
      SELECT
          COUNT(*)
      FROM
          pre_registration_forms
      WHERE
      project_id = #{projectId}
      AND delete_yn = 'N'
      <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
        AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
      </if>
  </select>

</mapper>
