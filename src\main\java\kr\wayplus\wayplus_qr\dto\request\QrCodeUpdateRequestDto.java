package kr.wayplus.wayplus_qr.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.Size;
import kr.wayplus.wayplus_qr.entity.QrCodeStatus;
import lombok.Builder;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;
import java.time.LocalDateTime;

@Data
@Builder
public class QrCodeUpdateRequestDto {

    // 수정할 QR 코드가 속한 프로젝트 ID
    private Long projectId;

    @Size(max = 100, message = "QR 코드 이름은 100자를 초과할 수 없습니다.")
    private String qrName;

    @Size(max = 50, message = "QR 코드 타입은 50자를 초과할 수 없습니다.")
    private String qrType;

    @Size(max = 2000, message = "대상 콘텐츠는 2000자를 초과할 수 없습니다.")
    private String targetContent;

    @Size(max = 500, message = "설명은 500자를 초과할 수 없습니다.")
    private String description;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime validFromDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime validToDate;

    // QR코드 디자인 옵션 (JSON 문자열)
    private String designOptions;

    private QrCodeStatus status;

    private Long linkedLandingPageId;
    private Long linkedEventId;

    private String installationLocation;
    private String installationLocationLat;
    private String installationLocationLng;
    private String qrInstalledImagePath;

    // logoImageFile 및 qrInstalledImageFile 필드는 Controller에서 MultipartFile로 처리
    private MultipartFile qrInstalledImageFile;
}
