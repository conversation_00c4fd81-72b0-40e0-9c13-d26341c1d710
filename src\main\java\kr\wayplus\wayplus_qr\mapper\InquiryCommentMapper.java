package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.entity.InquiryComment;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Optional;

@Mapper
public interface InquiryCommentMapper {

    /**
     * 문의 댓글 생성
     * 
     * @param comment 생성할 댓글 정보
     * @return 생성된 행 수
     */
    int insertComment(InquiryComment comment);

    /**
     * ID로 댓글 조회
     * 
     * @param commentId 댓글 ID
     * @return 댓글 정보 (Optional)
     */
    Optional<InquiryComment> selectCommentById(Long commentId);

    /**
     * 문의 ID로 댓글 목록 조회
     * 
     * @param inquiryId 문의 ID
     * @return 댓글 목록
     */
    List<InquiryComment> selectCommentsByInquiryId(Long inquiryId);

    /**
     * 댓글 삭제
     * 
     * @param commentId 삭제할 댓글 ID
     * @return 삭제된 행 수
     */
    int deleteComment(Long commentId);

    /**
     * 문의에 속한 모든 댓글 삭제
     * 
     * @param inquiryId 문의 ID
     * @return 삭제된 행 수
     */
    int deleteCommentsByInquiryId(Long inquiryId);
    
    /**
     * 댓글 수정
     * 
     * @param comment 수정할 댓글 정보
     * @return 수정된 행 수
     */
    int updateComment(InquiryComment comment);
}
