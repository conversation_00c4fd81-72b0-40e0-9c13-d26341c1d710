package kr.wayplus.wayplus_qr.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 알림 Entity - notifications 테이블 매핑.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Notification {
    private Long notificationId;
    private String userEmail;
    private Long inquiryId;
    private String message;
    private boolean isRead;
    private NotificationType notificationType;
    private LocalDateTime createdAt;
}
