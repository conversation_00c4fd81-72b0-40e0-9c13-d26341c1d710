<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.ProjectMapper">

    <select id="selectProjectById" parameterType="long" resultType="kr.wayplus.wayplus_qr.entity.Project">
        SELECT
            project_id AS projectId,
            project_name AS projectName,
            description,
            project_admin_user_email AS projectAdminUserEmail,
            status,
            create_user_email AS createUserEmail,
            DATE_FORMAT(create_date, '%Y-%m-%d %H:%i:%s') AS createDate,
            update_user_email AS updateUserEmail,
            DATE_FORMAT(last_update_date, '%Y-%m-%d %H:%i:%s') AS lastUpdateDate
        FROM
            projects
        WHERE
            project_id = #{projectId}
          AND delete_yn = 'N'
    </select>

    <select id="selectAllProjects" resultType="kr.wayplus.wayplus_qr.entity.Project">
        SELECT
            project_id AS projectId,
            project_name AS projectName,
            description,
            project_admin_user_email AS projectAdminUserEmail,
            status,
            create_user_email AS createUserEmail,
            DATE_FORMAT(create_date, '%Y-%m-%d %H:%i:%s') AS createDate,
            update_user_email AS updateUserEmail,
            DATE_FORMAT(last_update_date, '%Y-%m-%d %H:%i:%s') AS lastUpdateDate
        FROM
            projects
        WHERE
            delete_yn = 'N' -- 삭제되지 않은 프로젝트만 조회
        ORDER BY
            create_date DESC -- 최신순으로 정렬 (선택 사항)
    </select>

    <insert id="insertProject" parameterType="kr.wayplus.wayplus_qr.entity.Project" useGeneratedKeys="true" keyProperty="projectId">
        INSERT INTO projects (
            project_name, description, project_admin_user_email,
            status, create_user_email, create_date,
            update_user_email, last_update_date, delete_yn
        ) VALUES (
            #{projectName}, #{description}, #{projectAdminUserEmail},
            'ACTIVE', #{createUserEmail}, NOW(), -- status는 'ACTIVE'로 기본 설정, 생성일시는 DB 시간 사용
            #{createUserEmail}, NOW(), 'N' -- 최초 생성 시 수정자 정보=생성자 정보, 수정일시=생성일시, 삭제여부='N'
        )
    </insert>

    <!-- 프로젝트 업데이트 쿼리 추가 -->
    <update id="updateProject" parameterType="kr.wayplus.wayplus_qr.entity.Project">
        UPDATE projects
        SET
            project_name = #{projectName},
            description = #{description},
            project_admin_user_email = #{projectAdminUserEmail},
            last_update_date = NOW(),
            update_user_email = #{updateUserEmail}
        WHERE project_id = #{projectId}
          AND delete_yn = 'N'
    </update>

    <!-- 프로젝트 삭제 (Soft Delete) -->
    <update id="deleteProjectById" parameterType="kr.wayplus.wayplus_qr.entity.Project">
        UPDATE projects
        SET
            delete_yn = 'Y',
            last_update_date = NOW(),
            update_user_email = #{updateUserEmail}
        WHERE project_id = #{projectId}
          AND delete_yn = 'N'
    </update>


    <!-- 페이징 처리된 프로젝트 목록 조회 -->
    <select id="selectProjectListWithPaging" resultType="kr.wayplus.wayplus_qr.entity.Project">
        SELECT
            project_id, project_name, description, project_admin_user_email,
            create_date, create_user_email, last_update_date, update_user_email, delete_yn
        FROM
            projects
        WHERE
            delete_yn = 'N'
            <if test="status != null and status != ''">
              AND status = #{status}
            </if>
            <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
              AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
            </if>
        <!-- 안전한 동적 정렬 처리 -->
         <if test="pageable.sort != null">
             ORDER BY
             <foreach item="order" index="index" collection="pageable.sort" separator=",">
                 <choose>
                     <when test="order.property == 'projectId'">project_id</when>
                     <when test="order.property == 'projectName'">project_name</when>
                     <when test="order.property == 'createDate'">create_date</when>
                     <!-- 필요한 다른 컬럼에 대한 when 절 추가 -->
                     <otherwise>create_date</otherwise> <!-- 기본 정렬 컬럼 -->
                 </choose>
                 <choose>
                    <when test="order.direction.name() == 'ASC'">ASC</when>
                    <when test="order.direction.name() == 'DESC'">DESC</when>
                    <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                 </choose>
             </foreach>
         </if>
         <!-- 기본 정렬 (pageable.sort가 null일 경우) -->
         <if test="pageable.sort == null">
            ORDER BY create_date DESC
         </if>

         <if test="pageable.offset >= 0 and pageable.pageSize > 0">
             LIMIT #{pageable.pageSize} OFFSET #{pageable.offset}
         </if>
    </select>

    <!-- 전체 프로젝트 개수 조회 (페이징용) -->
    <select id="countAllProjects" resultType="int">
        SELECT COUNT(*)
        FROM projects
        WHERE delete_yn = 'N'
        <if test="status != null and status != ''">
          AND status = #{status}
        </if>
        <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
          AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
        </if>
    </select>

    <!-- countActiveProjectsByAdminEmail -->
    <select id="countActiveProjectsByAdminEmail" resultType="int">
        SELECT COUNT(*)
        FROM projects
        WHERE project_admin_user_email = #{adminEmail}
          AND delete_yn = 'N'
          <if test="excludeProjectId != null and excludeProjectId > 0">
              AND project_id != #{excludeProjectId}
          </if>
    </select>

    <!-- 프로젝트 이름으로 조회 (중복 검사용, 활성 프로젝트 대상) -->
    <select id="selectProjectByName" resultType="kr.wayplus.wayplus_qr.entity.Project">
        SELECT
            project_id, project_name, description, project_admin_user_email,
            create_date, create_user_email, last_update_date, update_user_email, delete_yn
        FROM projects
        WHERE project_name = #{projectName}
          AND delete_yn = 'N'
        LIMIT 1
    </select>

    <!-- 사용자가 특정 프로젝트의 관리자인지 확인 (수정됨) -->
    <!-- 조건 1: 프로젝트의 project_admin_user_email 이거나 -->
    <!-- 조건 2: user_project_mapping 에 매핑되어 있고, 사용자의 role_id가 adminRoleIds 목록에 포함되는 경우 -->
    <select id="isUserAdminOfProject" resultType="boolean">
        SELECT COUNT(*) > 0
        FROM users u
        WHERE u.user_email = #{userEmail}
          AND u.delete_yn = 'N'
          AND (
              EXISTS (
                  SELECT 1
                  FROM projects p
                  WHERE p.project_id = #{projectId}
                    AND p.project_admin_user_email = #{userEmail}
                    AND p.delete_yn = 'N'
              )
              OR
              (
                  u.role_id IN
                  <foreach item="roleId" collection="adminRoleIds" open="(" separator="," close=")">
                      #{roleId}
                  </foreach>
                  AND EXISTS (
                      SELECT 1
                      FROM user_project_mapping upm
                      WHERE upm.project_id = #{projectId}
                        AND upm.user_email = #{userEmail}
                        AND upm.delete_yn = 'N'
                        AND upm.use_yn = 'Y'
                  )
              )
          )
    </select>

    <!-- SUPER_ADMIN 용: 모든 프로젝트 목록 조회 (페이징 및 동적 정렬) -->
    <select id="selectAllProjectsForSuperAdmin" parameterType="map" resultType="kr.wayplus.wayplus_qr.entity.Project">
        SELECT
            project_id, project_name, description, project_admin_user_email,
            create_date, create_user_email, last_update_date, update_user_email,
            delete_yn
        FROM projects
        WHERE delete_yn = 'N'
        <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
          AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
        </if>
        <!-- Pageable을 사용한 동적 정렬 -->
        <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
            ORDER BY
            <foreach item="order" collection="pageable.sort" separator=", ">
                <choose>
                    <when test="order.property == 'projectName'">project_name</when>
                    <when test="order.property == 'description'">description</when>
                    <when test="order.property == 'projectAdminUserEmail'">project_admin_user_email</when>
                    <when test="order.property == 'status'">status</when>
                    <when test="order.property == 'createUserEmail'">create_user_email</when>
                    <when test="order.property == 'createDate'">create_date</when>
                    <when test="order.property == 'lastUpdateDate'">last_update_date</when>
                    <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                    <otherwise>create_date</otherwise> <!-- 기본 정렬 기준 -->
                </choose>
                <choose>
                    <when test="order.direction.name() == 'ASC'">ASC</when>
                    <when test="order.direction.name() == 'DESC'">DESC</when>
                    <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                </choose>
            </foreach>
        </if>
        <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
            <!-- 기본 정렬: 생성일 내림차순 -->
            ORDER BY create_date DESC
        </if>
        <!-- Pageable을 사용한 페이징 -->
        <if test="pageable != null">
            LIMIT #{pageable.offset}, #{pageable.pageSize}
        </if>
    </select>

    <!-- SUPER_ADMIN 용: 모든 프로젝트 총 개수 조회 -->
    <select id="countAllProjectsForSuperAdmin" resultType="long">
        SELECT COUNT(*)
        FROM projects
        WHERE delete_yn = 'N'
        <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
          AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
        </if>
    </select>

</mapper>
