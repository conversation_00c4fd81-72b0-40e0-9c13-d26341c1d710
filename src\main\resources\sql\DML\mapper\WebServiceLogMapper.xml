<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.WebServiceLogMapper">

    <select id="selectTotalRequestsInPeriod" resultType="long">
        SELECT COUNT(*)
        FROM webservice_log
        WHERE request_time BETWEEN #{startDate} AND #{endDate}
          <if test="projectId != null and projectId != 0">
            AND (
              request_params LIKE CONCAT('%projectId=', #{projectId})
              OR
              request_params LIKE CONCAT('%projectId=', #{projectId}, '&amp;%')
            )
          </if>
    </select>

    <select id="selectAdminRequestsInPeriod" resultType="long">
        SELECT COUNT(*)
        FROM webservice_log
        WHERE request_time BETWEEN #{startDate} AND #{endDate}
          AND user_email IS NOT NULL
          <if test="projectId != null and projectId != 0">
            AND (
              request_params LIKE CONCAT('%projectId=', #{projectId})
              OR
              request_params LIKE CONCAT('%projectId=', #{projectId}, '&amp;%')
            )
          </if>
    </select>

    <select id="selectGeneralUserRequestsInPeriod" resultType="long">
        SELECT COUNT(*)
        FROM webservice_log
        WHERE request_time BETWEEN #{startDate} AND #{endDate}
          AND user_email IS NULL
          <if test="projectId != null and projectId != 0">
            AND (
              request_params LIKE CONCAT('%projectId=', #{projectId})
              OR
              request_params LIKE CONCAT('%projectId=', #{projectId}, '&amp;%')
            )
          </if>
    </select>

    <select id="selectUniqueAdminUsersInPeriod" resultType="long">
        SELECT COUNT(DISTINCT user_email)
        FROM webservice_log
        WHERE request_time BETWEEN #{startDate} AND #{endDate}
          AND user_email IS NOT NULL
          <if test="projectId != null and projectId != 0">
            AND (
              request_params LIKE CONCAT('%projectId=', #{projectId})
              OR
              request_params LIKE CONCAT('%projectId=', #{projectId}, '&amp;%')
            )
          </if>
    </select>

    <select id="selectUniqueAnonymousSessionsInPeriod" resultType="long">
        SELECT COUNT(DISTINCT session_id)
        FROM webservice_log
        WHERE request_time BETWEEN #{startDate} AND #{endDate}
          AND user_email IS NULL
          <if test="projectId != null and projectId != 0">
            AND (
              request_params LIKE CONCAT('%projectId=', #{projectId})
              OR
              request_params LIKE CONCAT('%projectId=', #{projectId}, '&amp;%')
            )
          </if>
    </select>

    <select id="selectHourlyUsageInPeriod" resultType="kr.wayplus.wayplus_qr.dto.response.UsageStatisticsResponseDto$HourlyUsageDto">
        SELECT HOUR(request_time) AS hour, COUNT(*) AS requestCount
        FROM webservice_log
        WHERE request_time BETWEEN #{startDate} AND #{endDate}
          <if test="projectId != null and projectId != 0">
            AND (
              request_params LIKE CONCAT('%projectId=', #{projectId})
              OR
              request_params LIKE CONCAT('%projectId=', #{projectId}, '&amp;%')
            )
          </if>
        GROUP BY HOUR(request_time)
        ORDER BY hour ASC
    </select>

    <select id="selectTopUrlsInPeriod" resultType="kr.wayplus.wayplus_qr.dto.response.UsageStatisticsResponseDto$TopUrlDto">
        SELECT request_uri AS url, COUNT(*) AS requestCount
        FROM webservice_log
        WHERE request_time BETWEEN #{startDate} AND #{endDate}
          <if test="projectId != null and projectId != 0">
            AND (
              request_params LIKE CONCAT('%projectId=', #{projectId})
              OR
              request_params LIKE CONCAT('%projectId=', #{projectId}, '&amp;%')
            )
          </if>
        GROUP BY request_uri
        ORDER BY requestCount DESC
        LIMIT #{limit}
    </select>

    <select id="selectTopAdminUserActivityInPeriod" resultType="kr.wayplus.wayplus_qr.dto.response.UsageStatisticsResponseDto$TopUserActivityDto">
        SELECT user_email AS userIdentifier, COUNT(*) AS requestCount
        FROM webservice_log
        WHERE request_time BETWEEN #{startDate} AND #{endDate}
          AND user_email IS NOT NULL
          <if test="projectId != null and projectId != 0">
            AND (
              request_params LIKE CONCAT('%projectId=', #{projectId})
              OR
              request_params LIKE CONCAT('%projectId=', #{projectId}, '&amp;%')
            )
          </if>
        GROUP BY user_email
        ORDER BY requestCount DESC
        LIMIT #{limit}
    </select>

    <select id="selectTopAnonymousUserActivityInPeriod" resultType="kr.wayplus.wayplus_qr.dto.response.UsageStatisticsResponseDto$TopUserActivityDto">
        SELECT CONCAT('ANONYMOUS_SESSION:', session_id) AS userIdentifier, COUNT(*) AS requestCount
        FROM webservice_log
        WHERE request_time BETWEEN #{startDate} AND #{endDate}
          AND user_email IS NULL
          <if test="projectId != null and projectId != 0">
            AND (
              request_params LIKE CONCAT('%projectId=', #{projectId})
              OR
              request_params LIKE CONCAT('%projectId=', #{projectId}, '&amp;%')
            )
          </if>
        GROUP BY session_id
        ORDER BY requestCount DESC
        LIMIT #{limit}
    </select>


    <select id="selectTopReferersInPeriod" resultType="kr.wayplus.wayplus_qr.dto.response.UsageStatisticsResponseDto$TopRefererDto">
        SELECT 
            COALESCE(referer, 'DIRECT_ENTRY') AS referer,  <!-- 'DIRECT_ENTRY' 또는 원하시는 다른 문자열로 변경 가능 -->
            COUNT(*) AS count
        FROM webservice_log
        WHERE request_time BETWEEN #{startDate} AND #{endDate}
          <if test="projectId != null and projectId != 0">
            AND (
              request_params LIKE CONCAT('%projectId=', #{projectId})
              OR
              request_params LIKE CONCAT('%projectId=', #{projectId}, '&amp;%')
            )
          </if>
        GROUP BY referer  <!-- COALESCE를 사용했으므로, GROUP BY 절은 그대로 referer를 사용해도 됩니다. -->
        ORDER BY count DESC
        LIMIT #{limit}
    </select>

    <select id="selectTopUserAgentsInPeriod" resultType="kr.wayplus.wayplus_qr.dto.response.UsageStatisticsResponseDto$TopUserAgentDto">
        SELECT request_agent AS userAgent, COUNT(*) AS count
        FROM webservice_log
        WHERE request_time BETWEEN #{startDate} AND #{endDate}
          AND request_agent IS NOT NULL AND request_agent != ''
          <if test="projectId != null and projectId != 0">
            AND (
              request_params LIKE CONCAT('%projectId=', #{projectId})
              OR
              request_params LIKE CONCAT('%projectId=', #{projectId}, '&amp;%')
            )
          </if>
        GROUP BY request_agent
        ORDER BY count DESC
        LIMIT #{limit}
    </select>

</mapper>
