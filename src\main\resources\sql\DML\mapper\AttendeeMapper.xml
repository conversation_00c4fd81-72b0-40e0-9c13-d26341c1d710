<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.AttendeeMapper">
 <resultMap id="AttendeeSummaryResultMap" type="kr.wayplus.wayplus_qr.dto.response.AttendeeSummaryDto">
    <id property="attendeeId" column="attendee_id"/>
    <result property="formId" column="form_id"/> 
    <result property="attendeeName" column="attendee_name"/>
    <result property="teamName" column="team_name"/>
    <result property="attendeeContact" column="attendee_contact"/>
    <result property="attendeeEmail" column="attendee_email"/>
    <result property="registrationDate" column="registration_date"/>
    <result property="attendedYn" column="attended_yn"/>
    <result property="attendedConfirmYn" column="attended_confirm_yn"/>
    <result property="submissionDataJson" column="submission_data"/>
    <result property="confirmationCode" column="confirmation_code"/>
    <result property="eventId" column="event_id"/>
    <result property="eventName" column="event_name"/>
    <result property="eventStartDate" column="start_date"/> 
    <result property="eventEndDate" column="end_date"/> 
  </resultMap>

  <insert id="insertAttendee" parameterType="kr.wayplus.wayplus_qr.entity.Attendee" useGeneratedKeys="true" keyProperty="attendeeId">
    INSERT INTO attendees (
            event_id, team_id, form_id, submission_data,
            attendee_name, attendee_contact, attendee_email,
            attended_yn, attended_confirm_yn, confirmation_code, 
            registration_date, registered_by_admin_user_email,
            create_user_email, create_date,
            update_user_email, last_update_date,
            delete_user_email, delete_date,
            use_yn, delete_yn)
    VALUES (
            #{eventId}, #{teamId}, #{formId}, #{submissionData},
            #{attendeeName}, #{attendeeContact}, #{attendeeEmail},
            #{attendedYn}, #{attendedConfirmYn}, #{confirmationCode}, 
            NOW(), NULL,
            #{createUserEmail}, NOW(),
            #{updateUserEmail}, NOW(),
            NULL, NULL,
            'Y', 'N')
  </insert>
  <!-- 이벤트별 현재 신청자 수 조회 -->
  <select id="countAttendeesByEventId" parameterType="long" resultType="java.lang.Long">
    SELECT COUNT(*)
      FROM attendees
     WHERE event_id = #{eventId}
       AND delete_yn = 'N'
  </select>

  <!-- 팀별 현재 참석자 수 조회 -->
  <select id="countAttendeesByTeamId" parameterType="long" resultType="java.lang.Long">
    SELECT COUNT(*)
      FROM attendees
     WHERE team_id = #{teamId}
       AND delete_yn = 'N'
  </select>

  <select id="selectAttendeesByEventId" resultMap="AttendeeSummaryResultMap">
    SELECT
      atd.attendee_id,
      atd.form_id,
      atd.attendee_name,
      t.team_name,
      atd.attendee_contact,
      atd.attendee_email,
      atd.registration_date,
      atd.attended_yn,
      atd.attended_confirm_yn,
      atd.submission_data
    FROM
      attendees atd
    LEFT JOIN teams t ON atd.team_id = t.team_id
    WHERE
      atd.event_id = #{eventId}
      AND atd.delete_yn = 'N'
    ORDER BY
      atd.registration_date DESC
    LIMIT #{pageable.pageSize} OFFSET #{pageable.offset}
  </select>

  <!-- 프로젝트별 참석자 목록 조회 (페이지네이션) -->
  <select id="selectAttendeesByProjectId" resultMap="AttendeeSummaryResultMap">
    SELECT
      a.attendee_id,
      a.form_id,
      a.attendee_name,
      a.attendee_contact,
      a.attendee_email,
      a.registration_date,
      a.attended_yn,
      a.attended_confirm_yn,
      a.submission_data,
      a.confirmation_code,
      e.event_id,
      e.event_name,
      e.start_date,
      e.end_date,
      t.team_name
    FROM
      attendees a
    JOIN
      events e ON a.event_id = e.event_id
    LEFT JOIN
      teams t ON a.team_id = t.team_id
    WHERE
      e.project_id = #{projectId}
      AND a.delete_yn = 'N'
      <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
         <choose>
           <when test="searchColumn == 'attendee_name'">
           AND a.attendee_name LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'attendee_contact'">
           AND a.attendee_contact LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'attendee_email'">
           AND a.attendee_email LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'team_name'">
           AND t.team_name LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'create_date'">
           AND a.create_date LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
         </choose>
      </if>
      <!-- 동적 정렬 -->
      <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
          ORDER BY
          <foreach item="order" collection="pageable.sort" separator=", ">
              <choose>
                  <when test="order.property == 'attendeeName'">attendee_name</when>
                  <when test="order.property == 'attendeeContact'">attendee_contact</when>
                  <when test="order.property == 'attendeeEmail'">attendee_email</when>
                  <when test="order.property == 'teamName'">t.team_name</when>
                  <when test="order.property == 'createDate'">a.create_date</when>
                  <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                  <otherwise>a.create_date</otherwise> <!-- 기본 정렬 기준 -->
              </choose>
              <choose>
                  <when test="order.direction.name() == 'ASC'">ASC</when>
                  <when test="order.direction.name() == 'DESC'">DESC</when>
                  <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
              </choose>
          </foreach>
      </if>
      <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
          <!-- 기본 정렬: 생성일 내림차순 -->
          ORDER BY a.create_date DESC
      </if>
      <!-- 페이징 -->
      <if test="pageable != null">
          LIMIT #{pageable.offset}, #{pageable.pageSize}
      </if>
  </select>

  <!-- 프로젝트별 전체 참석자 수 조회 -->
  <select id="countAttendeesByProjectId" parameterType="long" resultType="long">
    SELECT COUNT(*)
    FROM attendees a
    JOIN events e ON a.event_id = e.event_id
    LEFT JOIN teams t ON a.team_id = t.team_id
    WHERE e.project_id = #{projectId}
      AND a.delete_yn = 'N'
      <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
         <choose>
           <when test="searchColumn == 'attendee_name'">
           AND a.attendee_name LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'attendee_contact'">
           AND a.attendee_contact LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'attendee_email'">
           AND a.attendee_email LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'team_name'">
           AND t.team_name LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
           <when test="searchColumn == 'create_date'">
           AND a.create_date LIKE CONCAT('%', #{searchKeyword}, '%')
           </when>
         </choose>
      </if>
  </select>

  <!-- 참석자 ID로 상세 정보 조회 -->
  <select id="selectAttendeeById" parameterType="long" resultType="kr.wayplus.wayplus_qr.entity.Attendee">
    SELECT *
    FROM attendees
    WHERE attendee_id = #{attendeeId}
      AND delete_yn = 'N'
  </select>

  <!-- 참석 확인 코드로 상세 정보 조회 -->
  <select id="selectAttendeeByConfirmationCode" parameterType="string" resultMap="AttendeeSummaryResultMap">
    SELECT
      a.attendee_id,
      a.form_id,
      a.attendee_name,
      a.attendee_contact,
      a.attendee_email,
      a.registration_date,
      a.attended_yn,
      a.attended_confirm_yn,
      a.submission_data,
      a.confirmation_code,
      e.event_id,
      e.event_name,
      e.start_date,
      e.end_date
    FROM attendees a
    JOIN events e ON a.event_id = e.event_id
    WHERE a.confirmation_code = #{confirmationCode}
      AND a.delete_yn = 'N'
  </select>

  <!-- 참석자 ID로 삭제 -->
  <delete id="deleteAttendeeById" parameterType="long">
    DELETE FROM attendees
    WHERE attendee_id = #{attendeeId}
  </delete>

  <!-- 참석 상태 업데이트 -->
  <update id="updateAttendedConfirmYn">
    UPDATE attendees
       SET attended_confirm_yn = #{attendedConfirmYn, jdbcType=VARCHAR},
           last_update_date = NOW()
           -- TODO: 업데이트한 사용자 정보(update_user_email)도 필요 시 추가
     WHERE attendee_id = #{attendeeId}
  </update>

  <!-- 참석 상태 및 시간 업데이트 (ID 기준) -->
  <update id="updateAttendanceStatus">
    UPDATE attendees
    SET
        attended_yn = #{attendedYn},
        attended_date = #{attendedDate}
    WHERE attendee_id = #{attendeeId}
  </update>

  <!-- 참석 상태 및 시간 업데이트 (Confirmation Code 기준) -->
  <update id="updateAttendedConfirmYnAndDateByCode">
    UPDATE attendees
    SET
        attended_yn = #{attendedYn},
        attended_date = #{attendedDate}
    WHERE confirmation_code = #{confirmationCode}
  </update>

  <!-- 여러 참가자의 attended_confirm_yn 상태 업데이트 -->
  <update id="updateAttendeesConfirmStatus">
    UPDATE attendees
    SET
        attended_confirm_yn = #{attendedConfirmYn}
    WHERE attendee_id IN
    <foreach item="id" collection="attendeeIds" open="(" separator="," close=")">
        #{id}
    </foreach>
  </update>

  <!-- 수동 체크를 위한 참가자 검색 -->
  <select id="selectAttendeesForManualCheck" resultType="kr.wayplus.wayplus_qr.dto.AttendeeManualCheckDto">
      SELECT
          a.attendee_id,
          a.attendee_name,
          a.attendee_contact,
          a.attendee_email,
          a.attended_yn,
          a.attended_confirm_yn,
          a.registration_date
      FROM attendees a
      WHERE a.event_id = #{eventId}
      AND a.delete_yn = 'N'
      <if test="name != null and name != ''">
          AND a.attendee_name LIKE CONCAT('%', #{name}, '%')
      </if>
      ORDER BY a.attendee_name ASC, a.registration_date DESC
      <!-- 필요시 페이지네이션 추가 -->
  </select>

  <!-- 참석자 논리적 삭제 -->
  <update id="markAttendeeAsDeleted" parameterType="map">
    UPDATE attendees
    SET
        delete_yn = 'Y',
        delete_date = NOW(),
        delete_user_email = #{adminEmail}
    WHERE attendee_id = #{attendeeId}
      AND delete_yn = 'N'
  </update>

    <!-- ********** 전체 참가자 수 조회 추가 ********** -->
    <select id="selectTotalAttendeeCount" resultType="long">
        SELECT COUNT(*) FROM attendees
    </select>

    <!-- 기간별 일일 참가자 등록 수 조회 -->
    <select id="selectDailyAttendeeRegistrationCounts" resultType="kr.wayplus.wayplus_qr.dto.response.DailyCountDto">
        SELECT
            DATE_FORMAT(create_date, '%Y-%m-%d') AS date,
            COUNT(*) AS count
        FROM
            attendees
        WHERE
            <![CDATA[
            DATE_FORMAT(create_date, '%Y-%m-%d') >= STR_TO_DATE(#{startDate}, '%Y-%m-%d')
          AND DATE_FORMAT(create_date, '%Y-%m-%d') <= STR_TO_DATE(#{endDate}, '%Y-%m-%d')
            ]]>
        GROUP BY
            DATE_FORMAT(create_date, '%Y-%m-%d')
        ORDER BY
            date
    </select>

    <!-- 기간별 일일 참가자 등록 수 (프로젝트별) 조회 -->
    <select id="selectDailyAttendeeRegistrationCountsByProject" resultType="kr.wayplus.wayplus_qr.dto.response.DailyCountDto">
        SELECT
            DATE_FORMAT(a.registration_date, '%Y-%m-%d') AS date,
            COUNT(*) AS count
        FROM
            attendees a
        JOIN events e ON a.event_id = e.event_id
        WHERE
            e.project_id = #{projectId}
            <![CDATA[
            AND DATE_FORMAT(a.registration_date, '%Y-%m-%d') >= STR_TO_DATE(#{startDate}, '%Y-%m-%d')
            AND DATE_FORMAT(a.registration_date, '%Y-%m-%d') <= STR_TO_DATE(#{endDate}, '%Y-%m-%d')
            ]]>
        GROUP BY
            DATE_FORMAT(a.registration_date, '%Y-%m-%d')
        ORDER BY
            date
    </select>

    <select id="selectAttendeesForSuperAdmin" parameterType="map" resultMap="AttendeeSummaryResultMap">
        SELECT
          a.attendee_id, a.form_id, a.attendee_name, t.team_name, a.attendee_contact, a.attendee_email,
          a.registration_date, a.attended_yn, a.attended_confirm_yn, a.submission_data,
          a.confirmation_code, ev.event_id, ev.event_name, ev.start_date, ev.end_date, 
          p.project_id, p.project_name, t.team_name
        FROM
          attendees a
        JOIN
          events ev ON a.event_id = ev.event_id
        JOIN
          projects p ON ev.project_id = p.project_id
        LEFT JOIN
          teams t ON a.team_id = t.team_id
        WHERE a.delete_yn = 'N'  
        <if test="projectId != null">
          AND ev.project_id = #{projectId}
        </if>
        <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
          AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
        </if>
        <!-- Pageable을 사용한 동적 정렬 -->
        <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
            ORDER BY
            <foreach item="order" collection="pageable.sort" separator=", ">
                <choose>
                    <when test="order.property == 'attendeeName'">a.attendee_name</when>
                    <when test="order.property == 'attendeeEmail'">a.attendee_email</when>
                    <when test="order.property == 'attendeeContact'">a.attendee_contact</when>
                    <when test="order.property == 'teamName'">t.team_name</when>
                    <when test="order.property == 'createDate'">a.create_date</when>
                    <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                    <otherwise>a.create_date</otherwise> <!-- 기본 정렬 기준 -->
                </choose>
                <choose>
                    <when test="order.direction.name() == 'ASC'">ASC</when>
                    <when test="order.direction.name() == 'DESC'">DESC</when>
                    <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                </choose>
            </foreach>
        </if>
        <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
            <!-- 기본 정렬: 생성일 내림차순 -->
            ORDER BY a.create_date DESC
        </if>
        <!-- Pageable을 사용한 페이징 -->
        <if test="pageable != null">
            LIMIT #{pageable.offset}, #{pageable.pageSize}
        </if>
      </select>

      <select id="countAttendeesForSuperAdmin" parameterType="map" resultType="long">
        SELECT
          COUNT(DISTINCT a.attendee_id)
        FROM
          attendees a
        JOIN
          events ev ON a.event_id = ev.event_id
        JOIN
          projects p ON ev.project_id = p.project_id
        LEFT JOIN
          teams t ON a.team_id = t.team_id
        WHERE a.delete_yn = 'N'  
        <if test="projectId != null">
          AND ev.project_id = #{projectId}
        </if>
        <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
          AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
        </if>
      </select>

      <!-- 기존 getDailyAttendeeCountsByEventId 쿼리 -->
      <select id="getDailyAttendeeCountsByEventId" resultType="kr.wayplus.wayplus_qr.dto.response.DailyCountDto">
          SELECT DATE(registration_date) AS date, COUNT(*) AS count
          FROM attendees
          WHERE event_id = #{eventId} AND delete_yn = 'N'
          GROUP BY DATE(registration_date)
          ORDER BY date ASC
      </select>

      <!-- confirmationCode 로 Attendee 엔티티 단건 조회 (혜택 사용 등) -->
      <select id="selectAttendeeByConfirmationCodeSimple" resultType="kr.wayplus.wayplus_qr.entity.Attendee">
        SELECT a.*,
              e.event_name   AS event_name,
              e.start_date   AS start_date
        FROM attendees a
        INNER JOIN events e
          ON a.event_id = e.event_id
        WHERE a.confirmation_code = #{confirmationCode}
          AND a.delete_yn = 'N'
        LIMIT 1
      </select>
</mapper>
