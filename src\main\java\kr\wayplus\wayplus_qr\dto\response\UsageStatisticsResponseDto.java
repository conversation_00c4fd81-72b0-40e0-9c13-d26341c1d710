package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UsageStatisticsResponseDto {

    private String periodStartDate;
    private String periodEndDate;
    private long totalRequests;
    private long adminRequests;
    private long generalUserRequests;
    private long uniqueAdminUsers;
    private long uniqueAnonymousSessions;
    private List<HourlyUsageDto> hourlyUsage;
    private List<TopUrlDto> topUrls;
    private List<TopUserActivityDto> topUserActivity;
    private Map<String, Long> requestsByStatusFamily;
    private List<TopRefererDto> topReferers;
    private List<TopUserAgentDto> topUserAgents;

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HourlyUsageDto {
        private int hour;
        private long requestCount;
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TopUrlDto {
        private String url;
        private long requestCount;
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TopUserActivityDto {
        private String userIdentifier;
        private long requestCount;
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TopRefererDto {
        private String referer;
        private long count;
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TopUserAgentDto {
        private String userAgent;
        private long count;
    }
}
