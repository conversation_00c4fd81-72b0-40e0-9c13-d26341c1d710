package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.entity.QrCode;
import kr.wayplus.wayplus_qr.dto.response.TotalQrStatusDto;
import kr.wayplus.wayplus_qr.dto.response.DailyCountDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.time.LocalDate;

@Mapper
public interface QrCodeMapper {

    Optional<QrCode> selectQrCodeById(@Param("qrCodeId") Long qrCodeId);

    String selectQrUuidById(@Param("qrCodeId") Long qrCodeId);

    Optional<QrCode> selectQrCodeByUuid(@Param("qrUuid") String qrUuid);

    List<QrCode> selectQrCodesByProjectId(@Param("projectId") Long projectId);

    // 페이징 및 정렬 적용된 QR 코드 목록 조회
    List<QrCode> selectQrCodesByProjectIdWithPaging(
        @Param("projectId") Long projectId,
        @Param("status") String status,
        @Param("searchColumn") String searchColumn,
        @Param("searchKeyword") String searchKeyword,
        @Param("pageable") Pageable pageable
    );

    // 프로젝트 ID로 QR 코드 개수 조회
    long countQrCodesByProjectId(
        @Param("projectId") Long projectId,
        @Param("status") String status,
        @Param("searchColumn") String searchColumn,
        @Param("searchKeyword") String searchKeyword
    );

    boolean existsByProjectIdAndQrName(@Param("projectId") Long projectId, @Param("qrName") String qrName);

    int insertQrCode(QrCode qrCode);

    // QR 코드 정보 업데이트 (전체 필드)
    int updateQrCode(QrCode qrCode);

    // QR 코드 이미지 경로 업데이트
    int updateQrCodeImagePath(@Param("qrCodeId") Long qrCodeId, @Param("imagePath") String imagePath);

    /**
     * 지정된 QR 코드 ID에 해당하는 레코드의 QR 이미지 경로를 업데이트합니다.
     *
     * @param qrCodeId 업데이트할 QR 코드의 ID
     * @param qrImagePath 저장할 QR 이미지 파일 경로
     * @return 업데이트된 레코드 수 (일반적으로 1)
     */
    int updateQrImagePath(@Param("qrCodeId") Long qrCodeId, @Param("qrImagePath") String qrImagePath);

    QrCode findQrCodeByShortCode(String shortCode);

    // QR 코드 논리적 삭제
    int deleteQrCodeById(@Param("qrCodeId") Long qrCodeId, @Param("deleteUserEmail") String userEmail);

    // 여러 QR 코드 ID를 한번에 논리적으로 삭제
    int deleteQrCodesByIds(@Param("qrCodeIds") List<Long> qrCodeIds, @Param("deleteUserEmail") String userEmail);

    int incrementScanCount(Long qrCodeId);

    // 특정 QR 코드 삭제 (논리적 삭제)
    int deleteQrCodeLogically(@Param("qrUuid") String qrUuid, @Param("deleteUserEmail") String userEmail);

    // 스캔 카운트 증가 (UUID 기반)
    int incrementScanCount(@Param("qrUuid") String qrUuid);

    // 이벤트 ID로 참조용 QR 코드 조회
    Optional<QrCode> selectQrCodeByLinkedEventId(@Param("eventId") Long eventId);

    /**
     * target_content (e.g., confirmationCode for attendance QR)를 사용하여 QR 코드를 조회합니다.
     * @param targetContent 찾고자 하는 QR 코드의 내용
     * @return Optional<QrCode>
     */
    Optional<QrCode> selectQrCodeByTargetContent(@Param("targetContent") String targetContent);

    void updateQrCodePaths(@Param("qrCodeId") Long qrCodeId, @Param("svgPath") String svgPath, @Param("logoPath") String logoPath, @Param("installedImagePath") String installedImagePath);

    void updateQrCodeScanCount(@Param("qrCodeId") Long qrCodeId);

    // 통계 관련 메서드
    TotalQrStatusDto selectTotalQrStatus(@Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 특정 프로젝트의 QR 코드 총 개수 및 총 스캔 수 조회
     */
    TotalQrStatusDto selectProjectTotalQrStatus(@Param("projectId") Long projectId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 지정된 기간 동안 일별 QR 코드 생성 수를 조회합니다.
     *
     * @param startDate 시작일
     * @param endDate 종료일
     * @return 일별 생성 수를 담은 DailyCountDto 리스트
     */
    List<DailyCountDto> selectDailyCreatedCounts(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 지정된 기간 동안 일별 QR 코드 스캔 수를 조회합니다.
     *
     * @param startDate 시작일
     * @param endDate 종료일
     * @return 일별 스캔 수를 담은 DailyCountDto 리스트
     */
    List<DailyCountDto> selectDailyScannedCounts(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 지정된 프로젝트의 기간 동안 일별 QR 코드 생성 수를 조회합니다.
     */
    List<DailyCountDto> selectDailyCreatedCountsByProject(@Param("projectId") Long projectId,
                                                          @Param("startDate") String startDate,
                                                          @Param("endDate") String endDate);

    /**
     * 지정된 프로젝트의 기간 동안 일별 QR 코드 스캔 수를 조회합니다.
     */
    List<DailyCountDto> selectDailyScannedCountsByProject(@Param("projectId") Long projectId,
                                                          @Param("startDate") String startDate,
                                                          @Param("endDate") String endDate);

    /**
     * 모든 QR 코드 목록 조회 (페이징)
     *
     * @param pageable 페이징 정보
     * @param searchColumn 검색 컬럼
     * @param searchKeyword 검색어
     * @return 모든 QR 코드 목록 (페이징 처리됨)
     */
    List<QrCode> selectAllQrCodesWithPaging(
        @Param("searchColumn") String searchColumn,
        @Param("searchKeyword") String searchKeyword,
        @Param("pageable") Pageable pageable);

    /**
     * 모든 QR 코드 총 개수 조회
     *
     * @return 모든 QR 코드의 총 개수
     */
    long countAllQrCodes(
        @Param("searchColumn") String searchColumn,
        @Param("searchKeyword") String searchKeyword);

    /**
     * QR 코드 타입별 분포 조회
     * @param projectId 프로젝트 ID (선택적)
     * @param startDate 시작일
     * @param endDate 종료일
     * @return 타입별 QR 코드 수를 담은 Map 리스트
     */
    List<Map<String, Object>> selectQrTypeDistribution(@Param("projectId") Long projectId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * QR 코드 상태별 분포 조회
     * @param projectId 프로젝트 ID (선택적)
     * @param startDate 시작일
     * @param endDate 종료일
     * @return 상태별 QR 코드 수를 담은 Map 리스트
     */
    List<Map<String, Object>> selectQrStatusDistribution(@Param("projectId") Long projectId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);

    /**
     * 기기별 QR 코드 스캔 통계 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return 기기별 스캔 수를 담은 Map 리스트
     */
    List<Map<String, Object>> selectQrScanDeviceStats(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);

    /**
     * 브라우저별 QR 코드 스캔 통계 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return 브라우저별 스캔 수를 담은 Map 리스트
     */
    List<Map<String, Object>> selectQrScanBrowserStats(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);

    /**
     * OS별 QR 코드 스캔 통계 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return OS별 스캔 수를 담은 Map 리스트
     */
    List<Map<String, Object>> selectQrScanOsStats(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);

    /**
     * 시간대별 QR 코드 스캔 분포 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return 시간대별 스캔 수를 담은 Map 리스트
     */
    List<Map<String, Object>> selectQrScanHourlyDistribution(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);

    /**
     * 요일별 QR 코드 스캔 분포 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return 요일별 스캔 수를 담은 Map 리스트
     */
    List<Map<String, Object>> selectQrScanWeekdayDistribution(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);

    /**
     * 교환권 QR 코드 통계 조회
     * @param projectId 프로젝트 ID (선택적)
     * @return 교환권 QR 코드 통계를 담은 Map
     */
    Map<String, Object> selectExchangeQrStats(@Param("projectId") Long projectId, @Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 교환권 QR 코드 일별 승인 횟수 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return 일별 승인 횟수를 담은 DailyCountDto 리스트
     */
    List<DailyCountDto> selectDailyExchangeApprovalCounts(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);

    /**
     * 승인자별 교환권 QR 코드 승인 횟수 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return 승인자별 승인 횟수를 담은 Map 리스트
     */
    List<Map<String, Object>> selectApproverExchangeCounts(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);

    /**
     * 시간대별 교환권 QR 코드 승인 횟수 조회
     * @param startDate 시작일
     * @param endDate 종료일
     * @param projectId 프로젝트 ID (선택적)
     * @return 시간대별 승인 횟수를 담은 Map 리스트
     */
    List<Map<String, Object>> selectHourlyExchangeApprovalCounts(
            @Param("startDate") String startDate,
            @Param("endDate") String endDate,
            @Param("projectId") Long projectId);
            
    /**
     * 한번이라도 스캔된 QR 코드 목록 조회 (최대 5개, 삭제된 것 제외)
     * @param projectId 프로젝트 ID (선택적)
     * @param startDate 시작일
     * @param endDate 종료일
     * @return 스캔된 QR 코드 정보를 담은 Map 리스트
     */
    List<Map<String, Object>> selectScannedQrCodes(
            @Param("projectId") Long projectId, @Param("startDate") LocalDate startDate, @Param("endDate") LocalDate endDate);
            
    /**
     * 동일한 project_id와 qr_name을 가지면서 이미 삭제된(delete_yn='Y') QR 코드를 찾는 메소드
     * @param projectId 프로젝트 ID
     * @param qrName QR 코드 이름
     * @param currentQrCodeId 현재 삭제하려는 QR 코드 ID (제외할 ID)
     * @return 충돌하는 QR 코드 정보
     */
    QrCode findConflictingSoftDeletedQrCode(
            @Param("projectId") Long projectId, 
            @Param("qrName") String qrName, 
            @Param("currentQrCodeId") Long currentQrCodeId);

    /**
     * 충돌하는 QR 코드의 qr_name을 고유하게 변경하는 메소드
     * @param qrCodeId 수정할 QR 코드 ID
     * @param newQrName 새로운 QR 이름
     * @return 업데이트된 레코드 수
     */
    int updateQrNameWithSuffix(
            @Param("qrCodeId") Long qrCodeId, 
            @Param("newQrName") String newQrName);
}
