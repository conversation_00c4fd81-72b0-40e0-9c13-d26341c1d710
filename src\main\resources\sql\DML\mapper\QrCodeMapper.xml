<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.QrCodeMapper">

    <resultMap id="qrCodeResultMap" type="kr.wayplus.wayplus_qr.entity.QrCode">
        <id property="qrCodeId" column="qr_code_id"/>
        <result property="qrUuid" column="qr_uuid"/>
        <result property="projectId" column="project_id"/>
        <result property="qrName" column="qr_name"/>
        <result property="qrType" column="qr_type"/>
        <result property="status" column="status"/>
        <result property="targetContent" column="target_content"/>
        <result property="description" column="description"/>
        <result property="linkedLandingPageId" column="linked_landing_page_id"/>
        <result property="linkedEventId" column="linked_event_id"/>
        <result property="designOptions" column="design_options"/>
        <result property="installationLocation" column="installation_location"/>
        <result property="installationLocationLat" column="installation_location_lat"/>
        <result property="installationLocationLng" column="installation_location_lng"/>
        <result property="imagePath" column="qr_image_path"/>
        <result property="qrInstalledImagePath" column="qr_installed_image_path"/>
        <result property="scanCount" column="scan_count"/>
        <result property="validFromDate" column="valid_from_date"/>
        <result property="validToDate" column="valid_to_date"/>
        <result property="createUserEmail" column="create_user_email"/>
        <result property="createDate" column="create_date"/>
        <result property="updateUser" column="update_user_email"/>
        <result property="updateDate" column="last_update_date"/>
        <result property="deleteUserEmail" column="delete_user_email"/>
        <result property="deleteDate" column="delete_date"/>
        <result property="useYn" column="use_yn"/>
        <result property="deleteYn" column="delete_yn"/>
        <result property="projectName" column="project_name"/>
    </resultMap>

    <select id="selectQrCodeById" resultMap="qrCodeResultMap">
        SELECT
            q.qr_code_id, q.project_id, q.qr_name, q.qr_type, q.status, q.target_content,
            q.description, q.linked_landing_page_id, q.linked_event_id, q.design_options,
            q.installation_location, q.installation_location_lat, q.installation_location_lng,
            q.qr_image_path, q.qr_installed_image_path, q.scan_count, q.valid_from_date, q.valid_to_date, q.create_user_email,
            q.create_date, q.update_user_email, q.last_update_date, q.delete_user_email,
            q.delete_date, q.use_yn, q.delete_yn,
            q.qr_uuid,
            p.project_name
        FROM
            qr_codes q
        JOIN
            projects p ON q.project_id = p.project_id
        WHERE
            q.qr_code_id = #{qrCodeId}
            AND q.delete_yn = 'N'
            AND q.use_yn = 'Y'
    </select>

    <!-- ID로 QR UUID 조회 -->
    <select id="selectQrUuidById" resultType="string" parameterType="long">
        SELECT qr_uuid
        FROM qr_codes
        WHERE qr_code_id = #{qrCodeId}
          AND delete_yn = 'N'
    </select>

    <select id="selectQrCodesByProjectId" resultMap="qrCodeResultMap">
        SELECT
            q.qr_code_id, q.project_id, q.qr_name, q.qr_type, q.status, q.target_content,
            q.description, q.linked_landing_page_id, q.linked_event_id, q.design_options,
            q.qr_image_path, q.scan_count, q.valid_from_date, q.valid_to_date, q.create_user_email,
            q.create_date, q.update_user_email, q.last_update_date, q.delete_user_email,
            q.delete_date, q.use_yn, q.delete_yn,
            q.qr_uuid,
            p.project_name
        FROM
            qr_codes q
        JOIN
            projects p ON q.project_id = p.project_id
        WHERE
            q.project_id = #{projectId}
            AND q.delete_yn = 'N'
            AND q.use_yn = 'Y'
        ORDER BY
            q.create_date DESC
    </select>

    <!-- 스캔 카운트 증가 -->
    <update id="incrementScanCount" parameterType="string">
        UPDATE qr_codes
        SET scan_count = scan_count + 1
        WHERE qr_uuid = #{qrUuid}
          AND delete_yn = 'N'
          AND status = 'ACTIVE'
    </update>

    <select id="existsByProjectIdAndQrName" resultType="boolean">
        SELECT EXISTS(
            SELECT 1
            FROM qr_codes
            WHERE project_id = #{projectId}
              AND qr_name = #{qrName}
              AND delete_yn = 'N'
        )
    </select>

    <insert id="insertQrCode" parameterType="kr.wayplus.wayplus_qr.entity.QrCode" useGeneratedKeys="true" keyProperty="qrCodeId">
        INSERT INTO qr_codes (
            project_id, qr_name, qr_type, status, target_content, description,
            linked_landing_page_id, linked_event_id, design_options, qr_image_path,
            valid_from_date, valid_to_date, create_user_email, create_date,
            installation_location, installation_location_lat, installation_location_lng,
            use_yn, delete_yn, qr_uuid
        )
        VALUES (
            #{projectId}, #{qrName}, #{qrType}, #{status, jdbcType=VARCHAR}, #{targetContent}, #{description, jdbcType=VARCHAR},
            #{linkedLandingPageId, jdbcType=BIGINT}, #{linkedEventId, jdbcType=BIGINT},
            #{designOptions, jdbcType=VARCHAR}, #{imagePath, jdbcType=VARCHAR},
            #{validFromDate, jdbcType=TIMESTAMP}, #{validToDate, jdbcType=TIMESTAMP},
            #{createUserEmail}, NOW(),
            #{installationLocation}, #{installationLocationLat}, #{installationLocationLng},
            'Y', 'N',
            #{qrUuid, jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateQrCode" parameterType="kr.wayplus.wayplus_qr.entity.QrCode">
        UPDATE qr_codes
        <set>
            <if test="qrName != null and qrName != ''">qr_name = #{qrName},</if>
            <if test="qrType != null and qrType != ''">qr_type = #{qrType},</if>
            <if test="status != null">status = #{status},</if>
            <if test="targetContent != null and targetContent != ''">target_content = #{targetContent},</if>
            <if test="description != null">description = #{description},</if>
            <if test="linkedLandingPageId != null">linked_landing_page_id = #{linkedLandingPageId},</if>
            <if test="linkedEventId != null">linked_event_id = #{linkedEventId},</if>
            <if test="designOptions != null">design_options = #{designOptions},</if>
            <if test="imagePath != null">qr_image_path = #{imagePath},</if>
            <if test="scanCount != null">scan_count = #{scanCount},</if>
            <if test="installationLocation != null">installation_location = #{installationLocation},</if>
            <if test="installationLocationLat != null">installation_location_lat = #{installationLocationLat},</if>
            <if test="installationLocationLng != null">installation_location_lng = #{installationLocationLng},</if>
            valid_from_date = #{validFromDate},
            valid_to_date = #{validToDate},
            <if test="useYn != null and useYn != ''">use_yn = #{useYn},</if>
            <if test="deleteYn != null and deleteYn != ''">delete_yn = #{deleteYn},</if>
            <if test="deleteUserEmail != null">delete_user_email = #{deleteUserEmail},</if>
            <if test="deleteDate != null">delete_date = #{deleteDate},</if>
            update_user_email = #{updateUser},
            last_update_date = NOW()
        </set>
        WHERE qr_code_id = #{qrCodeId}
          AND delete_yn = 'N'
    </update>

    <update id="updateQrCodeImagePath" parameterType="map">
        UPDATE qr_codes
        SET
            qr_image_path = #{imagePath, jdbcType=VARCHAR},
            last_update_date = NOW()
            -- updateUserEmail은 이 쿼리에서는 업데이트하지 않음.
            --       필요하다면 QrCodeService에서 QrCode 객체를 조회하여 updateQrCode를 호출하거나,
            --       이 메서드의 파라미터로 updateUserEmail을 추가해야 함.
        WHERE
            qr_code_id = #{qrCodeId}
            AND delete_yn = 'N'
    </update>

    <update id="updateQrCodePaths" parameterType="map">
        UPDATE qr_codes
        SET
            qr_image_path = #{svgPath, jdbcType=VARCHAR},
            qr_logo_path = #{logoPath, jdbcType=VARCHAR},
            qr_installed_image_path = #{installedImagePath, jdbcType=VARCHAR},
            last_update_date = NOW()
            -- Consider adding updateUserEmail if necessary, similar to updateQrCodeImagePath note
        WHERE
            qr_code_id = #{qrCodeId}
            AND delete_yn = 'N'
    </update>

    <update id="deleteQrCodeById" parameterType="map">
        UPDATE qr_codes
           SET qr_name = CONCAT(qr_name,'_DEL_',#{qrCodeId}),
               delete_yn = 'Y',
               delete_user_email = #{deleteUserEmail},
               delete_date = NOW()
         WHERE qr_code_id = #{qrCodeId}
           AND delete_yn = 'N'
    </update>
    
    <!-- 여러 QR 코드 ID를 한번에 삭제 (논리적 삭제) -->
    <update id="deleteQrCodesByIds" parameterType="map">
        UPDATE qr_codes
        SET delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = NOW()
        WHERE qr_code_id IN
        <foreach collection="qrCodeIds" item="qrCodeId" open="(" separator="," close=")">
            #{qrCodeId}
        </foreach>
        AND delete_yn = 'N'
    </update>

    <update id="deleteQrCodeByUuid" parameterType="map">
        UPDATE qr_codes
           SET qr_name = CONCAT(qr_name,'_DEL_',#{qrUuid}),
               delete_yn = 'Y',
               delete_user_email = #{deleteUserEmail},
               delete_date = NOW()
        WHERE qr_uuid = #{qrUuid}
          AND delete_yn = 'N'
    </update>

    <!-- UUID로 QR 코드 조회 (프로젝트 정보 포함) -->
    <select id="selectQrCodeByUuid" resultMap="qrCodeResultMap" parameterType="string">
        SELECT
            q.qr_code_id, q.project_id, q.qr_name, q.qr_type, q.status, q.target_content,
            q.description, q.linked_landing_page_id, q.linked_event_id, q.design_options,
            q.qr_image_path, q.scan_count, q.valid_from_date, q.valid_to_date, q.create_user_email,
            q.create_date, q.update_user_email, q.last_update_date, q.delete_user_email,
            q.delete_date, q.use_yn, q.delete_yn,
            q.qr_uuid,
            p.project_name
        FROM
            qr_codes q
        JOIN
            projects p ON q.project_id = p.project_id
        WHERE
            q.qr_uuid = #{qrUuid}
            AND q.delete_yn = 'N'
            AND q.use_yn = 'Y'
    </select>

    <!-- 이벤트 ID로 참석용 QR 코드 조회 -->
    <select id="selectQrCodeByLinkedEventId" resultMap="qrCodeResultMap" parameterType="long">
        SELECT
            q.qr_code_id, q.qr_uuid, q.project_id, q.qr_name, q.qr_type, q.status, q.target_content,
            q.description, q.linked_landing_page_id, q.linked_event_id, q.design_options,
            q.qr_image_path, q.scan_count, q.valid_from_date, q.valid_to_date, q.create_user_email,
            q.create_date, q.update_user_email, q.last_update_date, q.delete_user_email,
            q.delete_date, q.use_yn, q.delete_yn,
            p.project_name
        FROM
            qr_codes q
        LEFT JOIN
            projects p ON q.project_id = p.project_id
        WHERE
            q.linked_event_id = #{eventId}
            AND q.qr_type = 'EVENT_ATTENDANCE' -- 이벤트 참석용 타입 가정
            AND q.delete_yn = 'N'
            AND q.use_yn = 'Y'
        LIMIT 1 -- 이벤트 당 하나의 참석 QR 코드만 있다고 가정
    </select>

    <select id="selectQrCodeByTargetContent" resultMap="qrCodeResultMap">
        SELECT
            q.qr_code_id, q.project_id, q.qr_name, q.qr_type, q.status, q.target_content,
            q.description, q.linked_landing_page_id, q.linked_event_id, q.design_options,
            q.qr_image_path, q.scan_count, q.valid_from_date, q.valid_to_date, q.create_user_email,
            q.create_date, q.update_user_email, q.last_update_date, q.delete_user_email,
            q.delete_date, q.use_yn, q.delete_yn,
            q.qr_uuid,
            p.project_name
        FROM
            qr_codes q
        JOIN
            projects p ON q.project_id = p.project_id
        WHERE
            q.target_content = #{targetContent}
            AND q.qr_type = 'EVENT_ATTENDANCE' -- 이벤트 참석용 타입 가정
            AND q.delete_yn = 'N'
            AND q.use_yn = 'Y'
        LIMIT 1
    </select>

    <!-- 페이징 및 정렬 적용된 QR 코드 목록 조회 -->
    <select id="selectQrCodesByProjectIdWithPaging" parameterType="map" resultMap="qrCodeResultMap">
        SELECT
            q.qr_code_id, q.project_id, q.qr_name, q.qr_uuid, q.qr_type, q.target_content,
            q.description, q.valid_from_date, q.valid_to_date, q.qr_image_path,
            q.design_options, q.status, q.scan_count, q.create_user_email,
            q.create_date, q.update_user_email, q.last_update_date,
            q.use_yn, q.delete_yn, q.linked_landing_page_id, q.linked_event_id,
            lp.page_title AS linked_landing_page_title,
            e.event_name AS linked_event_name,
            p.project_name
        FROM qr_codes q
        LEFT JOIN landing_pages lp ON q.linked_landing_page_id = lp.landing_page_id AND lp.delete_yn = 'N'
        LEFT JOIN events e ON q.linked_event_id = e.event_id AND e.delete_yn = 'N'
        JOIN projects p ON q.project_id = p.project_id
        WHERE q.project_id = #{projectId}
          AND q.delete_yn = 'N'
          <if test="status != null and status != ''">
            AND q.status = #{status}
          </if>
          <if test="searchKeyword != null and searchKeyword != '' and searchColumn != null">
            AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
          </if>
        <!-- 동적 정렬 -->
        <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
            ORDER BY
            <foreach item="order" collection="pageable.sort" separator=", ">
                <choose>
                    <when test="order.property == 'qrName'">q.qr_name</when>
                    <when test="order.property == 'createDate'">q.create_date</when>
                    <when test="order.property == 'lastUpdateDate'">q.last_update_date</when>
                    <when test="order.property == 'scanCount'">q.scan_count</when>
                    <when test="order.property == 'status'">q.status</when>
                    <!-- 다른 허용된 정렬 기준 컬럼 추가 (예: linked_landing_page_title, linked_event_name 등) -->
                    <otherwise>q.create_date</otherwise> <!-- 기본 정렬 기준 -->
                </choose>
                <choose>
                    <when test="order.direction.name() == 'ASC'">ASC</when>
                    <when test="order.direction.name() == 'DESC'">DESC</when>
                    <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                </choose>
            </foreach>
        </if>
        <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
            <!-- 기본 정렬: 생성일 내림차순 -->
            ORDER BY q.create_date DESC
        </if>
        <!-- 페이징 -->
        <if test="pageable != null">
            LIMIT #{pageable.offset}, #{pageable.pageSize}
        </if>
    </select>

    <!-- 프로젝트 ID로 QR 코드 개수 조회 -->
    <select id="countQrCodesByProjectId" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM qr_codes
        WHERE project_id = #{projectId}
          AND delete_yn = 'N'
          <if test="status != null and status != ''">
            AND status = #{status}
          </if>
          <if test="searchKeyword != null and searchKeyword != '' and searchColumn != null">
            AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
          </if>
    </select>

    <!-- 통계 관련 쿼리 -->
    <select id="selectTotalQrStatus" resultType="kr.wayplus.wayplus_qr.dto.response.TotalQrStatusDto">
        SELECT
            COUNT(qr_code_id) AS totalQrCodes,
            SUM(scan_count) AS totalScans
        FROM qr_codes
        WHERE delete_yn = 'N'
        <if test="startDate != null">
            AND create_date &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND create_date &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
        </if>
    </select>

    <!-- 차트용 통계 관련 쿼리 (기간별 조회) -->
    <select id="selectQrStatusForChart" resultType="kr.wayplus.wayplus_qr.dto.response.TotalQrStatusDto">
        SELECT
            (SELECT COUNT(qr_code_id) FROM qr_codes qc
             WHERE qc.delete_yn = 'N'
               AND qc.use_yn = 'Y'
               <if test="startDate != null">
                   AND qc.create_date >= #{startDate} -- 시작일 00:00:00부터
               </if>
               <if test="endDate != null">
                   AND qc.create_date &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY) -- 종료일 23:59:59까지
               </if>
            ) AS totalQrCodes,
            (SELECT COUNT(scan_log_id) FROM qr_scan_logs qsl -- 테이블명 및 컬럼명 수정
             WHERE 1=1 -- 항상 참인 조건으로 시작 (필요시 scan_result = 'SUCCESS' 등 추가)
               <if test="startDate != null">
                   AND qsl.scan_time >= #{startDate} -- 시작일 00:00:00부터
               </if>
               <if test="endDate != null">
                   AND qsl.scan_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY) -- 종료일 23:59:59까지
               </if>
            ) AS totalScans
    </select>

    <!-- 지정된 기간 동안 일별 QR 코드 생성 수 조회 -->
    <select id="selectDailyCreatedCounts" resultType="kr.wayplus.wayplus_qr.dto.response.DailyCountDto">
        SELECT
            DATE(create_date) AS date,
            COUNT(qr_code_id) AS count
        FROM qr_codes
        <where>
             delete_yn = 'N'
            <if test="startDate != null">
                AND DATE(create_date) >= #{startDate} -- DATE 타입으로 변환 후 비교
            </if>
            <if test="endDate != null">
                AND DATE(create_date) &lt;= #{endDate} -- DATE 타입으로 변환 후 비교
            </if>
        </where>
        GROUP BY DATE(create_date)
        ORDER BY DATE(create_date)
    </select>

    <!-- 지정된 기간 동안 일별 QR 코드 스캔 수 조회 -->
    <select id="selectDailyScannedCounts" resultType="kr.wayplus.wayplus_qr.dto.response.DailyCountDto">
        SELECT
            DATE(scan_time) AS date,
            COUNT(scan_log_id) AS count
        FROM qr_scan_logs
        <where>
            <if test="startDate != null">
                AND scan_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND scan_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
            </if>
        </where>
        GROUP BY DATE(scan_time)
        ORDER BY DATE(scan_time)
    </select>

    <!-- Method to increment the scan count for a given QR code UUID -->


    <!-- 지정된 프로젝트 및 기간 동안 일별 QR 코드 생성 수 조회 -->
    <select id="selectDailyCreatedCountsByProject" resultType="kr.wayplus.wayplus_qr.dto.response.DailyCountDto">
        SELECT
            DATE(q.create_date) AS date,
            COUNT(q.qr_code_id) AS count
        FROM qr_codes q
        WHERE q.delete_yn = 'N'
          AND q.project_id = #{projectId}
          <if test="startDate != null">
              AND DATE(q.create_date) &gt;= #{startDate}
          </if>
          <if test="endDate != null">
              AND DATE(q.create_date) &lt;= #{endDate}
          </if>
        GROUP BY DATE(q.create_date)
        ORDER BY DATE(q.create_date)
    </select>

    <!-- 지정된 프로젝트 및 기간 동안 일별 QR 코드 스캔 수 조회 -->
    <select id="selectDailyScannedCountsByProject" resultType="kr.wayplus.wayplus_qr.dto.response.DailyCountDto">
        SELECT
            DATE(qsl.scan_time) AS date,
            COUNT(qsl.scan_log_id) AS count
        FROM qr_scan_logs qsl
        JOIN qr_codes q ON qsl.qr_code_id = q.qr_code_id
        WHERE q.project_id = #{projectId}
          <if test="startDate != null">
            AND qsl.scan_time &gt;= #{startDate}
          </if>
          <if test="endDate != null">
            AND qsl.scan_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
          </if>
        GROUP BY DATE(qsl.scan_time)
        ORDER BY DATE(qsl.scan_time)
    </select>

    <!-- 프로젝트별 총 QR 코드/스캔 수 조회 -->
    <select id="selectProjectTotalQrStatus" resultType="kr.wayplus.wayplus_qr.dto.response.TotalQrStatusDto">
        SELECT
            COUNT(qr_code_id) AS totalQrCodes,
            SUM(scan_count) AS totalScans
        FROM qr_codes
        WHERE project_id = #{projectId}
          AND delete_yn = 'N'
          <if test="startDate != null">
            AND create_date &gt;= #{startDate}
          </if>
          <if test="endDate != null">
            AND create_date &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
          </if>
    </select>

    <!-- SUPER_ADMIN 용: 모든 QR 코드 목록 조회 (페이징) -->
    <select id="selectAllQrCodesWithPaging" resultMap="qrCodeResultMap">
        SELECT
            q.qr_code_id, q.project_id, q.qr_name, q.qr_type, q.target_content, q.description,
            q.qr_uuid, q.qr_image_path, q.design_options, q.status, q.scan_count,
            q.valid_from_date, q.valid_to_date, q.linked_landing_page_id, q.linked_event_id,
            q.use_yn, q.delete_yn, q.create_date, q.create_user_email, q.last_update_date, q.update_user_email
        FROM qr_codes q
        WHERE delete_yn = 'N'
        <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
          AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
        </if>
        <!-- Pageable을 사용한 동적 정렬 -->
        <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
            ORDER BY
            <foreach item="order" collection="pageable.sort" separator=", ">
                <choose>
                    <when test="order.property == 'qrCodeName'">q.qr_name</when>
                    <when test="order.property == 'qrType'">q.qr_type</when>
                    <when test="order.property == 'targetContent'">q.target_content</when>
                    <when test="order.property == 'validFromDate'">q.valid_from_date</when>
                    <when test="order.property == 'validToDate'">q.valid_to_date</when>
                    <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                    <otherwise>q.create_date</otherwise> <!-- 기본 정렬 기준 -->
                </choose>
                <choose>
                    <when test="order.direction.name() == 'ASC'">ASC</when>
                    <when test="order.direction.name() == 'DESC'">DESC</when>
                    <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                </choose>
            </foreach>
        </if>
        <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
            <!-- 기본 정렬: 생성일 내림차순 -->
            ORDER BY q.create_date DESC
        </if>
        <!-- Pageable을 사용한 페이징 -->
        <if test="pageable != null">
            LIMIT #{pageable.offset}, #{pageable.pageSize}
        </if>
    </select>

    <!-- SUPER_ADMIN 용: 모든 QR 코드 총 개수 조회 -->
    <select id="countAllQrCodes" resultType="long">
        SELECT COUNT(*)
        FROM qr_codes
        WHERE delete_yn = 'N'
        <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
          AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
        </if>
    </select>

    <!-- QR 코드 타입별 분포 조회 -->
    <select id="selectQrTypeDistribution" resultType="map">
        SELECT
            qr_type AS type,
            COUNT(*) AS count
        FROM
            qr_codes
        WHERE
            delete_yn = 'N'
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
            <if test="startDate != null">
                AND create_date >= #{startDate, jdbcType=DATE}
            </if>
            <if test="endDate != null">
                AND create_date &lt; DATE_ADD(#{endDate, jdbcType=DATE}, INTERVAL 1 DAY)
            </if>
        GROUP BY
            qr_type
        ORDER BY
            count DESC
    </select>

    <!-- QR 코드 상태별 분포 조회 -->
    <select id="selectQrStatusDistribution" resultType="map">
        SELECT
            status AS status,
            COUNT(*) AS count
        FROM
            qr_codes
        WHERE
            delete_yn = 'N'
            <if test="projectId != null">
                AND project_id = #{projectId}
            </if>
            <if test="startDate != null">
                AND create_date >= #{startDate, jdbcType=DATE}
            </if>
            <if test="endDate != null">
                AND create_date &lt; DATE_ADD(#{endDate, jdbcType=DATE}, INTERVAL 1 DAY)
            </if>
        GROUP BY
            status
        ORDER BY
            count DESC
    </select>

    <!-- 기기별 QR 코드 스캔 통계 조회 -->
    <select id="selectQrScanDeviceStats" resultType="map">
        SELECT
            CASE
                WHEN user_agent LIKE '%Mobile%' THEN 'Mobile'
                WHEN user_agent LIKE '%Tablet%' THEN 'Tablet'
                ELSE 'Desktop'
            END AS device_type,
            COUNT(*) AS count
        FROM qr_scan_logs
        <where>
            <if test="startDate != null">
                AND scan_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND scan_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
            </if>
            <if test="projectId != null">
                AND qr_code_id IN (SELECT qr_code_id FROM qr_codes WHERE project_id = #{projectId} AND delete_yn = 'N')
            </if>
        </where>
        GROUP BY device_type
        ORDER BY count DESC
    </select>

    <!-- 브라우저별 QR 코드 스캔 통계 조회 -->
    <select id="selectQrScanBrowserStats" resultType="map">
        SELECT
            CASE
                WHEN user_agent LIKE '%Chrome%' THEN 'Chrome'
                WHEN user_agent LIKE '%Firefox%' THEN 'Firefox'
                WHEN user_agent LIKE '%Safari%' THEN 'Safari'
                WHEN user_agent LIKE '%Edge%' THEN 'Edge'
                WHEN user_agent LIKE '%MSIE%' OR user_agent LIKE '%Trident%' THEN 'Internet Explorer'
                ELSE 'Other'
            END AS browser_type,
            COUNT(*) AS count
        FROM qr_scan_logs
        <where>
            <if test="startDate != null">
                AND scan_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND scan_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
            </if>
            <if test="projectId != null">
                AND qr_code_id IN (SELECT qr_code_id FROM qr_codes WHERE project_id = #{projectId} AND delete_yn = 'N')
            </if>
        </where>
        GROUP BY browser_type
        ORDER BY count DESC
    </select>

    <!-- OS별 QR 코드 스캔 통계 조회 -->
    <select id="selectQrScanOsStats" resultType="map">
        SELECT
            CASE
                WHEN user_agent LIKE '%Android%' THEN 'Android'
                WHEN user_agent LIKE '%iPhone%' OR user_agent LIKE '%iPad%' OR user_agent LIKE '%iOS%' THEN 'iOS'
                WHEN user_agent LIKE '%Windows%' THEN 'Windows'
                WHEN user_agent LIKE '%Mac%' THEN 'Mac OS'
                WHEN user_agent LIKE '%Linux%' THEN 'Linux'
                ELSE 'Other'
            END AS os_type,
            COUNT(*) AS count
        FROM qr_scan_logs
        <where>
            <if test="startDate != null">
                AND scan_time >= #{startDate}
            </if>
            <if test="endDate != null">
                AND scan_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
            </if>
            <if test="projectId != null">
                AND qr_code_id IN (SELECT qr_code_id FROM qr_codes WHERE project_id = #{projectId} AND delete_yn = 'N')
            </if>
        </where>
        GROUP BY os_type
        ORDER BY count DESC
    </select>

    <!-- 시간대별 QR 코드 스캔 분포 조회 -->
    <select id="selectQrScanHourlyDistribution" resultType="map">
        SELECT 
            h.hour,
            IFNULL(t.count, 0) AS count
        FROM
            (SELECT 0 AS hour UNION ALL SELECT 1 UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL
            SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL SELECT 7 UNION ALL
            SELECT 8 UNION ALL SELECT 9 UNION ALL SELECT 10 UNION ALL SELECT 11 UNION ALL
            SELECT 12 UNION ALL SELECT 13 UNION ALL SELECT 14 UNION ALL SELECT 15 UNION ALL
            SELECT 16 UNION ALL SELECT 17 UNION ALL SELECT 18 UNION ALL SELECT 19 UNION ALL
            SELECT 20 UNION ALL SELECT 21 UNION ALL SELECT 22 UNION ALL SELECT 23) h
        LEFT JOIN (
            SELECT
                HOUR(scan_time) AS hour,
                COUNT(*) AS count
            FROM qr_scan_logs
            <where>
                <if test="startDate != null">
                    scan_time >= #{startDate}
                </if>
                <if test="endDate != null">
                    AND scan_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
                </if>
                <if test="projectId != null">
                    AND qr_code_id IN (SELECT qr_code_id FROM qr_codes WHERE project_id = #{projectId} AND delete_yn = 'N')
                </if>
            </where>
            GROUP BY HOUR(scan_time)
        ) t ON h.hour = t.hour
        ORDER BY h.hour
    </select>

    <!-- 요일별 QR 코드 스캔 분포 조회 -->
    <select id="selectQrScanWeekdayDistribution" resultType="map">
        SELECT 
            d.weekday,
            IFNULL(t.count, 0) AS count
        FROM
            (SELECT 1 AS day_num, 'Sunday' AS weekday UNION ALL 
             SELECT 2, 'Monday' UNION ALL 
             SELECT 3, 'Tuesday' UNION ALL 
             SELECT 4, 'Wednesday' UNION ALL
             SELECT 5, 'Thursday' UNION ALL 
             SELECT 6, 'Friday' UNION ALL 
             SELECT 7, 'Saturday') d
        LEFT JOIN (
            SELECT
                DAYOFWEEK(scan_time) AS day_num,
                CASE DAYOFWEEK(scan_time)
                    WHEN 1 THEN 'Sunday'
                    WHEN 2 THEN 'Monday'
                    WHEN 3 THEN 'Tuesday'
                    WHEN 4 THEN 'Wednesday'
                    WHEN 5 THEN 'Thursday'
                    WHEN 6 THEN 'Friday'
                    WHEN 7 THEN 'Saturday'
                END AS weekday,
                COUNT(*) AS count
            FROM qr_scan_logs
            <where>
                <if test="startDate != null">
                    scan_time >= #{startDate}
                </if>
                <if test="endDate != null">
                    AND scan_time &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
                </if>
                <if test="projectId != null">
                    AND qr_code_id IN (SELECT qr_code_id FROM qr_codes WHERE project_id = #{projectId} AND delete_yn = 'N')
                </if>
            </where>
            GROUP BY DAYOFWEEK(scan_time)
        ) t ON d.day_num = t.day_num
        ORDER BY d.day_num
    </select>

    <!-- 교환권 QR 코드 통계 조회 (events, event_benefits, attendee_benefit_redemption 테이블 사용) -->
    <select id="selectExchangeQrStats" resultType="map">
        SELECT
            COUNT(DISTINCT q.qr_code_id) AS totalExchangeQrCodes,
            IFNULL(SUM(eb.quantity), 0) AS totalExchangeCount,
            IFNULL(COUNT(abr.redemption_id), 0) AS totalUsedCount
        FROM qr_codes q
        JOIN events e ON q.linked_event_id = e.event_id
        LEFT JOIN event_benefits eb ON e.event_id = eb.event_id AND eb.delete_yn = 'N' AND eb.status = 'ACTIVE'
        LEFT JOIN attendee_benefit_redemption abr ON eb.benefit_id = abr.benefit_id
        WHERE q.delete_yn = 'N'
        <if test="projectId != null">
            AND q.project_id = #{projectId}
        </if>
        <if test="startDate != null">
            AND abr.redeemed_at &gt;= #{startDate}
        </if>
        <if test="endDate != null">
            AND abr.redeemed_at &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
        </if>
    </select>

    <!-- 교환권 QR 코드 일별 승인 횟수 조회 (attendee_benefit_redemption 테이블 사용) -->
    <select id="selectDailyExchangeApprovalCounts" resultType="kr.wayplus.wayplus_qr.dto.response.DailyCountDto">
        SELECT
            DATE(abr.redeemed_at) AS date,
            COUNT(*) AS count
        FROM attendee_benefit_redemption abr
        JOIN event_benefits eb ON abr.benefit_id = eb.benefit_id
        JOIN events e ON eb.event_id = e.event_id
        JOIN qr_codes q ON e.event_id = q.linked_event_id
        <where>
            q.delete_yn = 'N'
            <if test="startDate != null">
                AND abr.redeemed_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND abr.redeemed_at &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
            </if>
            <if test="projectId != null">
                AND q.project_id = #{projectId}
            </if>
        </where>
        GROUP BY DATE(abr.redeemed_at)
        ORDER BY DATE(abr.redeemed_at)
    </select>

    <!-- 승인자별 교환권 QR 코드 승인 횟수 조회 (attendee_benefit_redemption 테이블 사용) -->
    <select id="selectApproverExchangeCounts" resultType="map">
        SELECT
            abr.redeemed_by_user_email AS approver,
            COUNT(*) AS count
        FROM attendee_benefit_redemption abr
        JOIN event_benefits eb ON abr.benefit_id = eb.benefit_id
        JOIN events e ON eb.event_id = e.event_id
        JOIN qr_codes q ON e.event_id = q.linked_event_id
        <where>
            q.delete_yn = 'N'
            <if test="startDate != null">
                AND abr.redeemed_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND abr.redeemed_at &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
            </if>
            <if test="projectId != null">
                AND q.project_id = #{projectId}
            </if>
        </where>
        GROUP BY abr.redeemed_by_user_email
        ORDER BY count DESC
    </select>

    <!-- 시간대별 교환권 QR 코드 승인 횟수 조회 (attendee_benefit_redemption 테이블 사용) -->
    <select id="selectHourlyExchangeApprovalCounts" resultType="map">
        SELECT
            HOUR(abr.redeemed_at) AS hour,
            COUNT(*) AS count
        FROM attendee_benefit_redemption abr
        JOIN event_benefits eb ON abr.benefit_id = eb.benefit_id
        JOIN events e ON eb.event_id = e.event_id
        JOIN qr_codes q ON e.event_id = q.linked_event_id
        <where>
            q.delete_yn = 'N'
            <if test="startDate != null">
                AND abr.redeemed_at >= #{startDate}
            </if>
            <if test="endDate != null">
                AND abr.redeemed_at &lt; DATE_ADD(#{endDate}, INTERVAL 1 DAY)
            </if>
            <if test="projectId != null">
                AND q.project_id = #{projectId}
            </if>
        </where>
        GROUP BY HOUR(abr.redeemed_at)
        ORDER BY HOUR(abr.redeemed_at)
    </select>



    <!-- 한번이라도 스캔된 QR 코드 목록 조회 (최대 5개, 삭제된 것 제외) -->
    <select id="selectScannedQrCodes" resultType="map">
        SELECT
            q.qr_code_id AS qr_code_id, q.qr_name, q.qr_type, q.installation_location as location, 
            q.installation_location_lat, q.installation_location_lng,
            q.scan_count, DATE_FORMAT(q.create_date, '%Y-%m-%d') as created_at
        FROM
            qr_codes q
        WHERE
            q.delete_yn = 'N'
            AND q.scan_count > 0
            AND q.installation_location IS NOT NULL
            <if test="projectId != null">
                AND q.project_id = #{projectId}
            </if>
            <if test="startDate != null">
                AND q.create_date >= #{startDate, jdbcType=DATE}
            </if>
            <if test="endDate != null">
                AND q.create_date &lt; DATE_ADD(#{endDate, jdbcType=DATE}, INTERVAL 1 DAY)
            </if>
        ORDER BY
            q.scan_count DESC,
            q.create_date DESC
    </select>
    <!-- 동일한 project_id와 qr_name을 가지면서 이미 삭제된 QR 코드 찾기 -->
    <select id="findConflictingSoftDeletedQrCode" resultMap="qrCodeResultMap">
        SELECT 
            qr_code_id, project_id, qr_name, qr_type, status, target_content,
            description, linked_landing_page_id, linked_event_id, design_options,
            installation_location, installation_location_lat, installation_location_lng,
            qr_image_path, qr_installed_image_path, scan_count, valid_from_date, valid_to_date, create_user_email,
            create_date, update_user_email, last_update_date, delete_user_email,
            delete_date, use_yn, delete_yn, qr_uuid
        FROM qr_codes
        WHERE project_id = #{projectId}
          AND qr_name = #{qrName}
          AND delete_yn = 'Y'
          AND qr_code_id != #{currentQrCodeId}
        LIMIT 1
    </select>
    
    <!-- 충돌하는 QR 코드의 이름 변경 -->
    <update id="updateQrNameWithSuffix">
        UPDATE qr_codes
        SET qr_name = #{newQrName},
            last_update_date = NOW()
        WHERE qr_code_id = #{qrCodeId}
    </update>
</mapper>
