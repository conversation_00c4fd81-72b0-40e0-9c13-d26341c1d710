package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.entity.PreRegistrationFormField;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

@Mapper
public interface PreRegistrationFormFieldMapper {

    int insertField(PreRegistrationFormField field);

    List<PreRegistrationFormField> selectFieldsByFormId(@Param("formId") Long formId);

    // 삭제된 필드 포함 모든 필드 정보 조회 (데이터 변환용)
    List<PreRegistrationFormField> selectAllFieldsByFormId(@Param("formId") Long formId);

    // submissionData 변환용: 삭제된 필드 포함 모든 필드의 이름과 라벨 조회
    List<Map<String, String>> selectFieldNameAndLabelByFormIdIncludingDeleted(@Param("formId") Long formId);

    // Update: mark all existing fields as deleted for a form
    int deleteFieldsByFormId(@Param("formId") Long formId, @Param("userEmail") String userEmail);
}
