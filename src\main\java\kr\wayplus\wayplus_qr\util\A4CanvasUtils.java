package kr.wayplus.wayplus_qr.util;

import lombok.extern.slf4j.Slf4j;

/**
 * A4 캔버스 관련 유틸리티 클래스
 * - A4 용지 크기 및 좌표계 처리
 * - mm ↔ px 단위 변환
 * - 300 DPI 기준 인쇄 품질 지원
 */
@Slf4j
public class A4CanvasUtils {

    // A4 용지 표준 크기 (mm)
    public static final double A4_WIDTH_MM = 210.0;
    public static final double A4_HEIGHT_MM = 297.0;

    // 인쇄 품질 DPI (Dots Per Inch)
    public static final int PRINT_DPI = 300;

    // 1인치 = 25.4mm
    public static final double MM_PER_INCH = 25.4;

    // 300 DPI 기준 1mm당 픽셀 수
    public static final double PIXELS_PER_MM = PRINT_DPI / MM_PER_INCH; // ≈ 11.81 pixels/mm

    // A4 용지 크기 (300 DPI 기준 픽셀)
    public static final int A4_WIDTH_PX = (int) Math.round(A4_WIDTH_MM * PIXELS_PER_MM); // 2480 px
    public static final int A4_HEIGHT_PX = (int) Math.round(A4_HEIGHT_MM * PIXELS_PER_MM); // 3508 px

    /**
     * mm를 픽셀로 변환 (300 DPI 기준)
     * @param mm 밀리미터 값
     * @return 픽셀 값 (소수점 첫째 자리까지 반올림)
     */
    public static double mmToPixels(double mm) {
        double pixels = mm * PIXELS_PER_MM;
        return Math.round(pixels * 10.0) / 10.0; // 소수점 첫째 자리까지 반올림
    }

    /**
     * 픽셀을 mm로 변환 (300 DPI 기준)
     * @param pixels 픽셀 값
     * @return 밀리미터 값 (소수점 첫째 자리까지 반올림)
     */
    public static double pixelsToMm(double pixels) {
        double mm = pixels / PIXELS_PER_MM;
        return Math.round(mm * 10.0) / 10.0; // 소수점 첫째 자리까지 반올림
    }

    /**
     * A4 용지 기준으로 QR코드 위치를 픽셀 좌표로 변환
     * @param positionXMm X 위치 (mm)
     * @param positionYMm Y 위치 (mm)
     * @return int[] {x픽셀, y픽셀}
     */
    public static int[] mmPositionToPixels(double positionXMm, double positionYMm) {
        int xPixels = (int) Math.round(mmToPixels(positionXMm));
        int yPixels = (int) Math.round(mmToPixels(positionYMm));
        
        log.debug("Position conversion: ({}, {}) mm -> ({}, {}) px", 
                 positionXMm, positionYMm, xPixels, yPixels);
        
        return new int[]{xPixels, yPixels};
    }

    /**
     * A4 용지 기준으로 QR코드 크기를 픽셀로 변환
     * @param widthMm 너비 (mm)
     * @param heightMm 높이 (mm)
     * @return int[] {너비픽셀, 높이픽셀}
     */
    public static int[] mmSizeToPixels(double widthMm, double heightMm) {
        int widthPixels = (int) Math.round(mmToPixels(widthMm));
        int heightPixels = (int) Math.round(mmToPixels(heightMm));
        
        log.debug("Size conversion: ({}, {}) mm -> ({}, {}) px", 
                 widthMm, heightMm, widthPixels, heightPixels);
        
        return new int[]{widthPixels, heightPixels};
    }

    /**
     * A4 용지 범위 내 좌표인지 검증
     * @param positionXMm X 위치 (mm)
     * @param positionYMm Y 위치 (mm)
     * @param widthMm QR코드 너비 (mm)
     * @param heightMm QR코드 높이 (mm)
     * @return 유효한 범위 내인지 여부
     */
    public static boolean isValidA4Position(double positionXMm, double positionYMm, 
                                          double widthMm, double heightMm) {
        // QR코드가 A4 용지 범위를 벗어나지 않는지 확인
        boolean valid = positionXMm >= 0 && positionYMm >= 0 &&
                       (positionXMm + widthMm) <= A4_WIDTH_MM &&
                       (positionYMm + heightMm) <= A4_HEIGHT_MM;
        
        if (!valid) {
            log.warn("QR code position/size exceeds A4 boundaries: " +
                    "position=({}, {}) mm, size=({}, {}) mm, A4=({}, {}) mm",
                    positionXMm, positionYMm, widthMm, heightMm, A4_WIDTH_MM, A4_HEIGHT_MM);
        }
        
        return valid;
    }

    /**
     * 배경 이미지 맞춤 모드에 따른 스케일 계산
     * @param backgroundWidth 배경 이미지 원본 너비
     * @param backgroundHeight 배경 이미지 원본 높이
     * @param canvasWidth 캔버스 너비
     * @param canvasHeight 캔버스 높이
     * @param fitMode 맞춤 모드 ("fit", "fill", "original")
     * @return double[] {scaleX, scaleY, offsetX, offsetY}
     */
    public static double[] calculateBackgroundScale(int backgroundWidth, int backgroundHeight,
                                                   int canvasWidth, int canvasHeight, 
                                                   String fitMode) {
        double scaleX = 1.0;
        double scaleY = 1.0;
        double offsetX = 0.0;
        double offsetY = 0.0;

        switch (fitMode != null ? fitMode.toLowerCase() : "fit") {
            case "fit":
                // 비율을 유지하면서 캔버스에 맞춤 (여백 생김)
                double scale = Math.min((double) canvasWidth / backgroundWidth, 
                                      (double) canvasHeight / backgroundHeight);
                scaleX = scaleY = scale;
                offsetX = (canvasWidth - backgroundWidth * scale) / 2.0;
                offsetY = (canvasHeight - backgroundHeight * scale) / 2.0;
                break;
                
            case "fill":
                // 비율을 유지하면서 캔버스를 채움 (잘림 발생 가능)
                double fillScale = Math.max((double) canvasWidth / backgroundWidth, 
                                          (double) canvasHeight / backgroundHeight);
                scaleX = scaleY = fillScale;
                offsetX = (canvasWidth - backgroundWidth * fillScale) / 2.0;
                offsetY = (canvasHeight - backgroundHeight * fillScale) / 2.0;
                break;
                
            case "original":
                // 원본 크기 유지 (중앙 정렬)
                scaleX = scaleY = 1.0;
                offsetX = (canvasWidth - backgroundWidth) / 2.0;
                offsetY = (canvasHeight - backgroundHeight) / 2.0;
                break;
                
            default:
                log.warn("Unknown background fit mode: {}. Using 'fit' as default.", fitMode);
                // fit 모드로 fallback
                double defaultScale = Math.min((double) canvasWidth / backgroundWidth, 
                                             (double) canvasHeight / backgroundHeight);
                scaleX = scaleY = defaultScale;
                offsetX = (canvasWidth - backgroundWidth * defaultScale) / 2.0;
                offsetY = (canvasHeight - backgroundHeight * defaultScale) / 2.0;
        }

        log.debug("Background scale calculation: {}x{} -> {}x{}, mode={}, " +
                 "scale=({}, {}), offset=({}, {})",
                 backgroundWidth, backgroundHeight, canvasWidth, canvasHeight, fitMode,
                 scaleX, scaleY, offsetX, offsetY);

        return new double[]{scaleX, scaleY, offsetX, offsetY};
    }

    /**
     * A4 캔버스 사용 여부 확인
     * @param useA4Canvas A4 캔버스 사용 플래그
     * @return A4 캔버스 사용 여부
     */
    public static boolean shouldUseA4Canvas(Boolean useA4Canvas) {
        return useA4Canvas != null && useA4Canvas;
    }

    /**
     * 배경 이미지 사용 여부 확인
     * @param useBackgroundImage 배경 이미지 사용 플래그
     * @return 배경 이미지 사용 여부
     */
    public static boolean shouldUseBackgroundImage(Boolean useBackgroundImage) {
        return useBackgroundImage != null && useBackgroundImage;
    }

    /**
     * A4 크기 정보를 기준으로 픽셀 크기 계산
     * @param widthCm 너비 (cm)
     * @param heightCm 높이 (cm)
     * @return int[] {너비픽셀, 높이픽셀}
     */
    public static int[] cmSizeToPixels(double widthCm, double heightCm) {
        // cm를 mm로 변환 후 픽셀로 변환
        double widthMm = widthCm * 10.0;
        double heightMm = heightCm * 10.0;
        
        int widthPixels = (int) Math.round(mmToPixels(widthMm));
        int heightPixels = (int) Math.round(mmToPixels(heightMm));
        
        log.debug("Size conversion: ({}, {}) cm -> ({}, {}) mm -> ({}, {}) px", 
                 widthCm, heightCm, widthMm, heightMm, widthPixels, heightPixels);
        
        return new int[]{widthPixels, heightPixels};
    }
}
