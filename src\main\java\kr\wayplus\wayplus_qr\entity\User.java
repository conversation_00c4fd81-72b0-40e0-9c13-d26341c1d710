package kr.wayplus.wayplus_qr.entity;

import lombok.*;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import kr.wayplus.wayplus_qr.dto.response.ProjectResponseDto;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

@Getter
@Setter // MyBatis나 JPA 프레임워크에서 필요할 수 있음
@NoArgsConstructor(access = AccessLevel.PROTECTED)
@AllArgsConstructor // Builder와 함께 사용 시 필요
@Builder
public class User implements UserDetails {

    private Long userIdx;
    private String userEmail; // UserDetails의 getUsername() 메서드에서 사용됨
    private String name; // 사용자 이름 필드 추가
    private String password; // UserDetails의 getPassword() 메서드에서 사용됨
    private String roleId; // 역할 ID 필드 (기존 role 필드 대체 또는 병기, 여기선 대체)
    private String status; // 사용자 상태 (DB의 status 컬럼과 매핑)
    private String description; // 사용자 설명 필드 추가
    private String useYn; // 사용 여부 필드 추가
    private String lastUpdateDate; // 마지막 수정일 필드 추가
    private String updateUserEmail; // 수정자 이메일 필드 추가
    private LocalDateTime lastLoginDate; // 마지막 로그인 일자 필드 추가
    private String contactNumber; // 연락처 필드 추가
    private String userTokenId;

    @Builder.Default // 빌더 기본값으로 빈 리스트 사용
    private List<ProjectResponseDto> projects = new ArrayList<>(); // 빈 리스트로 초기화

    // 계정 상태 관련 필드 (DB에 컬럼이 있다면 매핑, 없다면 기본값 true 사용)
    @Builder.Default private boolean accountNonExpired = true; // 계정 만료 여부
    @Builder.Default private boolean accountNonLocked = true; // 계정 잠김 여부
    @Builder.Default private boolean credentialsNonExpired = true; // 비밀번호 만료 여부
    // isEnabled() 는 status 필드를 사용하므로 별도 필드 불필요

    // 생성/수정 시간 및 생성자 정보
    private LocalDateTime createDate;
    private LocalDateTime updateDate;
    private String createUserEmail; // 생성자 이메일 필드 추가
    @Builder.Default private String deleteYn = "N"; // 삭제 여부 필드 추가 및 기본값 설정

    // 최초 비밀번호 변경 필요 여부
    private String initialPasswordYn; // 'Y' or 'N', DB 컬럼명과 일치시킬 것

    // 로그인 실패 횟수
    private int loginFailCount;

    // --- UserDetails 구현 메서드 ---

    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        // 'roleId' 필드를 기반으로 권한 목록 생성 (SimpleGrantedAuthority는 역할 이름 그대로 사용)
        if (this.roleId == null || this.roleId.isBlank()) {
            return Collections.emptyList();
        }
        // Spring Security는 기본적으로 "ROLE_" 접두사를 기대하지만, hasAuthority를 사용하면 접두사 없이 가능
        return List.of(new SimpleGrantedAuthority(this.roleId));
    }

    @Override
    public String getPassword() {
        return this.password;
    }

    @Override
    public String getUsername() {
        // Spring Security에서는 일반적으로 이메일이나 ID를 username으로 사용
        return this.userEmail; 
    }

    // 아래 메서드들은 계정 상태를 반환. DB에 관련 컬럼이 있다면 해당 값 반환, 없다면 true 반환
    @Override
    public boolean isAccountNonExpired() {
        return this.accountNonExpired;
    }

    @Override
    public boolean isAccountNonLocked() {
        return this.accountNonLocked;
    }

    @Override
    public boolean isCredentialsNonExpired() {
        return this.credentialsNonExpired;
    }

    @Override
    public boolean isEnabled() {
        // status 필드가 "ACTIVE" (대소문자 구분 없이) 이고 삭제되지 않은 경우에만 활성화
        return "ACTIVE".equalsIgnoreCase(this.status) && "N".equalsIgnoreCase(this.deleteYn);
    }

    // 추가된 필드의 getter 메소드
    public LocalDateTime getLastLoginDate() {
        return lastLoginDate;
    }

    public List<ProjectResponseDto> getProjects() {
        return projects;
    }

    public void setProjects(List<ProjectResponseDto> projects) {
        this.projects = projects;
    }
}
