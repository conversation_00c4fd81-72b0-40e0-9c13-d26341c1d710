package kr.wayplus.wayplus_qr.dto.response;

import kr.wayplus.wayplus_qr.entity.InquiryComment;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 문의 댓글 응답 DTO.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryCommentResponseDto {
    private Long commentId;
    private Long inquiryId;
    private String userEmail;
    private String commentContent;
    private LocalDateTime createdAt;
    private List<AttachmentResponseDto> attachments;

    public static InquiryCommentResponseDto fromEntity(InquiryComment entity) {
        return InquiryCommentResponseDto.builder()
                .commentId(entity.getCommentId())
                .inquiryId(entity.getInquiryId())
                .userEmail(entity.getUserEmail())
                .commentContent(entity.getCommentContent())
                .createdAt(entity.getCreatedAt())
                .attachments(null) // 첨부파일은 별도로 설정
                .build();
    }
}
