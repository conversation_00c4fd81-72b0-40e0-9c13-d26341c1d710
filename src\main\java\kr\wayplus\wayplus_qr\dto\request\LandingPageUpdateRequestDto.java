package kr.wayplus.wayplus_qr.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.FutureOrPresent;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import kr.wayplus.wayplus_qr.entity.LandingPageStatus;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Map;

@Data
public class LandingPageUpdateRequestDto {

    @JsonProperty("pageTitle")
    @Size(max = 255, message = "랜딩 페이지 이름은 255자를 초과할 수 없습니다.")
    private String landingPageName;

    @Size(max = 1000, message = "설명은 1000자를 초과할 수 없습니다.")
    private String description;

    // contentJson은 Map 형태로 받지만, DB 저장 시 String으로 변환됩니다.
    // 수정 시 특정 필드만 업데이트할 수 있도록 필수가 아닙니다.
    private Map<String, Object> contentJson;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime validFromDate;

    @FutureOrPresent(message = "유효 종료일은 현재 또는 미래여야 합니다.")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime validToDate;

    @NotNull(message = "상태 값은 필수입니다.")
    private LandingPageStatus status;

    //유효성 검증: validFromDate가 validToDate보다 이전이어야 함 (필요시 추가)
    @AssertTrue(message = "유효 시작일은 유효 종료일보다 이전이어야 합니다.")
    public boolean isValidDateRange() {
        if (validFromDate == null || validToDate == null) {
            return true; // 둘 중 하나라도 없으면 검증 통과
        }
        return !validFromDate.isAfter(validToDate);
    }
}