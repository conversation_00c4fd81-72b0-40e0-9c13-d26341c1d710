<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.InquiryCommentMapper">

    <!-- 결과 매핑 -->
    <resultMap id="commentResultMap" type="kr.wayplus.wayplus_qr.entity.InquiryComment">
        <id property="commentId" column="comment_id" />
        <result property="inquiryId" column="inquiry_id" />
        <result property="userEmail" column="user_email" />
        <result property="commentContent" column="comment_content" />
        <result property="createdAt" column="created_at" />
    </resultMap>

    <!-- 댓글 생성 -->
    <insert id="insertComment" parameterType="kr.wayplus.wayplus_qr.entity.InquiryComment" useGeneratedKeys="true" keyProperty="commentId">
        INSERT INTO inquiry_comments (
            inquiry_id,
            user_email,
            comment_content,
            created_at
        ) VALUES (
            #{inquiryId},
            #{userEmail},
            #{commentContent},
            NOW()
        )
    </insert>

    <!-- ID로 댓글 조회 -->
    <select id="selectCommentById" parameterType="long" resultMap="commentResultMap">
        SELECT 
            comment_id,
            inquiry_id,
            user_email,
            comment_content,
            created_at
        FROM inquiry_comments
        WHERE comment_id = #{commentId}
    </select>

    <!-- 문의 ID로 댓글 목록 조회 -->
    <select id="selectCommentsByInquiryId" parameterType="long" resultMap="commentResultMap">
        SELECT 
            comment_id,
            inquiry_id,
            user_email,
            comment_content,
            created_at
        FROM inquiry_comments
        WHERE inquiry_id = #{inquiryId}
        ORDER BY created_at ASC
    </select>

    <!-- 댓글 삭제 -->
    <delete id="deleteComment">
        DELETE FROM inquiry_comments
        WHERE comment_id = #{commentId}
    </delete>

    <!-- 문의에 속한 모든 댓글 삭제 -->
    <delete id="deleteCommentsByInquiryId">
        DELETE FROM inquiry_comments
        WHERE inquiry_id = #{inquiryId}
    </delete>
    
    <!-- 댓글 수정 -->
    <update id="updateComment" parameterType="kr.wayplus.wayplus_qr.entity.InquiryComment">
        UPDATE inquiry_comments
        SET comment_content = #{commentContent}
        WHERE comment_id = #{commentId}
    </update>

</mapper>
