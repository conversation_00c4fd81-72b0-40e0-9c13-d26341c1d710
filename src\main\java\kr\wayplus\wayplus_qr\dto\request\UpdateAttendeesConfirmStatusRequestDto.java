package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Data;

import java.util.List;

@Data
public class UpdateAttendeesConfirmStatusRequestDto {

    @NotEmpty(message = "참가자 ID 목록은 비어 있을 수 없습니다.")
    private List<Long> attendeeIds;

    @NotNull(message = "확정 상태 값은 null일 수 없습니다.")
    @Pattern(regexp = "^[YN]$", message = "확정 상태 값은 'Y' 또는 'N'이어야 합니다.")
    private String attendedConfirmYn;
}
