<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.QrScanLogMapper">

    <insert id="insertQrScanLog" parameterType="kr.wayplus.wayplus_qr.entity.QrScanLog">
        INSERT INTO qr_scan_logs (
            qr_code_id, scan_time, ip_address, user_agent,
            location_data, scanner_user_email, is_unique_scan
        )
        VALUES (
            #{qrCodeId}, #{scanTime}, #{ipAddress}, #{userAgent},
            #{locationData, jdbcType=VARCHAR}, #{scannerUserEmail, jdbcType=VARCHAR}, #{isUniqueScan}
        )
    </insert>
    
    <!-- 특정 QR 코드의 주어진 기간 내 스캔 횟수 조회 -->
    <select id="countScansByQrIdAndDateRange" resultType="long">
        SELECT COUNT(*)
        FROM qr_scan_logs
        WHERE qr_code_id = #{qrCodeId}
          AND scan_time >= #{startDateTime}
          AND scan_time &lt; #{endDateTime}
    </select>
    
    <!-- 특정 QR 코드의 주어진 기간 내 시간대별 Top 5 스캔 횟수 조회 -->
    <select id="selectTopScanHoursByQrIdAndDateRange" resultType="java.util.Map">
        SELECT HOUR(scan_time) AS hour, COUNT(*) AS count
        FROM qr_scan_logs
        WHERE qr_code_id = #{qrCodeId}
          AND scan_time >= #{startDateTime}
          AND scan_time &lt; #{endDateTime}
        GROUP BY HOUR(scan_time)
        ORDER BY count DESC
        LIMIT 5
    </select>
    
    <!-- 특정 QR 코드의 주어진 기간 내 스캔한 기기 OS 통계 조회 -->
    <select id="selectDeviceOsStatsByQrIdAndDateRange" resultType="java.util.Map">
        SELECT 
            CASE
                WHEN user_agent LIKE '%Windows%' THEN 'Windows'
                WHEN user_agent LIKE '%Android%' THEN 'Android'
                WHEN user_agent LIKE '%iPhone%' OR user_agent LIKE '%iPad%' OR user_agent LIKE '%Macintosh%' THEN 'iOS/macOS'
                WHEN user_agent LIKE '%Linux%' THEN 'Linux'
                ELSE 'Other'
            END AS os,
            COUNT(*) AS count
        FROM qr_scan_logs
        WHERE qr_code_id = #{qrCodeId}
          AND scan_time >= #{startDateTime}
          AND scan_time &lt; #{endDateTime}
        GROUP BY 
            CASE
                WHEN user_agent LIKE '%Windows%' THEN 'Windows'
                WHEN user_agent LIKE '%Android%' THEN 'Android'
                WHEN user_agent LIKE '%iPhone%' OR user_agent LIKE '%iPad%' OR user_agent LIKE '%Macintosh%' THEN 'iOS/macOS'
                WHEN user_agent LIKE '%Linux%' THEN 'Linux'
                ELSE 'Other'
            END
        ORDER BY count DESC
    </select>
    
    <!-- 특정 QR 코드의 주어진 기간 내 일자별 스캔 횟수 조회 -->
    <select id="selectDailyScanCountsByQrIdAndDateRange" resultType="java.util.Map">
        SELECT 
            DATE_FORMAT(scan_time, '%Y-%m-%d') AS date,
            COUNT(*) AS count
        FROM qr_scan_logs
        WHERE qr_code_id = #{qrCodeId}
          AND scan_time >= #{startDateTime}
          AND scan_time &lt; #{endDateTime}
        GROUP BY DATE_FORMAT(scan_time, '%Y-%m-%d')
        ORDER BY date ASC
    </select>

</mapper>
