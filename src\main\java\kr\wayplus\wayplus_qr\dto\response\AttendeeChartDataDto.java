package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 참가자 등록 통계 차트 데이터 DTO
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class AttendeeChartDataDto {
    private List<String> dates;            // 날짜 목록 (YYYY-MM-DD)
    private List<Long> registeredCounts; // 일별 등록 수
}
