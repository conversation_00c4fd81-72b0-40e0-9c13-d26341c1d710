package kr.wayplus.wayplus_qr.exception;

import lombok.Getter;

@Getter
public class CustomEventException extends RuntimeException {
    private final ErrorCode errorCode;

    public CustomEventException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public CustomEventException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}
