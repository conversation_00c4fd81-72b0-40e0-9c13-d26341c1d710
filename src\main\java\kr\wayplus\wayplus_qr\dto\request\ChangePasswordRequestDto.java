package kr.wayplus.wayplus_qr.dto.request; // 패키지 경로 수정

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class ChangePasswordRequestDto {

    // 최초 비밀번호 변경 시 사용자 식별을 위해 필요
    @NotBlank(message = "이메일은 필수입니다.")
    private String userEmail;

    @NotBlank(message = "현재 비밀번호는 필수입니다.")
    private String currentPassword;

    @NotBlank(message = "새 비밀번호는 필수입니다.")
    @Size(min = 8, message = "새 비밀번호는 8자 이상이어야 합니다.") // 비밀번호 정책에 맞게 조정
    private String newPassword;
}
