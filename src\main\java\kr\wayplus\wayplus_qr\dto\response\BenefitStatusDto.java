package kr.wayplus.wayplus_qr.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 개별 혜택의 사용 상태 정보를 전달하기 위한 DTO.
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BenefitStatusDto {
    private Long benefitId;
    private String benefitCode;
    private String benefitName;
    private boolean redeemed; // true = 이미 사용, false = 사용 가능

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
    private LocalDateTime redeemedAt; // 사용된 시각 (null 이면 미사용)

    // 남은 수량 (null == 무제한)
    private Integer remainingQuantity;
}
