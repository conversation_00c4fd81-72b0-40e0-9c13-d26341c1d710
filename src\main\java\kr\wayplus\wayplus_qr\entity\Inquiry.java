package kr.wayplus.wayplus_qr.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 문의(Entity) - inquiries 테이블 매핑용 VO.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Inquiry {

    private Long inquiryId;
    private Long projectId;
    private String projectName;
    private String userEmail;
    private String inquiryTitle;
    private String inquiryContent;
    private InquiryType inquiryType;
    private InquiryStatus inquiryStatus;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
