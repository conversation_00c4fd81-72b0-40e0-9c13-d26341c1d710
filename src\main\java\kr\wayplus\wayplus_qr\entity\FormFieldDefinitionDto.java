package kr.wayplus.wayplus_qr.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FormFieldDefinitionDto {
    private String fieldName; // 필드 이름 (예: "name", "email")
    private String fieldLabel; // 사용자에게 보여질 레이블 (예: "이름", "이메일 주소")
    private String fieldType; // 필드 타입 (예: "text", "email", "checkbox", "select")
    private boolean required; // 필수 여부
    // 필요에 따라 추가 필드 (예: 옵션 목록, 유효성 검사 규칙 등)
}
