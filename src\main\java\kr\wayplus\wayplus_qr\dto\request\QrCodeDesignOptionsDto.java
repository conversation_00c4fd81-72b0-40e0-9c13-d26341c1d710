package kr.wayplus.wayplus_qr.dto.request;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QrCodeDesignOptionsDto {
    private QrCodeDotsOptionsDto dotsOptions;
    private QrCodeBackgroundOptionsDto backgroundOptions;
    private Double logoRatio;
    private String errorCorrectionLevel; // 오류 복원 수준 (L, M, Q, H)

}