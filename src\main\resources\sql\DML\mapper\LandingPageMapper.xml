<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.LandingPageMapper">

    <!-- 랜딩 페이지 ResultMap -->
    <resultMap id="LandingPageResultMap" type="kr.wayplus.wayplus_qr.entity.LandingPage">
        <id property="landingPageId" column="landing_page_id" jdbcType="BIGINT" javaType="java.lang.Long"/>
        <result property="projectId" column="project_id" jdbcType="BIGINT" javaType="java.lang.Long"/>
        <result property="projectName" column="project_name" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="pageTitle" column="page_title" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="description" column="description" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="contentJson" column="content_json" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="status" column="status"/>
        <result property="validFromDate" column="valid_from_date" jdbcType="TIMESTAMP" javaType="java.time.LocalDateTime"/>
        <result property="validToDate" column="valid_to_date" jdbcType="TIMESTAMP" javaType="java.time.LocalDateTime"/>
        <result property="createUserEmail" column="create_user_email" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="createDate" column="create_date" jdbcType="TIMESTAMP" javaType="java.time.LocalDateTime"/>
        <result property="updateUserEmail" column="update_user_email" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="lastUpdateDate" column="last_update_date" jdbcType="TIMESTAMP" javaType="java.time.LocalDateTime"/>
        <result property="deleteUserEmail" column="delete_user_email" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="deleteDate" column="delete_date" jdbcType="TIMESTAMP" javaType="java.time.LocalDateTime"/>
        <result property="useYn" column="use_yn" jdbcType="VARCHAR" javaType="java.lang.String"/>
        <result property="deleteYn" column="delete_yn" jdbcType="VARCHAR" javaType="java.lang.String"/>
    </resultMap>

    <!-- 랜딩 페이지 생성 -->
    <insert id="insertLandingPage" parameterType="kr.wayplus.wayplus_qr.entity.LandingPage" useGeneratedKeys="true" keyProperty="landingPageId">
        INSERT INTO landing_pages (
            project_id, page_title, description, content_json, status,
            valid_from_date, valid_to_date, create_user_email, create_date, use_yn, delete_yn
        ) VALUES (
            #{projectId}, #{pageTitle}, #{description}, #{contentJson, jdbcType=VARCHAR}, #{status, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
            #{validFromDate}, #{validToDate}, #{createUserEmail}, NOW(), 'Y', 'N'
        )
    </insert>

    <!-- ID로 랜딩 페이지 조회 -->
    <select id="selectLandingPageById" resultMap="LandingPageResultMap">
        SELECT
            landing_page_id,
            project_id,
            page_title,
            description,
            content_json,
            status,
            valid_from_date,
            valid_to_date,
            create_user_email,
            create_date,
            update_user_email,
            last_update_date,
            delete_user_email,
            delete_date,
            use_yn,
            delete_yn
        FROM landing_pages
        WHERE landing_page_id = #{landingPageId}
          AND delete_yn = 'N'
    </select>

    <!-- 프로젝트 ID로 랜딩 페이지 목록 조회 -->
    <select id="selectLandingPagesByProjectId" resultMap="LandingPageResultMap">
        SELECT *, p.project_name
        FROM landing_pages AS lp
        LEFT JOIN projects AS p ON lp.project_id = p.project_id
        WHERE lp.project_id = #{projectId}
          AND lp.delete_yn = 'N'
          <if test="status != null and status != ''">
            AND lp.status = #{status, typeHandler=org.apache.ibatis.type.EnumTypeHandler}
          </if>
          <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
            AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
          </if>
        <!-- 동적 정렬 -->
        <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
            ORDER BY
            <foreach item="order" collection="pageable.sort" separator=", ">
                <choose>
                    <when test="order.property == 'pageTitle'">lp.page_title</when>
                    <when test="order.property == 'createDate'">lp.create_date</when>
                    <when test="order.property == 'lastUpdateDate'">lp.last_update_date</when>
                    <when test="order.property == 'status'">lp.status</when>
                    <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                    <otherwise>lp.landing_page_id</otherwise> <!-- 기본 정렬 기준 -->
                </choose>
                <choose>
                    <when test="order.direction.name() == 'ASC'">ASC</when>
                    <when test="order.direction.name() == 'DESC'">DESC</when>
                    <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                </choose>
            </foreach>
        </if>
        <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
            <!-- 기본 정렬: 생성일 내림차순 -->
            ORDER BY lp.create_date DESC
        </if>
        <!-- 페이징 -->
        <if test="pageable != null">
            LIMIT #{pageable.offset}, #{pageable.pageSize}
        </if>
    </select>

    <!-- 프로젝트 ID로 랜딩 페이지 개수 조회 -->
    <select id="countLandingPagesByProjectId" resultType="long">
        SELECT COUNT(*)
        FROM landing_pages AS lp
        LEFT JOIN projects AS p ON lp.project_id = p.project_id
        WHERE lp.project_id = #{projectId}
          AND lp.delete_yn = 'N'
          <if test="status != null and status != ''">
            AND lp.status = #{status}
          </if>
          <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
            AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
          </if>
    </select>

    <!-- 프로젝트 ID와 페이지 이름으로 랜딩 페이지 조회 (중복 체크용) -->
    <select id="selectLandingPageByProjectIdAndPageTitle" resultMap="LandingPageResultMap">
        SELECT *
        FROM landing_pages
        WHERE project_id = #{projectId}
          AND page_title = #{pageTitle}
          AND delete_yn = 'N'
    </select>

    <!-- 랜딩 페이지 수정 -->
    <update id="updateLandingPage" parameterType="kr.wayplus.wayplus_qr.entity.LandingPage">
        UPDATE landing_pages
        SET
            page_title = #{pageTitle},
            description = #{description},
            content_json = #{contentJson, jdbcType=VARCHAR},
            status = #{status, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
            valid_from_date = #{validFromDate},
            valid_to_date = #{validToDate},
            update_user_email = #{updateUserEmail},
            last_update_date = NOW()
        WHERE landing_page_id = #{landingPageId}
          AND delete_yn = 'N'
    </update>

    <!-- 랜딩 페이지 논리적 삭제 -->
    <update id="logicalDeleteLandingPage">
        UPDATE landing_pages
        SET
            delete_yn = 'Y',
            use_yn = 'N',
            delete_user_email = #{userEmail},
            delete_date = NOW()
        WHERE landing_page_id = #{landingPageId}
          AND delete_yn = 'N'
    </update>

    <!-- SUPER_ADMIN: 모든 랜딩 페이지 목록 조회 (페이징 및 동적 정렬) -->
    <select id="selectAllLandingPagesForSuperAdmin" resultMap="LandingPageResultMap">
        SELECT
            lp.landing_page_id     AS landing_page_id,
            lp.project_id          AS project_id,
            p.project_name         AS project_name,
            lp.page_title          AS page_title,
            lp.description         AS description,
            lp.content_json        AS content_json,
            lp.status              AS status,
            lp.valid_from_date     AS valid_from_date,
            lp.valid_to_date       AS valid_to_date,
            lp.create_user_email   AS create_user_email,
            lp.create_date         AS create_date,
            lp.update_user_email   AS update_user_email,
            lp.last_update_date    AS last_update_date,
            lp.delete_user_email   AS delete_user_email,
            lp.delete_date         AS delete_date,
            lp.use_yn              AS use_yn,
            lp.delete_yn           AS delete_yn
        FROM landing_pages lp
        LEFT JOIN projects p ON lp.project_id = p.project_id
        WHERE lp.delete_yn = 'N'
        <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
            AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
        </if>
        <!-- Pageable을 사용한 동적 정렬 -->
        <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
            ORDER BY
            <foreach item="order" collection="pageable.sort" separator=", ">
                <choose>
                    <when test="order.property == 'pageTitle'">lp.page_title</when>
                    <when test="order.property == 'validFromDate'">lp.valid_from_date</when>
                    <when test="order.property == 'validToDate'">lp.valid_to_date</when>
                    <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                    <otherwise>lp.landing_page_id</otherwise> <!-- 기본 정렬 기준 -->
                </choose>
                <choose>
                    <when test="order.direction.name() == 'ASC'">ASC</when>
                    <when test="order.direction.name() == 'DESC'">DESC</when>
                    <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                </choose>
            </foreach>
        </if>
        <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
            <!-- 기본 정렬: 랜딩 페이지 ID 내림차순 -->
            ORDER BY lp.landing_page_id DESC
        </if>
        <!-- Pageable을 사용한 페이징 -->
        <if test="pageable != null">
            LIMIT #{pageable.offset}, #{pageable.pageSize}
        </if>
    </select>

    <!-- SUPER_ADMIN: 특정 프로젝트의 랜딩 페이지 목록 조회 (페이징 및 동적 정렬) -->
    <select id="selectLandingPagesByProjectIdForSuperAdmin" resultMap="LandingPageResultMap">
        SELECT
            lp.landing_page_id     AS landing_page_id,
            lp.project_id          AS project_id,
            p.project_name         AS project_name,
            lp.page_title          AS page_title,
            lp.description         AS description,
            lp.content_json        AS content_json,
            lp.status              AS status,
            lp.valid_from_date     AS valid_from_date,
            lp.valid_to_date       AS valid_to_date,
            lp.create_user_email   AS create_user_email,
            lp.create_date         AS create_date,
            lp.update_user_email   AS update_user_email,
            lp.last_update_date    AS last_update_date,
            lp.delete_user_email   AS delete_user_email,
            lp.delete_date         AS delete_date,
            lp.use_yn              AS use_yn,
            lp.delete_yn           AS delete_yn
        FROM landing_pages lp
        LEFT JOIN projects p ON lp.project_id = p.project_id
        WHERE lp.delete_yn = 'N'
            AND lp.project_id = #{projectId}
            <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
                AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
            </if>
            <!-- Pageable을 사용한 동적 정렬 -->
            <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
                ORDER BY
                <foreach item="order" collection="pageable.sort" separator=", ">
                    <choose>
                        <when test="order.property == 'pageTitle'">lp.page_title</when>
                        <when test="order.property == 'validFromDate'">lp.valid_from_date</when>
                        <when test="order.property == 'validToDate'">lp.valid_to_date</when>
                        <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                        <otherwise>lp.landing_page_id</otherwise> <!-- 기본 정렬 기준 -->
                    </choose>
                    <choose>
                        <when test="order.direction.name() == 'ASC'">ASC</when>
                        <when test="order.direction.name() == 'DESC'">DESC</when>
                        <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                    </choose>
                </foreach>
            </if>
            <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
                <!-- 기본 정렬: 랜딩 페이지 ID 내림차순 -->
                ORDER BY lp.landing_page_id DESC
            </if>
            <!-- Pageable을 사용한 페이징 -->
            <if test="pageable != null">
                LIMIT #{pageable.offset}, #{pageable.pageSize}
            </if>
    </select>

    <!-- SUPER_ADMIN: 특정 프로젝트의 랜딩 페이지 개수 조회 -->
    <select id="countLandingPagesByProjectIdForSuperAdmin" resultType="long">
        SELECT count(*)
        FROM landing_pages lp
        LEFT JOIN projects p ON lp.project_id = p.project_id
        WHERE lp.delete_yn = 'N'
          AND lp.project_id = #{projectId}
          <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
            AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
          </if>
    </select>

    <!-- SUPER_ADMIN: 모든 랜딩 페이지 개수 조회 -->
    <select id="countAllLandingPagesForSuperAdmin" resultType="long">
        SELECT count(*)
        FROM landing_pages lp
        LEFT JOIN projects p ON lp.project_id = p.project_id
        WHERE lp.delete_yn = 'N'
          <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
            AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
          </if>
    </select>

</mapper>
