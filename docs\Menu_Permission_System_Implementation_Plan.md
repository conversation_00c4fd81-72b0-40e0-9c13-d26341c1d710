# 메뉴별 사용자 권한 관리 시스템 구현 계획

## 📋 개요
관리자페이지에서 특정 메뉴의 상세페이지에서 사용자별 CRUD 권한을 관리하는 시스템

**생성일**: 2025-07-02
**대상**: 백엔드/프론트엔드 개발자

---

## 🎯 구현 목표

### 핵심 기능
**메뉴 상세페이지에서 사용자별 CRUD 권한 관리**
- 특정 메뉴 선택 → 해당 메뉴에 대한 모든 사용자의 CRUD 권한 조회/설정
- 개별 사용자별로 읽기(R), 쓰기(C), 수정(U), 삭제(D) 권한 개별 설정
- 설정된 권한에 따른 실제 API 접근 차단

### 사용 시나리오
1. 관리자가 "QR 관리" 메뉴 상세페이지 접근
2. 해당 메뉴에 대한 모든 사용자의 현재 권한 상태 확인
3. 특정 사용자의 권한 수정 (예: A사용자는 읽기만, B사용자는 읽기+수정만)
4. 설정 저장 후 실제 API 호출 시 권한에 따라 접근 제어

---

## 🗄️ 데이터베이스 구조

### 필요한 테이블
**DDL 파일**: `src/main/resources/sql/DDL/menu_permission_system_DDL.sql`

### 권한 체크 방식
- **어노테이션 기반**: `@RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = "READ")`
- **메뉴 코드 활용**: 기존 `menu_code`를 사용하여 권한 체크

---

## 🔧 백엔드 구현 계획

### 1단계: 기본 구조 (1주)
- [ ] 기존 테이블에 CRUD 권한 컬럼 추가
- [ ] `@RequireMenuPermission` 어노테이션 생성
- [ ] MenuDto에 권한 응답 DTO 추가

### 2단계: 권한 관리 API (1주)
- [ ] 메뉴별 사용자 권한 조회 API
- [ ] 메뉴별 사용자 권한 설정/수정 API
- [ ] ManageMenuService에 권한 관련 메서드 추가

### 3단계: API 접근 제어 (1주)
- [ ] AOP Aspect 생성 (어노테이션 기반 권한 체크)
- [ ] 메뉴 코드 기반 권한 검증 로직
- [ ] 기존 API에 `@RequireMenuPermission` 어노테이션 적용

---

## 🌐 프론트엔드 API 가이드

### 기본 정보
- **Base URL**: `/manage/menus`
- **인증**: JWT Bearer Token 필요
- **Content-Type**: `application/json`

### API 권한 구분
- **SUPER_ADMIN 전용**: 메뉴 관리, 권한 설정 등 관리자 기능
- **로그인 사용자**: 본인의 접근 가능한 메뉴 조회

---

## 📡 API 엔드포인트 구성

### 🏗️ 1. 메뉴 기본 관리 API (SUPER_ADMIN 전용)
**기능**: 메뉴 생성, 수정, 삭제, 전체 조회 등 기본적인 메뉴 관리 기능

#### 1.1 신규 메뉴 생성
**기능**: 새로운 메뉴를 생성합니다. 메뉴 코드, 이름, URL, 계층구조를 설정할 수 있습니다.

```http
POST /manage/menus
```

#### 1.2 기존 메뉴 정보 수정
**기능**: 기존 메뉴의 이름, URL, 계층구조, 상태 등을 수정합니다.

```http
PUT /manage/menus/{menuId}
```

#### 1.3 메뉴 삭제
**기능**: 선택한 메뉴를 삭제합니다. 하위 메뉴가 있는 경우 삭제가 제한될 수 있습니다.

```http
DELETE /manage/menus/{menuId}
```

#### 1.4 전체 메뉴 트리 구조 조회
**기능**: 시스템의 모든 메뉴를 계층구조로 조회합니다. 메뉴 관리 화면에서 사용됩니다.

```http
GET /manage/menus/tree
```

---

### 🔐 2. 역할/사용자별 기본 접근 권한 API (SUPER_ADMIN 전용)
**기능**: 메뉴에 대한 기본적인 접근 권한 설정 (접근 가능/불가능)

#### 2.1 역할별 메뉴 접근 권한 설정
**기능**: 특정 역할(ROLE)이 특정 메뉴에 접근할 수 있는지 설정합니다. 역할 기반 기본 권한을 관리합니다.

```http
POST /manage/menus/{menuId}/roles/{roleId}/permissions?isAccessible=Y
```

#### 2.2 개별 사용자 메뉴 접근 권한 설정
**기능**: 특정 사용자가 특정 메뉴에 접근할 수 있는지 개별적으로 설정합니다. 역할 권한보다 우선 적용됩니다.

```http
POST /manage/menus/{menuId}/users/{userEmail}/permissions
```

---

### 👥 3. 사용자별 메뉴 조회 API
**기능**: 사용자가 접근 가능한 메뉴 조회 및 권한 확인

#### 3.1 내 접근 가능 메뉴 조회 (로그인 사용자)
**기능**: 현재 로그인한 사용자가 접근 가능한 메뉴 트리를 조회합니다. 프론트엔드 네비게이션 메뉴 구성에 사용됩니다.

```http
GET /manage/menus/accessible
```

#### 3.2 특정 사용자의 접근 가능 메뉴 조회 (관리자 전용)
**기능**: 관리자가 특정 사용자의 접근 가능한 메뉴 트리를 조회합니다. 사용자 권한 확인 및 디버깅에 사용됩니다.

```http
GET /manage/menus/users/{userEmail}/accessible
```

#### 3.3 사용자별 메뉴 접근 권한 검증 (관리자 전용)
**기능**: 특정 사용자가 특정 메뉴에 접근 가능한지 확인합니다. 권한 설정 후 검증 및 디버깅에 사용됩니다.

```http
GET /manage/menus/{menuId}/users/{userEmail}/access
```

---

### 🎯 4. 메뉴별 사용자 CRUD 권한 관리 API (SUPER_ADMIN 전용)
**기능**: 특정 메뉴에 대한 사용자별 세부 CRUD 권한 관리 (메뉴 상세페이지 핵심 기능)

#### 4.1 메뉴별 사용자 CRUD 권한 목록 조회
**기능**: 특정 메뉴에 대한 모든 사용자의 CRUD 권한을 조회합니다. 메뉴 상세 페이지에서 권한 관리 테이블을 구성할 때 사용됩니다.

```http
GET /manage/menus/{menuId}/permissions/users
```

**경로 변수**:
- `menuId`: 메뉴 ID

**응답 예시**:
```json
{
  "success": true,
  "data": [
    {
      "userEmail": "<EMAIL>",
      "userName": "사용자A",
      "roleId": "SUB_ADMIN",
      "roleName": "서브 관리자",
      "canRead": "Y",
      "canWrite": "N",
      "canUpdate": "N",
      "canDelete": "N",
      "permissionNote": "읽기 전용",
      "permissionSource": "default",
      "lastUpdateDate": "2025-01-01 10:00:00"
    },
    {
      "userEmail": "<EMAIL>",
      "userName": "사용자B",
      "roleId": "VIEWER",
      "roleName": "뷰어",
      "canRead": "Y",
      "canWrite": "N",
      "canUpdate": "Y",
      "canDelete": "Y",
      "permissionNote": "QR 관리 특별 권한",
      "permissionSource": "custom",
      "lastUpdateDate": "2025-01-01 10:00:00"
    }
  ],
  "message": "성공적으로 조회되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

**permissionSource 값**:
- `default`: 기본 권한 (역할 기반 또는 전역 기본값)
- `custom`: 개별 설정된 권한

#### 4.2 메뉴별 사용자 CRUD 권한 설정/수정
**기능**: 특정 메뉴에 대한 개별 사용자의 CRUD 권한을 설정하거나 수정합니다. 읽기(R), 생성(C), 수정(U), 삭제(D) 권한을 개별적으로 제어할 수 있습니다.

```http
POST /manage/menus/{menuId}/permissions/users
```

**요청 예시**:
```json
{
  "userEmail": "<EMAIL>",
  "isAccessible": "Y",
  "canRead": "Y",
  "canWrite": "N",
  "canUpdate": "Y",
  "canDelete": "Y",
  "permissionNote": "QR 관리 특별 권한 부여"
}
```

**응답 예시**:
```json
{
  "success": true,
  "data": null,
  "message": "메뉴 사용자 권한이 성공적으로 설정되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

#### 4.3 메뉴별 사용자 권한 삭제 (기본값 복원)
**기능**: 특정 메뉴의 개별 사용자 권한을 삭제하여 기본 권한(역할 기반)으로 복원합니다.

```http
DELETE /manage/menus/{menuId}/permissions/users/{userEmail}
```

**응답 예시**:
```json
{
  "success": true,
  "data": "메뉴 사용자 권한이 삭제되어 기본값으로 복원되었습니다.",
  "message": "성공적으로 처리되었습니다.",
  "timestamp": "2025-01-01T10:00:00"
}
```

---

## 🎨 프론트엔드 구현 가이드

### API 사용 시나리오별 가이드

#### 시나리오 1: 메뉴 관리 페이지 구성
1. **전체 메뉴 트리 조회**: `GET /manage/menus/tree`
2. **메뉴 생성**: `POST /manage/menus`
3. **메뉴 수정**: `PUT /manage/menus/{menuId}`
4. **메뉴 삭제**: `DELETE /manage/menus/{menuId}`

#### 시나리오 2: 사용자 네비게이션 메뉴 구성
1. **내 접근 가능 메뉴 조회**: `GET /manage/menus/accessible`
2. 응답받은 메뉴 트리로 네비게이션 구성

#### 시나리오 3: 메뉴별 사용자 권한 관리 (메뉴 상세페이지)
1. **사용자 권한 목록 조회**: `GET /manage/menus/{menuId}/permissions/users`
2. **개별 권한 설정**: `POST /manage/menus/{menuId}/permissions/users`
3. **권한 삭제 (기본값 복원)**: `DELETE /manage/menus/{menuId}/permissions/users/{userEmail}`

#### 시나리오 4: 기본 접근 권한 설정
1. **역할별 접근 권한**: `POST /manage/menus/{menuId}/roles/{roleId}/permissions`
2. **개별 사용자 접근 권한**: `POST /manage/menus/{menuId}/users/{userEmail}/permissions`

### 메뉴 상세페이지 UI 구조
```
📋 QR 관리 메뉴 상세
├── 📊 메뉴 기본 정보 (메뉴명, 코드, 설명)
└── 👥 사용자별 권한 관리
    ├── 🔍 사용자 검색/필터
    ├── 📋 권한 테이블
    │   ├── 사용자명 | 이메일 | 역할 | R | C | U | D | 메모 | 액션
    │   └── 체크박스로 CRUD 권한 개별 설정
    └── 💾 일괄 저장 버튼
```

### 권한 관리 유틸리티 클래스
```javascript
class MenuPermissionManager {
  constructor(menuId) {
    this.menuId = menuId;
    this.baseUrl = '/manage/menus';
  }

  // 메뉴별 사용자 권한 목록 조회
  async getUserPermissions() {
    const response = await fetch(`${this.baseUrl}/${this.menuId}/permissions/users`);
    return response.json();
  }

  // 개별 사용자 권한 설정
  async setUserPermission(userEmail, permissions) {
    const response = await fetch(`${this.baseUrl}/${this.menuId}/permissions/users`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        userEmail: userEmail,
        isAccessible: permissions.isAccessible,
        canRead: permissions.canRead,
        canWrite: permissions.canWrite,
        canUpdate: permissions.canUpdate,
        canDelete: permissions.canDelete,
        permissionNote: permissions.permissionNote
      })
    });
    return response.json();
  }

  // 사용자 권한 삭제 (기본값 복원)
  async deleteUserPermission(userEmail) {
    const response = await fetch(`${this.baseUrl}/${this.menuId}/permissions/users/${userEmail}`, {
      method: 'DELETE'
    });
    return response.json();
  }

  // 내 접근 가능 메뉴 조회 (네비게이션용)
  static async getMyAccessibleMenus() {
    const response = await fetch('/manage/menus/accessible');
    return response.json();
  }

  // 전체 메뉴 트리 조회 (관리자용)
  static async getAllMenuTree() {
    const response = await fetch('/manage/menus/tree');
    return response.json();
  }
}
```

---

## 📊 API 구성 요약

### 현재 구현된 API 목록

| 분류 | HTTP Method | URL | 기능 | 권한 |
|------|-------------|-----|------|------|
| **메뉴 기본 관리** | POST | `/manage/menus` | 신규 메뉴 생성 | SUPER_ADMIN |
| | PUT | `/manage/menus/{menuId}` | 메뉴 정보 수정 | SUPER_ADMIN |
| | DELETE | `/manage/menus/{menuId}` | 메뉴 삭제 | SUPER_ADMIN |
| | GET | `/manage/menus/tree` | 전체 메뉴 트리 조회 | SUPER_ADMIN |
| **기본 접근 권한** | POST | `/manage/menus/{menuId}/roles/{roleId}/permissions` | 역할별 메뉴 접근 권한 설정 | SUPER_ADMIN |
| | POST | `/manage/menus/{menuId}/users/{userEmail}/permissions` | 개별 사용자 메뉴 접근 권한 설정 | SUPER_ADMIN |
| **사용자 메뉴 조회** | GET | `/manage/menus/accessible` | 내 접근 가능 메뉴 조회 | 로그인 사용자 |
| | GET | `/manage/menus/users/{userEmail}/accessible` | 특정 사용자 접근 가능 메뉴 조회 | SUPER_ADMIN |
| | GET | `/manage/menus/{menuId}/users/{userEmail}/access` | 사용자별 메뉴 접근 권한 검증 | SUPER_ADMIN |
| **CRUD 권한 관리** | GET | `/manage/menus/{menuId}/permissions/users` | 메뉴별 사용자 CRUD 권한 목록 조회 | SUPER_ADMIN |
| | POST | `/manage/menus/{menuId}/permissions/users` | 메뉴별 사용자 CRUD 권한 설정/수정 | SUPER_ADMIN |
| | DELETE | `/manage/menus/{menuId}/permissions/users/{userEmail}` | 메뉴별 사용자 권한 삭제 (기본값 복원) | SUPER_ADMIN |

### 어노테이션 기반 권한 체크 예시 (향후 구현 예정)
```java
// QR 관리 관련 API들
@GetMapping("/qr-codes/{id}")
@RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = "READ")
public ResponseEntity<?> getQrCode(@PathVariable Long id) { ... }

@PostMapping("/qr-codes")
@RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = "WRITE")
public ResponseEntity<?> createQrCode(...) { ... }

@PutMapping("/qr-codes/{id}")
@RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = "UPDATE")
public ResponseEntity<?> updateQrCode(...) { ... }

@DeleteMapping("/qr-codes/{id}")
@RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = "DELETE")
public ResponseEntity<?> deleteQrCode(...) { ... }
```

---

## 🚀 구현 순서 (간소화)

### Phase 1: 기반 구조 (1주)
1. 기존 테이블에 CRUD 권한 컬럼 추가
2. `@RequireMenuPermission` 어노테이션 생성
3. MenuDto 확장

### Phase 2: 메뉴별 사용자 권한 API (1주) ✅ **완료**
1. ✅ 메뉴별 사용자 권한 조회 API
2. ✅ 메뉴별 사용자 권한 설정/수정/삭제 API
3. ⏳ 일괄 설정 API (미구현)

### Phase 3: API 접근 제어 (1주)
1. AOP Aspect 구현 (어노테이션 기반)
2. 메뉴 코드 기반 권한 검증 로직
3. 기존 API에 어노테이션 적용 및 테스트

---

## 📝 주요 특징

### ✅ 현재 구현된 기능
- **메뉴 기본 관리**: 생성, 수정, 삭제, 트리 조회
- **기본 접근 권한**: 역할별/사용자별 메뉴 접근 가능 여부 설정
- **사용자 메뉴 조회**: 개인별 접근 가능한 메뉴 트리 조회
- **CRUD 권한 관리**: 메뉴별 사용자의 세부 CRUD 권한 관리

### 🎯 핵심 설계 원칙
- **단순성**: 복잡한 매핑 테이블 대신 직관적인 API 구조
- **계층적 권한**: 역할 기반 기본 권한 + 개별 사용자 권한 오버라이드
- **개발자 친화적**: 명확한 API 명명 규칙과 일관된 응답 구조
- **확장성**: 향후 어노테이션 기반 API 접근 제어 확장 가능


