package kr.wayplus.wayplus_qr.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import kr.wayplus.wayplus_qr.dto.response.QnaAnswerResponseDto;

@Mapper
public interface QnaAnswerMapper {
    
    // 답변 등록
    int insertAnswer(
        @Param("qnaQuestionId") Long qnaQuestionId,
        @Param("answerContent") String answerContent,
        @Param("createUserEmail") String createUserEmail
    );
    
    // 최근 등록된 답변의 ID 조회
    Long selectLastInsertId();
    
    // 특정 ID의 답변 조회
    QnaAnswerResponseDto selectAnswerById(@Param("qnaAnswerId") Long qnaAnswerId);
    
    // 특정 질문에 대한 답변 조회
    QnaAnswerResponseDto selectAnswerByQuestionId(@Param("qnaQuestionId") Long qnaQuestionId);
    
    // 특정 질문에 대한 답변 존재 여부 체크
    int countAnswerByQuestionId(@Param("qnaQuestionId") Long qnaQuestionId);
    
    // 답변 수정
    int updateAnswer(
        @Param("qnaAnswerId") Long qnaAnswerId,
        @Param("answerContent") String answerContent,
        @Param("updateUserEmail") String updateUserEmail
    );
    
    // 답변 삭제 (논리적 삭제)
    int deleteAnswer(
        @Param("qnaAnswerId") Long qnaAnswerId,
        @Param("deleteUserEmail") String deleteUserEmail
    );
}
