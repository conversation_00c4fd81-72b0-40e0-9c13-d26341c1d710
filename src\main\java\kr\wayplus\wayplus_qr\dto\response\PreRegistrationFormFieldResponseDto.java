package kr.wayplus.wayplus_qr.dto.response;

import kr.wayplus.wayplus_qr.entity.PreRegistrationFormField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;

@Data
@AllArgsConstructor
@Builder
public class PreRegistrationFormFieldResponseDto {
    private Long fieldId;
    private Long formId;
    private String fieldLabel;
    private String fieldName;
    private String fieldType;
    private String isRequiredYn;
    private String helpText;
    private String options;
    private Integer displayOrder;
    private String createUserEmail;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createDate;
    private String updateUserEmail;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime lastUpdateDate;

    public static PreRegistrationFormFieldResponseDto fromEntity(PreRegistrationFormField e) {
        return PreRegistrationFormFieldResponseDto.builder()
                .fieldId(e.getFieldId())
                .formId(e.getFormId())
                .fieldLabel(e.getFieldLabel())
                .fieldName(e.getFieldName())
                .fieldType(e.getFieldType())
                .isRequiredYn(e.getIsRequiredYn())
                .helpText(e.getHelpText())
                .options(e.getOptions())
                .displayOrder(e.getDisplayOrder())
                .createUserEmail(e.getCreateUserEmail())
                .createDate(e.getCreateDate())
                .updateUserEmail(e.getUpdateUserEmail())
                .lastUpdateDate(e.getLastUpdateDate())
                .build();
    }
}
