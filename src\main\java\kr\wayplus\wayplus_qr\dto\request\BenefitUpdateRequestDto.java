package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BenefitUpdateRequestDto {

    @Size(max = 100)
    private String benefitName;

    @Size(max = 255)
    private String description;

    // NULL == 무제한, 0 이상
    @Min(0)
    private Integer quantity;

    // ACTIVE or INACTIVE (필요 시)
    private String status;
}
