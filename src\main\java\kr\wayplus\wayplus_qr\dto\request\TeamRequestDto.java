package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

/**
 * Team Create Request DTO
 * 
 * 팀 생성 요청 데이터 전송 객체
 * 
 * <AUTHOR> Name]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeamRequestDto {
    
    @NotNull(message = "이벤트 선택은 필수입니다.")
    private Long eventId;
    @NotBlank(message = "팀명은 필수입니다.")
    @Size(max = 255, message = "팀명은 255자를 초과할 수 없습니다.")
    private String teamName;
    @Size(max = 20, message = "팀 코드는 20자를 초과할 수 없습니다.")
    private String teamCode;
    private String teamStatus;
    private String description;
    private Integer maxMembers;
    private Long joinQrCodeId;
    private Long leaderAttendeeId;
    private String leaderName;
    private String leaderPhone;
    private String profileImgPath;
    private MultipartFile profileImageFile;
}