package kr.wayplus.wayplus_qr.exception;

import org.springframework.security.core.AuthenticationException;

/**
 * 사용자가 초기 비밀번호를 변경해야 할 때 발생하는 예외입니다.
 * AuthenticationException을 상속받아 Spring Security 처리 흐름에 통합될 수 있도록 합니다.
 */
public class PasswordChangeRequiredException extends AuthenticationException {

    private final String userEmail; // 필요시 사용자 이메일 정보 포함 가능

    public PasswordChangeRequiredException(String msg, String userEmail) {
        super(msg);
        this.userEmail = userEmail;
    }

    public PasswordChangeRequiredException(String msg) {
        super(msg);
        this.userEmail = null; // 이메일 정보가 없는 경우
    }

    public String getUserEmail() {
        return userEmail;
    }
}
