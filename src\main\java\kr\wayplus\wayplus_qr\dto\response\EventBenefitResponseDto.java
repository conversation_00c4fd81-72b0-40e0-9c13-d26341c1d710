package kr.wayplus.wayplus_qr.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import kr.wayplus.wayplus_qr.entity.EventBenefit;
import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class EventBenefitResponseDto {
    private Long eventBenefitId;
    private Long eventId;
    private String benefitCode;
    private String benefitName;
    private String description;
    private Integer quantity; // 총 수량
    private Integer redeemedQuantity; // 사용된 수량 (이 필드는 필요 시 추가. 현재 EventBenefit 엔티티에는 없음)
    private String status;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime createDate;

    public static EventBenefitResponseDto fromEntity(EventBenefit eventBenefit) {
        if (eventBenefit == null) {
            return null;
        }
        return EventBenefitResponseDto.builder()
                .eventBenefitId(eventBenefit.getBenefitId())
                .eventId(eventBenefit.getEventId())
                .benefitCode(eventBenefit.getBenefitCode())
                .benefitName(eventBenefit.getBenefitName())
                .description(eventBenefit.getDescription())
                .quantity(eventBenefit.getQuantity())
                .redeemedQuantity(eventBenefit.getRedeemedQuantity()) 
                .status(eventBenefit.getStatus())
                .createDate(eventBenefit.getCreateDate())
                .build();
    }
}
