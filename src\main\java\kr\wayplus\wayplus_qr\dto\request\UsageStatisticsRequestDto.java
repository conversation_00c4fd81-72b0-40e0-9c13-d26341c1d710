package kr.wayplus.wayplus_qr.dto.request;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;

@Getter
@Setter
@ToString
public class UsageStatisticsRequestDto {

    @NotBlank(message = "시작일은 필수입니다.")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "시작일은 YYYY-MM-DD 형식이어야 합니다.")
    private String startDate;

    @NotBlank(message = "종료일은 필수입니다.")
    @Pattern(regexp = "^\\d{4}-\\d{2}-\\d{2}$", message = "종료일은 YYYY-MM-DD 형식이어야 합니다.")
    private String endDate;

    private Long projectId;
}
