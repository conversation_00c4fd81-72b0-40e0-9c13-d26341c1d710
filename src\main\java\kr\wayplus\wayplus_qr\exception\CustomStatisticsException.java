package kr.wayplus.wayplus_qr.exception;

import lombok.Getter;

@Getter
public class CustomStatisticsException extends RuntimeException {

    private final ErrorCode errorCode;

    public CustomStatisticsException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public CustomStatisticsException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}
