package kr.wayplus.wayplus_qr.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import kr.wayplus.wayplus_qr.dto.response.EventBenefitResponseDto;
import kr.wayplus.wayplus_qr.entity.Event;
import kr.wayplus.wayplus_qr.entity.EventStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventResponseDto {
    private Long teamId;
    private Long eventId;
    private Long projectId;
    private String eventName;
    private String teamName;
    private String teamCode;
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime startDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime endDate;

    private String location;
    private Integer participantLimit;
    private Long preRegistrationFormId;
    private String preRegistrationFormName;
    private Long linkedQrCodeId;
    private String qrName;
    private Integer linkedQrCodeCount;
    private EventStatus status;
    private String createUserEmail;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime createDate;

    private String eventImagePath;

    private List<EventBenefitResponseDto> benefits;

    public static EventResponseDto fromEntity(Event event) {
        if (event == null) {
            return null;
        }
        return EventResponseDto.builder()
                .eventId(event.getEventId())
                .projectId(event.getProjectId())
                .eventName(event.getEventName())
                .teamName(event.getTeamName())
                .description(event.getDescription())
                .startDate(event.getStartDate())
                .endDate(event.getEndDate())
                .location(event.getLocation())
                .participantLimit(event.getParticipantLimit())
                .preRegistrationFormId(event.getPreRegistrationFormId())
                .preRegistrationFormName(event.getPreRegistrationFormName())
                .linkedQrCodeId(event.getLinkedQrCodeId())
                .qrName(event.getQrName())
                .linkedQrCodeCount(event.getLinkedQrCodeCount())
                .status(event.getStatus())
                .eventImagePath(event.getEventImagePath())
                .createUserEmail(event.getCreateUserEmail())
                .createDate(event.getCreateDate())
                .build();
    }
}
