package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.request.LoginRequestDto;
import kr.wayplus.wayplus_qr.dto.request.RefreshTokenRequestDto;
import kr.wayplus.wayplus_qr.dto.response.TokenResponseDto;
import kr.wayplus.wayplus_qr.entity.RefreshToken;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.exception.ExpiredTokenException;
import kr.wayplus.wayplus_qr.exception.InvalidTokenException;
import kr.wayplus.wayplus_qr.exception.PasswordChangeRequiredException;
import kr.wayplus.wayplus_qr.mapper.RefreshTokenMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import kr.wayplus.wayplus_qr.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.DisabledException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthService {

    private final UserMapper userMapper;
    private final RefreshTokenMapper refreshTokenMapper;
    private final JwtUtil jwtUtil;
    private final PasswordEncoder passwordEncoder;

    /**
     * 사용자 로그인을 처리하고 JWT 토큰(Access, Refresh)을 발급합니다.
     *
     * @param loginRequestDto 로그인 요청 정보 (이메일, 비밀번호)
     * @return 토큰 정보 DTO
     * @throws AuthenticationException 인증 실패 시 발생
     */
    @Transactional
    public TokenResponseDto login(LoginRequestDto loginRequestDto) {
        log.debug("Attempting login for user: {}", loginRequestDto.getUserEmail());

        // 1. 사용자 조회
        User user = userMapper.selectUserByEmail(loginRequestDto.getUserEmail())
                .orElseThrow(() -> {
                    log.warn("Login attempt failed - User not found: {}", loginRequestDto.getUserEmail());
                    return new BadCredentialsException("아이디 또는 비밀번호가 잘못되었습니다.");
                });

        // 2. 비밀번호 검증
        if (!passwordEncoder.matches(loginRequestDto.getPassword(), user.getPassword())) {
            log.warn("Login attempt failed - Invalid password for user: {}", loginRequestDto.getUserEmail());
            throw new BadCredentialsException("아이디 또는 비밀번호가 잘못되었습니다.");
        }

        // 2.1. 사용자 활성화 상태 검증
        if (!user.isEnabled()) {
            log.warn("Login attempt failed - User account is disabled: {}", loginRequestDto.getUserEmail());
            throw new DisabledException("비활성화된 계정입니다.");
        }

        // 2.2. 초기 비밀번호 여부 확인
        if ("Y".equals(user.getInitialPasswordYn())) {
            log.info("Password change required for user: {}", user.getUserEmail());
            // 초기 비밀번호 변경 필요 시 예외 발생
            throw new PasswordChangeRequiredException("최초 로그인입니다. 비밀번호를 변경해야 합니다.", user.getUserEmail());
        }

        // 3. 인증 성공 - JWT 및 Refresh Token 생성
        Authentication authentication = new UsernamePasswordAuthenticationToken(
                user, null, user.getAuthorities()
        );

        TokenResponseDto tokenResponseDto = generateTokens(authentication, user);

        // 4. Refresh Token 저장 또는 업데이트
        saveOrUpdateRefreshToken(user.getUserEmail(), tokenResponseDto.getRefreshToken());

        log.info("Login successful for user: {}", user.getUserEmail());
        return tokenResponseDto;
    }

    private TokenResponseDto generateTokens(Authentication authentication, User user) {
        String accessToken = jwtUtil.createAccessToken(authentication);
        String refreshTokenValue = jwtUtil.createRefreshToken(authentication);

        return TokenResponseDto.builder()
                .tokenType("Bearer")
                .accessToken(accessToken)
                .refreshToken(refreshTokenValue)
                .accessTokenExpiresIn(jwtUtil.getAccessTokenExpirationSeconds())
                .build();
    }

    private void saveOrUpdateRefreshToken(String userEmail, String refreshTokenValue) {
        Optional<RefreshToken> existingToken = refreshTokenMapper.selectRefreshTokenByUserEmail(userEmail);

        LocalDateTime expiryDateTime = LocalDateTime.now().plus(jwtUtil.getRefreshTokenExpirationMillis(), ChronoUnit.MILLIS);

        if (existingToken.isPresent()) {
            RefreshToken refreshTokenEntity = existingToken.get();
            refreshTokenEntity.updateToken(refreshTokenValue, expiryDateTime);
            refreshTokenMapper.update(refreshTokenEntity);
            log.info("Updated refresh token for user: {}", userEmail);
        } else {
            RefreshToken refreshTokenEntity = RefreshToken.builder()
                    .userEmail(userEmail)
                    .token(refreshTokenValue)
                    .expiryDate(expiryDateTime)
                    .build();
            refreshTokenMapper.save(refreshTokenEntity);
            log.info("Saved new refresh token for user: {}", userEmail);
        }
    }

    /**
     * Refresh Token을 사용하여 새로운 Access Token을 발급합니다.
     *
     * @param refreshTokenRequestDto Refresh Token 요청 정보
     * @return 새로운 토큰 정보 DTO
     * @throws RuntimeException 유효하지 않은 토큰 또는 기타 오류 발생 시
     */
    @Transactional
    public TokenResponseDto refreshToken(RefreshTokenRequestDto refreshTokenRequestDto) {
        String requestRefreshToken = refreshTokenRequestDto.getRefreshToken();
        log.info("Attempting to refresh token");

        // 1. Refresh Token 유효성 검증 (JWT 자체 유효성)
        if (!jwtUtil.validateToken(requestRefreshToken)) {
            log.warn("Invalid refresh token received");
            throw new InvalidTokenException("유효하지 않은 Refresh Token 입니다.");
        }

        // 2. 토큰에서 사용자 이메일 추출
        String userEmail = jwtUtil.getUserEmailFromToken(requestRefreshToken);
        log.info("Extracted userEmail from refresh token: {}", userEmail);

        // 3. DB에서 해당 사용자의 Refresh Token 조회 및 비교 검증
        RefreshToken storedRefreshToken = refreshTokenMapper.selectByToken(requestRefreshToken)
                .orElseThrow(() -> {
                    log.warn("Refresh token not found in DB for token value");
                    return new InvalidTokenException("Refresh Token을 DB에서 찾을 수 없습니다.");
                });

        // 4. DB 토큰 만료 시간 검증 (DB 기준)
        if (storedRefreshToken.getExpiryDate().isBefore(LocalDateTime.now())) {
            log.warn("Refresh token expired (DB check) for user: {}", userEmail);
            refreshTokenMapper.deleteByToken(requestRefreshToken);
            throw new ExpiredTokenException("Refresh Token이 만료되었습니다.");
        }

        // 5. 사용자 정보 로드 (새 Access Token 생성을 위해)
        User user = userMapper.selectUserByEmail(userEmail)
                .orElseThrow(() -> new UsernameNotFoundException("사용자를 찾을 수 없습니다: " + userEmail));

        // User 객체로부터 Authentication 객체 생성 (주의: 비밀번호 없이 생성)
        Authentication authentication = new UsernamePasswordAuthenticationToken(
                user, null, user.getAuthorities()
        );

        // 6. 새로운 Access Token 생성
        String newAccessToken = jwtUtil.createAccessToken(authentication);
        log.info("New access token generated for user: {}", userEmail);

        // 7. (선택적) Refresh Token Rotation: 새로운 Refresh Token 발급 및 DB 업데이트
        // String newRefreshTokenValue = jwtUtil.createRefreshToken(authentication);
        // storedRefreshToken.updateToken(newRefreshTokenValue, LocalDateTime.now().plusSeconds(jwtUtil.getAccessTokenExpirationSeconds() * 2));
        // refreshTokenMapper.update(storedRefreshToken);
        // log.info("Rotated refresh token for user: {}", userEmail);

        // 현재는 기존 Refresh Token을 그대로 반환 (Rotation 미적용)
        String currentRefreshToken = storedRefreshToken.getToken();

        // 8. 새로운 TokenResponseDto 생성 및 반환
        return TokenResponseDto.builder()
                .tokenType("Bearer")
                .accessToken(newAccessToken)
                .refreshToken(currentRefreshToken)
                .expiresIn(jwtUtil.getAccessTokenExpirationSeconds())
                .build();
    }

    /**
     * 사용자 로그아웃 처리 (Refresh Token 삭제)
     *
     * @param userEmail 로그아웃할 사용자의 이메일
     */
    @Transactional
    public void logout(String userEmail) {
        int deletedCount = refreshTokenMapper.deleteByUserEmail(userEmail);
        if (deletedCount > 0) {
            log.info("Refresh token deleted for user during logout: {}", userEmail);
        } else {
            log.warn("No refresh token found to delete for user during logout: {}", userEmail);
        }
    }
}
