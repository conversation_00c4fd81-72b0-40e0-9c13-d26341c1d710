<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.EventMapper">

    <resultMap id="EventResultMap" type="kr.wayplus.wayplus_qr.entity.Event">
        <id property="eventId" column="event_id"/>
        <result property="teamId" column="team_id"/>
        <result property="projectId" column="project_id"/>
        <result property="eventName" column="event_name"/>
        <result property="description" column="description"/>
        <result property="startDate" column="start_date"/>
        <result property="endDate" column="end_date"/>
        <result property="location" column="location"/>
        <result property="participantLimit" column="participant_limit"/>
        <result property="preRegistrationFormId" column="pre_registration_form_id"/>
        <result property="preRegistrationFormName" column="pre_registration_form_name"/>
        <result property="linkedQrCodeId" column="linked_qr_code_id"/>
        <result property="qrName" column="qr_name"/>
        <result property="linkedQrCodeCount" column="linked_qr_code_count"/>
        <result property="eventImagePath" column="event_image_path"/>
        <result property="status" column="status"/>
        <result property="createUserEmail" column="create_user_email"/>
        <result property="createDate" column="create_date"/>
        <result property="updateUserEmail" column="update_user_email"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteUserEmail" column="delete_user_email"/>
        <result property="deleteDate" column="delete_date"/>
        <result property="useYn" column="use_yn"/>
        <result property="deleteYn" column="delete_yn"/>
    </resultMap>

    <insert id="insertEvent" parameterType="kr.wayplus.wayplus_qr.entity.Event" useGeneratedKeys="true" keyProperty="eventId">
        INSERT INTO events (
            project_id, team_id, event_name, description, start_date, end_date, location, participant_limit,
            pre_registration_form_id, linked_qr_code_id, status, event_image_path, create_user_email, create_date, use_yn, delete_yn
        ) VALUES (
            #{projectId}, #{teamId}, #{eventName}, #{description}, #{startDate}, #{endDate}, #{location}, #{participantLimit},
            #{preRegistrationFormId}, #{linkedQrCodeId}, #{status, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
            #{eventImagePath}, #{createUserEmail}, NOW(), 'Y', 'N'
        )
    </insert>

    <select id="selectEventById" resultMap="EventResultMap">
        SELECT
            e.*,
            prf.form_name as pre_registration_form_name,
            qc.qr_name as qr_name,
            t.team_name, t.team_code
        FROM
            events e
        LEFT JOIN
            pre_registration_forms prf ON e.pre_registration_form_id = prf.form_id
        LEFT JOIN 
            qr_codes qc ON e.linked_qr_code_id = qc.qr_code_id 
        LEFT JOIN 
            teams t ON e.team_id = t.team_id       
        WHERE
            e.event_id = #{eventId}
          AND e.delete_yn = 'N'
    </select>

    <select id="selectEvents" resultMap="EventResultMap">
        SELECT
            e.*, 
            prf.form_name as pre_registration_form_name,
            qc.qr_name as qr_name,
            COALESCE(qcc.qr_count,0) as linked_qr_code_count,
            t.team_name
        FROM
            events e
        LEFT JOIN
            pre_registration_forms prf ON e.pre_registration_form_id = prf.form_id
        LEFT JOIN 
            qr_codes qc ON e.linked_qr_code_id = qc.qr_code_id    
        LEFT JOIN (
            SELECT linked_event_id, COUNT(*) AS qr_count
            FROM qr_codes
            WHERE delete_yn = 'N'
            GROUP BY linked_event_id
        ) qcc ON qcc.linked_event_id = e.event_id
        LEFT JOIN 
            teams t ON e.team_id = t.team_id
        WHERE e.delete_yn = 'N'
          <if test="projectId != null">
            AND e.project_id = #{projectId}
          </if>
          <if test="status != null and status != ''">
            AND e.status = #{status}
          </if>
          <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
            AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
          </if>
        <!-- Pageable을 사용한 동적 정렬 -->
        <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
            ORDER BY
            <foreach item="order" collection="pageable.sort" separator=", ">
                <choose>
                    <when test="order.property == 'eventName'">e.event_name</when>
                    <when test="order.property == 'startDate'">e.start_date</when>
                    <when test="order.property == 'endDate'">e.end_date</when>
                    <when test="order.property == 'status'">e.status</when>
                    <when test="order.property == 'createDate'">e.create_date</when>
                    <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                    <otherwise>e.create_date</otherwise> <!-- 기본 정렬 기준 -->
                </choose>
                <choose>
                    <when test="order.direction.name() == 'ASC'">ASC</when>
                    <when test="order.direction.name() == 'DESC'">DESC</when>
                    <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                </choose>
            </foreach>
        </if>
        <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
            <!-- 기본 정렬: 생성일 내림차순 -->
            ORDER BY e.create_date DESC
        </if>
        <!-- Pageable을 사용한 페이징 -->
        <if test="pageable != null">
            LIMIT #{pageable.offset}, #{pageable.pageSize}
        </if>
    </select>

    <select id="countEvents" resultType="int">
        SELECT COUNT(*)
        FROM
            events e
        LEFT JOIN
            pre_registration_forms prf ON e.pre_registration_form_id = prf.form_id
        LEFT JOIN 
            qr_codes qc ON e.linked_qr_code_id = qc.qr_code_id    
        LEFT JOIN (
            SELECT linked_event_id, COUNT(*) AS qr_count
            FROM qr_codes
            WHERE delete_yn = 'N'
            GROUP BY linked_event_id
        ) qcc ON qcc.linked_event_id = e.event_id
        WHERE e.delete_yn = 'N'
          <if test="projectId != null">
            AND e.project_id = #{projectId}
          </if>
          <if test="status != null and status != ''">
            AND e.status = #{status}
          </if>
          <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
            AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
          </if>
    </select>

    <update id="updateEvent" parameterType="kr.wayplus.wayplus_qr.entity.Event">
        UPDATE events
        <set>
            team_id = #{teamId},
            event_name = #{eventName},
            description = #{description},
            start_date = #{startDate},
            end_date = #{endDate},
            location = #{location},
            participant_limit = #{participantLimit},
            pre_registration_form_id = #{preRegistrationFormId},
            linked_qr_code_id = #{linkedQrCodeId},
            event_image_path = #{eventImagePath},
            status = #{status, typeHandler=org.apache.ibatis.type.EnumTypeHandler},
            update_user_email = #{updateUserEmail},
            last_update_date = NOW()
        </set>
        WHERE event_id = #{eventId}
          AND delete_yn = 'N'
    </update>

    <update id="logicalDeleteEvent">
        UPDATE events
        SET
            delete_yn = 'Y',
            use_yn = 'N',
            delete_user_email = #{userEmail},
            delete_date = NOW()
        WHERE event_id = #{eventId}
          AND delete_yn = 'N'
    </update>

    <!-- 이벤트 총 개수 조회 -->
    <select id="selectTotalEventCount" resultType="long">
        SELECT COUNT(*) FROM events
    </select>

    <!-- 기간별 일일 이벤트 생성 수 조회 -->
    <select id="selectDailyEventCreationCounts" resultType="kr.wayplus.wayplus_qr.dto.response.DailyCountDto">
        SELECT
            DATE_FORMAT(create_date, '%Y-%m-%d') AS date,
            COUNT(*) AS count
        FROM
            events
        WHERE
            <![CDATA[
            DATE_FORMAT(create_date, '%Y-%m-%d') >= STR_TO_DATE(#{startDate}, '%Y-%m-%d')
          AND DATE_FORMAT(create_date, '%Y-%m-%d') <= STR_TO_DATE(#{endDate}, '%Y-%m-%d')
            ]]>
        GROUP BY
            DATE_FORMAT(create_date, '%Y-%m-%d')
        ORDER BY
            date
    </select>

    <!-- 기간별 일일 이벤트 생성 수 (프로젝트별) 조회 -->
    <select id="selectDailyEventCreationCountsByProject" resultType="kr.wayplus.wayplus_qr.dto.response.DailyCountDto">
        SELECT
            DATE_FORMAT(create_date, '%Y-%m-%d') AS date,
            COUNT(*) AS count
        FROM
            events
        WHERE
            project_id = #{projectId}
            <![CDATA[
            AND DATE_FORMAT(create_date, '%Y-%m-%d') >= STR_TO_DATE(#{startDate}, '%Y-%m-%d')
            AND DATE_FORMAT(create_date, '%Y-%m-%d') <= STR_TO_DATE(#{endDate}, '%Y-%m-%d')
            ]]>
        GROUP BY
            DATE_FORMAT(create_date, '%Y-%m-%d')
        ORDER BY
            date
    </select>

    <!-- ==================== SUPER ADMIN Queries ==================== -->

    <select id="selectAllEventsForSuperAdmin" parameterType="map" resultMap="EventResultMap">
        SELECT
            e.*,
            prf.form_name as pre_registration_form_name
        FROM
            events e
        LEFT JOIN
            pre_registration_forms prf ON e.pre_registration_form_id = prf.form_id
        WHERE
            e.delete_yn = 'N'
            <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
                AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
            </if>
            <!-- Pageable을 사용한 동적 정렬 -->
            <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
                ORDER BY
                <foreach item="order" collection="pageable.sort" separator=", ">
                    <choose>
                        <when test="order.property == 'eventName'">e.event_name</when>
                        <when test="order.property == 'description'">e.description</when>
                        <when test="order.property == 'startDate'">e.start_date</when>
                        <when test="order.property == 'endDate'">e.end_date</when>
                        <when test="order.property == 'preRegistrationFormName'">prf.form_name</when>
                        <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                        <otherwise>e.create_date</otherwise> <!-- 기본 정렬 기준 -->
                    </choose>
                    <choose>
                        <when test="order.direction.name() == 'ASC'">ASC</when>
                        <when test="order.direction.name() == 'DESC'">DESC</when>
                        <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                    </choose>
                </foreach>
            </if>
            <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
                <!-- 기본 정렬: 생성일 내림차순 -->
                ORDER BY e.create_date DESC
            </if>
            <!-- Pageable을 사용한 페이징 -->
            <if test="pageable != null">
                LIMIT #{pageable.offset}, #{pageable.pageSize}
            </if>
    </select>

    <select id="countAllEventsForSuperAdmin" resultType="long">
        SELECT
            COUNT(*)
        FROM
            events e
        WHERE
            e.delete_yn = 'N'
            <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
                AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
            </if>
    </select>

    <select id="selectEventsByProjectIdForSuperAdmin" parameterType="map" resultMap="EventResultMap">
        SELECT
            e.*,
            prf.form_name as pre_registration_form_name
        FROM
            events e
        LEFT JOIN
            pre_registration_forms prf ON e.pre_registration_form_id = prf.form_id
        WHERE
            e.project_id = #{projectId}
            AND e.delete_yn = 'N'
            <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
                AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
            </if>
            <!-- Pageable을 사용한 동적 정렬 -->
            <if test="pageable != null and pageable.sort != null and pageable.sort.isSorted()">
                ORDER BY
                <foreach item="order" collection="pageable.sort" separator=", ">
                    <choose>
                        <when test="order.property == 'eventName'">e.event_name</when>
                        <when test="order.property == 'description'">e.description</when>
                        <when test="order.property == 'startDate'">e.start_date</when>
                        <when test="order.property == 'endDate'">e.end_date</when>
                        <when test="order.property == 'preRegistrationFormName'">prf.form_name</when>
                        <!-- 다른 허용된 정렬 기준 컬럼 추가 -->
                        <otherwise>e.create_date</otherwise> <!-- 기본 정렬 기준 -->
                    </choose>
                    <choose>
                        <when test="order.direction.name() == 'ASC'">ASC</when>
                        <when test="order.direction.name() == 'DESC'">DESC</when>
                        <otherwise>DESC</otherwise> <!-- 기본 정렬 방향 -->
                    </choose>
                </foreach>
            </if>
            <if test="pageable != null and (pageable.sort == null or pageable.sort.isUnsorted())">
                <!-- 기본 정렬: 생성일 내림차순 -->
                ORDER BY e.create_date DESC
            </if>
            <!-- Pageable을 사용한 페이징 -->
            <if test="pageable != null">
                LIMIT #{pageable.offset}, #{pageable.pageSize}
            </if>
    </select>

    <select id="countEventsByProjectIdForSuperAdmin" parameterType="long" resultType="long">
        SELECT
            COUNT(*)
        FROM
            events e
        WHERE
            e.project_id = #{projectId}
            AND e.delete_yn = 'N'
            <if test="searchColumn != null and searchKeyword != null and searchKeyword != ''">
                AND ${searchColumn} LIKE CONCAT('%', #{searchKeyword}, '%')
            </if>
    </select>

    <!-- 이벤트의 사전 등록 양식 ID 업데이트 -->
    <update id="updateEventPreRegistrationFormId">
        UPDATE 
            events
        SET 
            pre_registration_form_id = #{preRegistrationFormId, jdbcType=BIGINT},
            update_user_email = #{userEmail, jdbcType=VARCHAR},
            last_update_date = NOW()
        WHERE 
            event_id = #{eventId, jdbcType=BIGINT}
    </update>
    
    <!-- 사전 등록 양식 조회 -->
    <select id="selectPreRegistrationFormById" resultType="java.util.HashMap">
        SELECT 
            form_id as formId,
            event_id as eventId,
            form_name as formName,
            description as formDescription,
            require_consent as requireConsent,
            auto_confirm_yn as autoConfirmYn,
            consent_text as consentText,
            max_attendees as maxAttendees,
            create_user_email as createUserEmail,
            create_date as createDate,
            update_user_email as updateUserEmail,
            last_update_date as lastUpdateDate
        FROM 
            pre_registration_forms
        WHERE 
            form_id = #{formId}
            AND delete_yn = 'N'
    </select>
    
    <!-- 사전 등록 양식 삽입 -->
    <insert id="insertPreRegistrationForm" parameterType="java.util.Map" useGeneratedKeys="true" keyProperty="formId" keyColumn="form_id">
        INSERT INTO pre_registration_forms (
            event_id,
            form_name,
            description,
            require_consent,
            auto_confirm_yn,
            consent_text,
            max_attendees,
            create_user_email,
            create_date,
            use_yn,
            delete_yn
        ) VALUES (
            #{eventId, jdbcType=BIGINT},
            #{formName, jdbcType=VARCHAR},
            #{formDescription, jdbcType=VARCHAR},
            #{requireConsent, jdbcType=VARCHAR},
            #{autoConfirmYn, jdbcType=VARCHAR},
            #{consentText, jdbcType=VARCHAR},
            #{maxAttendees, jdbcType=INTEGER},
            #{createUserEmail, jdbcType=VARCHAR},
            NOW(),
            'Y',
            'N'
        )
    </insert>
    
    <!-- 사전 등록 양식의 필드 목록 조회 -->
    <select id="selectFormFieldsByFormId" resultType="java.util.HashMap">
        SELECT 
            field_id as fieldId,
            form_id as formId,
            field_type as fieldType,
            field_name as fieldName,
            field_label as fieldLabel,
            placeholder,
            is_required as isRequired,
            display_order as displayOrder,
            create_user_email as createUserEmail,
            create_date as createDate
        FROM 
            form_fields
        WHERE 
            form_id = #{formId}
            AND delete_yn = 'N'
        ORDER BY
            display_order ASC
    </select>
    
    <!-- 이벤트 ID로 랜딩 페이지 조회 -->
    <select id="selectLandingPageByEventId" resultType="java.util.HashMap">
        SELECT 
            landing_page_id as landingPageId,
            event_id as eventId,
            custom_url as customUrl,
            status,
            create_user_email as createUserEmail,
            create_date as createDate,
            update_user_email as updateUserEmail,
            last_update_date as lastUpdateDate
        FROM 
            landing_pages
        WHERE 
            event_id = #{params.eventId}
            <if test="params.status != null">
            AND status = #{params.status}
            </if>
            AND delete_yn = 'N'
        LIMIT 1
    </select>
    
    <!-- 랜딩 페이지 삽입 -->
    <insert id="insertLandingPage" parameterType="java.util.Map" useGeneratedKeys="true" keyProperty="landingPageId" keyColumn="landing_page_id">
        INSERT INTO landing_pages (
            event_id,
            custom_url,
            status,
            create_user_email,
            create_date,
            use_yn,
            delete_yn
        ) VALUES (
            #{eventId, jdbcType=BIGINT},
            #{customUrl, jdbcType=VARCHAR},
            #{status, jdbcType=VARCHAR},
            #{createUserEmail, jdbcType=VARCHAR},
            NOW(),
            #{useYn, jdbcType=VARCHAR},
            #{deleteYn, jdbcType=VARCHAR}
        )
    </insert>
    
    <!-- 랜딩 페이지 ID로 버전 조회 -->
    <select id="selectLandingPageVersionsByLandingPageId" resultType="java.util.HashMap">
        SELECT 
            version_id as versionId,
            landing_page_id as landingPageId,
            version_number as versionNumber,
            content,
            is_published as isPublished,
            create_user_email as createUserEmail,
            create_date as createDate,
            update_user_email as updateUserEmail,
            last_update_date as lastUpdateDate
        FROM 
            landing_page_versions
        WHERE 
            landing_page_id = #{landingPageId}
            AND delete_yn = 'N'
        ORDER BY
            version_number ASC
    </select>
    
    <!-- 랜딩 페이지 버전 삽입 -->
    <insert id="insertLandingPageVersion" parameterType="java.util.Map" useGeneratedKeys="true" keyProperty="versionId" keyColumn="version_id">
        INSERT INTO landing_page_versions (
            landing_page_id,
            version_number,
            content,
            is_published,
            create_user_email,
            create_date,
            use_yn,
            delete_yn
        ) VALUES (
            #{landingPageId, jdbcType=BIGINT},
            #{versionNumber, jdbcType=INTEGER},
            #{content, jdbcType=VARCHAR},
            #{isPublished, jdbcType=VARCHAR},
            #{createUserEmail, jdbcType=VARCHAR},
            NOW(),
            #{useYn, jdbcType=VARCHAR},
            #{deleteYn, jdbcType=VARCHAR}
        )
    </insert>
    
    <!-- 프로젝트 ID로 랜딩 페이지 조회 (DDL 구조에 맞게 추가) -->
    <select id="selectLandingPageByProjectId" resultType="java.util.HashMap">
        SELECT 
            landing_page_id as landingPageId,
            project_id as projectId,
            page_title as pageTitle,
            description,
            content_json as contentJson,
            status,
            valid_from_date as validFromDate,
            valid_to_date as validToDate,
            create_user_email as createUserEmail,
            create_date as createDate,
            update_user_email as updateUserEmail,
            last_update_date as lastUpdateDate
        FROM 
            landing_pages
        WHERE 
            project_id = #{params.projectId}
            AND status = #{params.status}
            AND delete_yn = 'N'
        LIMIT 1
    </select>
    
    <!-- 폼 필드 삽입 - DDL 구조에 맞게 수정 -->
    <insert id="insertFormField" parameterType="java.util.Map" useGeneratedKeys="true" keyProperty="fieldId" keyColumn="field_id">
        INSERT INTO form_fields (
            form_id,
            field_type,
            field_name,
            field_label,
            help_text,
            is_required_yn,
            options,
            display_order,
            create_user_email,
            create_date,
            use_yn,
            delete_yn
        ) VALUES (
            #{formId, jdbcType=BIGINT},
            #{fieldType, jdbcType=VARCHAR},
            #{fieldName, jdbcType=VARCHAR},
            #{fieldLabel, jdbcType=VARCHAR},
            #{helpText, jdbcType=VARCHAR},
            #{isRequiredYn, jdbcType=VARCHAR},
            #{options, jdbcType=VARCHAR},
            #{displayOrder, jdbcType=INTEGER},
            #{createUserEmail, jdbcType=VARCHAR},
            NOW(),
            #{useYn, jdbcType=VARCHAR},
            #{deleteYn, jdbcType=VARCHAR}
        )
    </insert>
</mapper>
