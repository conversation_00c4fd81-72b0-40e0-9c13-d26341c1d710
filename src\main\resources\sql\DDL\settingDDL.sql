CREATE TABLE users (
    user_idx        BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '사용자 내부 고유 번호 (PK, 시스템 내부 식별용)',
    user_email      VARCHAR(255) NOT NULL UNIQUE COMMENT '사용자 아이디 (로그인용 이메일, 외부 테이블 참조용 키)',
    name       VARCHAR(100) NOT NULL COMMENT '사용자 이름',
    password        VARCHAR(255) NOT NULL COMMENT '비밀번호 (해싱됨)',
    role_id         VARCHAR(50) NOT NULL COMMENT '사용자 역할 ID (FK)',
    description     TEXT COMMENT '사용자 설명',
    contact_number  VARCHAR(50) COMMENT '연락처',
    status          ENUM('ACTIVE', 'INACTIVE', 'INVITED', 'LOCKED') NOT NULL DEFAULT 'ACTIVE' COMMENT '계정 상태',
    initial_password_yn ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '초기 비밀번호 사용 여부 (변경 필요 여부)',
    can_approve_exchange_qr_yn ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '교환권 QR 승인 가능 여부',
    last_login_date DATETIME COMMENT '최종 로그인 일시',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    login_fail_count int(11) DEFAULT 0 COMMENT '로그인 실패 횟수',
    user_token_id varchar(50) DEFAULT NULL COMMENT '사용자 고유 토큰값',
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '시스템 사용자 (관리자, 등록 사용자 포함)';

CREATE INDEX idx_users_role_id ON users(role_id);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_delete_yn ON users(delete_yn);

CREATE TABLE manage_menus (
    menu_id         BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '메뉴 고유 번호 (PK)',
    parent_menu_id  BIGINT UNSIGNED NULL COMMENT '상위 메뉴 ID (FK, 최상위 메뉴인 경우 NULL)',
    menu_code       VARCHAR(100) NOT NULL COMMENT '메뉴 코드 (영문/숫자, 예: DASHBOARD, QR_MANAGEMENT)',
    menu_name       VARCHAR(255) NOT NULL COMMENT '메뉴 이름 (화면에 표시될 이름)',
    menu_url        VARCHAR(500) COMMENT '메뉴 URL 경로 (프론트엔드 라우팅용)',
    menu_icon       VARCHAR(100) COMMENT '메뉴 아이콘 클래스명 또는 경로',
    menu_level      INT UNSIGNED NOT NULL DEFAULT 1 COMMENT '메뉴 레벨 (1: 최상위, 2: 2단계, 3: 3단계)',
    display_order   INT UNSIGNED DEFAULT 0 COMMENT '메뉴 표시 순서 (같은 레벨 내에서)',
    status          ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '메뉴 상태 (ACTIVE: 활성화/표시, INACTIVE: 비활성화/숨김)',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    FOREIGN KEY (parent_menu_id) REFERENCES manage_menus(menu_id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    CONSTRAINT uk_menu_code_delete_yn UNIQUE (menu_code, delete_yn),
    CONSTRAINT uk_menu_name_delete_yn UNIQUE (menu_name, delete_yn)
) COMMENT '관리자 시스템 메뉴 정의 (SUPER_ADMIN 관리)';

CREATE INDEX idx_manage_menus_parent_id ON manage_menus(parent_menu_id);
CREATE INDEX idx_manage_menus_menu_code ON manage_menus(menu_code);
CREATE INDEX idx_manage_menus_menu_level ON manage_menus(menu_level);
CREATE INDEX idx_manage_menus_display_order ON manage_menus(display_order);
CREATE INDEX idx_manage_menus_status ON manage_menus(status);
CREATE INDEX idx_manage_menus_delete_yn ON manage_menus(delete_yn);

-- 메뉴별 역할 접근 권한 테이블 (단순화된 버전)
CREATE TABLE manage_menu_role_permissions (
    permission_id   BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '권한 매핑 고유 번호 (PK)',
    menu_id         BIGINT UNSIGNED NOT NULL COMMENT '메뉴 고유 번호 (FK)',
    role_id         VARCHAR(50) NOT NULL COMMENT '역할 ID (FK, roles.role_id 참조)',
    is_accessible   ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '접근 가능 여부',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    UNIQUE KEY uk_menu_role_permission (menu_id, role_id, delete_yn),
    FOREIGN KEY (menu_id) REFERENCES manage_menus(menu_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '메뉴별 역할 접근 권한 매핑 (SUPER_ADMIN 관리)';

CREATE INDEX idx_manage_menu_role_permissions_menu_id ON manage_menu_role_permissions(menu_id);
CREATE INDEX idx_manage_menu_role_permissions_role_id ON manage_menu_role_permissions(role_id);
CREATE INDEX idx_manage_menu_role_permissions_delete_yn ON manage_menu_role_permissions(delete_yn);

-- 개별 사용자별 메뉴 접근 권한 테이블
CREATE TABLE manage_menu_user_permissions (
    user_permission_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '사용자 권한 매핑 고유 번호 (PK)',
    menu_id         BIGINT UNSIGNED NOT NULL COMMENT '메뉴 고유 번호 (FK)',
    user_email      VARCHAR(255) NOT NULL COMMENT '사용자 이메일 (FK, users.user_email 참조)',
    is_accessible   ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '접근 가능 여부',
    permission_note TEXT COMMENT '권한 부여 사유 또는 메모',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    UNIQUE KEY uk_menu_user_permission (menu_id, user_email, delete_yn),
    FOREIGN KEY (menu_id) REFERENCES manage_menus(menu_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (user_email) REFERENCES users(user_email) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '개별 사용자별 메뉴 접근 권한 매핑 (SUPER_ADMIN 관리)';

CREATE INDEX idx_manage_menu_user_permissions_menu_id ON manage_menu_user_permissions(menu_id);
CREATE INDEX idx_manage_menu_user_permissions_user_email ON manage_menu_user_permissions(user_email);
CREATE INDEX idx_manage_menu_user_permissions_delete_yn ON manage_menu_user_permissions(delete_yn);

CREATE TABLE projects (
    project_id      BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '프로젝트 고유 번호 (PK)',
    project_name    VARCHAR(255) NOT NULL COMMENT '프로젝트 이름 (전체 시스템 내 고유)',
    description     TEXT COMMENT '프로젝트 설명',
    project_admin_user_email VARCHAR(255) COMMENT '담당 프로젝트 관리자 user_email (FK, 주 담당자)',
    status          ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '프로젝트 상태',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    FOREIGN KEY (project_admin_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '프로젝트(업체) 정보';

CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_delete_yn ON projects(delete_yn);
CREATE INDEX idx_project_name_delete_yn ON projects(project_name, delete_yn);

CREATE TABLE user_project_mapping (
    mapping_id      BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '매핑 고유 번호 (PK)',
    user_email      VARCHAR(255) NOT NULL COMMENT '사용자 아이디 (FK, users.user_email 참조)',
    project_id      BIGINT UNSIGNED NOT NULL COMMENT '프로젝트 고유 번호 (FK, projects.project_id 참조)',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    UNIQUE KEY uk_user_project (user_email, project_id), 
    FOREIGN KEY (user_email) REFERENCES users(user_email) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '사용자와 프로젝트 멤버십 매핑 정보';

CREATE INDEX idx_user_project_mapping_project_id ON user_project_mapping(project_id);
CREATE INDEX idx_user_project_mapping_delete_yn ON user_project_mapping(delete_yn);

CREATE TABLE invitations (
    invite_id       BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '초대 고유 번호 (PK)',
    project_id      BIGINT UNSIGNED NOT NULL COMMENT '초대 대상 프로젝트 고유 번호 (FK)',
    inviter_user_email VARCHAR(255) COMMENT '초대한 관리자 user_email (FK)',
    invitee_email   VARCHAR(255) NOT NULL COMMENT '초대받는 사람 이메일',
    invitee_name    VARCHAR(100) COMMENT '초대받는 사람 이름 (선택)',
    role_id         VARCHAR(50) NOT NULL COMMENT '초대 시 부여할 역할 ID (FK)',
    invite_token    VARCHAR(100) NOT NULL UNIQUE COMMENT '초대 수락용 고유 토큰',
    expiry_date     DATETIME NOT NULL COMMENT '초대 유효 기간',
    status          ENUM('PENDING', 'ACCEPTED', 'EXPIRED', 'CANCELED') NOT NULL DEFAULT 'PENDING' COMMENT '초대 상태',
    accepted_user_email VARCHAR(255) COMMENT '초대를 수락한 사용자 user_email (FK)',
    accepted_date   DATETIME COMMENT '초대 수락 일시',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '초대 생성 시기',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '상태 변경 시기',
    FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (inviter_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (accepted_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '팀 멤버 초대 정보';

CREATE INDEX idx_invitations_project_id ON invitations(project_id);
CREATE INDEX idx_invitations_invitee_email ON invitations(invitee_email);
CREATE INDEX idx_invitations_status ON invitations(status);

CREATE TABLE qr_codes (
    qr_code_id      BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'QR 코드 고유 번호 (PK)',
    project_id      BIGINT UNSIGNED NOT NULL COMMENT '소속 프로젝트 고유 번호 (FK)',
    qr_name         VARCHAR(255) NOT NULL COMMENT 'QR 코드 이름 (프로젝트 내 고유)',
    description     TEXT COMMENT 'QR 코드 설명',
    short_code      VARCHAR(50) UNIQUE COMMENT 'QR 코드 접근용 짧은 고유 식별 코드 (선택적, 외부 사용 가능)',
    qr_type         ENUM('URL', 'TEXT', 'WIFI', 'SNS_LINK', 'RESERVATION_LINK', 'EXCHANGE_VOUCHER_LINK', 'LOCATION', 'LANDING_PAGE', 'EVENT_ATTENDANCE', 'PAYMENT_URL') NOT NULL COMMENT 'QR 코드 타입',
    target_content  TEXT NOT NULL COMMENT '연결될 콘텐츠 내용 (URL, Wifi 정보 JSON, 랜딩페이지 ID, 이벤트 ID 등)',
    exchange_total_count INT UNSIGNED DEFAULT NULL COMMENT '교환권 총 발행(사용 가능) 횟수 (null이면 무제한)',
    exchange_current_uses INT UNSIGNED DEFAULT 0 COMMENT '교환권 현재 사용 횟수',
    linked_landing_page_id BIGINT UNSIGNED NULL COMMENT '연결된 랜딩페이지 고유 번호 (FK, qr_type=''LANDING_PAGE'')',
    linked_event_id BIGINT UNSIGNED NULL COMMENT '연결된 이벤트 고유 번호 (FK, qr_type=''EVENT_ATTENDANCE'')',
    design_options  JSON COMMENT 'QR 코드 디자인 옵션 (색상, 로고 경로, 스타일 등 JSON)',
    qr_image_path   VARCHAR(512) COMMENT '생성된 QR 코드 이미지 파일 경로 (필요시)',
    qr_logo_path    VARCHAR(512) COMMENT 'QR 코드에 사용된 로고 이미지 파일 경로',
    qr_installed_image_path VARCHAR(512) COMMENT 'QR 코드 설치 장소 이미지 파일 경로',
    scan_count      BIGINT UNSIGNED DEFAULT 0 COMMENT '스캔 횟수 (로그 기반 업데이트, 조회 성능용)',
    status          ENUM('ACTIVE', 'INACTIVE', 'EXPIRED') NOT NULL DEFAULT 'ACTIVE' COMMENT 'QR 코드 상태',
    valid_from_date DATETIME COMMENT '유효 시작 일시',
    valid_to_date   DATETIME COMMENT '유효 종료 일시',
    installation_location VARCHAR(255) COMMENT '설치 장소 (선택)',
    installation_location_lat DECIMAL(10, 8) NULL COMMENT '설치 장소 위도',
    installation_location_lng DECIMAL(11, 8) NULL COMMENT '설치 장소 경도',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부 (휴지통 기능)',
    qr_uuid         VARCHAR(36) NOT NULL UNIQUE COMMENT 'QR 코드 외부 식별용 UUID',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    UNIQUE KEY uk_qr_name_project (project_id, qr_name, delete_yn),
    FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT 'QR 코드 정보 (동적 QR)';

CREATE INDEX idx_qr_codes_project_id ON qr_codes(project_id);
CREATE INDEX idx_qr_codes_qr_type ON qr_codes(qr_type);
CREATE INDEX idx_qr_codes_status ON qr_codes(status);
CREATE INDEX idx_qr_codes_delete_yn ON qr_codes(delete_yn);
CREATE INDEX idx_qr_codes_qr_uuid ON qr_codes(qr_uuid);

-- 외래 키 제약 조건 추가 (테이블 생성 순서 문제 해결)
ALTER TABLE qr_codes
    ADD CONSTRAINT fk_qr_codes_landing_page FOREIGN KEY (linked_landing_page_id) REFERENCES landing_pages(landing_page_id) ON DELETE SET NULL ON UPDATE CASCADE,
    ADD CONSTRAINT fk_qr_codes_event FOREIGN KEY (linked_event_id) REFERENCES events(event_id) ON DELETE SET NULL ON UPDATE CASCADE;

CREATE TABLE landing_pages (
    landing_page_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '랜딩 페이지 고유 번호 (PK)',
    project_id      BIGINT UNSIGNED NOT NULL COMMENT '소속 프로젝트 고유 번호 (FK)',
    page_title      VARCHAR(255) COMMENT '랜딩 페이지 제목 (브라우저 제목 표시줄, SEO용)',
    description     TEXT COMMENT '랜딩 페이지 설명',
    content_json    JSON COMMENT '페이지 구조 및 콘텐츠 (WYSIWYG/블록 에디터 데이터)',
    status          ENUM('DRAFT', 'PUBLISHED') NOT NULL DEFAULT 'DRAFT' COMMENT '랜딩 페이지 상태 (임시저장/게시됨)',
    valid_from_date DATETIME COMMENT '유효 시작 일시',
    valid_to_date   DATETIME COMMENT '유효 종료 일시',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부 (복구 불가)',
    FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '랜딩 페이지 정보';

CREATE INDEX idx_landing_pages_project_id ON landing_pages(project_id);
CREATE INDEX idx_landing_pages_status ON landing_pages(status);
CREATE INDEX idx_landing_pages_delete_yn ON landing_pages(delete_yn);

CREATE TABLE landing_page_versions (
    version_id      BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '버전 고유 번호 (PK)',
    landing_page_id BIGINT UNSIGNED NOT NULL COMMENT '원본 랜딩 페이지 고유 번호 (FK)',
    content_json    JSON COMMENT '해당 버전의 콘텐츠',
    create_user_email VARCHAR(255) COMMENT '버전 생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '버전 생성 시기',
    FOREIGN KEY (landing_page_id) REFERENCES landing_pages(landing_page_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '랜딩 페이지 변경 이력 (버전 관리)';

CREATE TABLE pre_registration_forms (
    form_id         BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '신청서 양식 고유 번호 (PK)',
    project_id      BIGINT UNSIGNED NOT NULL COMMENT '소속 프로젝트 고유 번호 (FK)',
    form_name       VARCHAR(255) NOT NULL COMMENT '신청서 양식 이름 (프로젝트 내 고유)',
    description     TEXT COMMENT '신청서 양식 설명',
    completion_message TEXT COMMENT '신청 완료 시 보여줄 메시지',
    privacy_policy_agreement_text TEXT COMMENT '개인정보 처리방침 동의 문구',
    require_consent ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '개인정보 동의 필요 여부',
    auto_confirm_yn   ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '참가 신청 자동 처리 여부',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    UNIQUE KEY uk_form_name_project (project_id, form_name),
    FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '이벤트 사전 신청서 양식 정의';

CREATE INDEX idx_pre_reg_forms_project_id ON pre_registration_forms(project_id);
CREATE INDEX idx_pre_reg_forms_delete_yn ON pre_registration_forms(delete_yn);

CREATE TABLE form_fields (
    field_id        BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '양식 필드 고유 번호 (PK)',
    form_id         BIGINT UNSIGNED NOT NULL COMMENT '소속 신청서 양식 고유 번호 (FK)',
    field_label     VARCHAR(255) NOT NULL COMMENT '필드 레이블 (화면에 보여지는 이름)',
    field_name      VARCHAR(100) NOT NULL COMMENT '필드 내부 식별 이름 (데이터 저장 키)',
    field_type      ENUM('TEXT', 'TEXTAREA', 'NUMBER', 'NAME', 'TEL', 'EMAIL', 'DATE', 'TIME', 'SELECT', 'RADIO', 'CHECKBOX') NOT NULL COMMENT '필드 타입',
    is_required_yn  ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '필수 입력 여부',
    help_text       VARCHAR(512) COMMENT '도움말 텍스트',
    options         JSON COMMENT '선택형 필드의 옵션 목록 (JSON 배열)',
    display_order   INT UNSIGNED DEFAULT 0 COMMENT '필드 표시 순서',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    UNIQUE KEY uk_field_name_form (form_id, field_name),
    FOREIGN KEY (form_id) REFERENCES pre_registration_forms(form_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '사전 신청서 양식의 개별 필드 정의';

CREATE INDEX idx_form_fields_form_id ON form_fields(form_id);

CREATE TABLE events (
    event_id        BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '이벤트 고유 번호 (PK)',
    project_id      BIGINT UNSIGNED NOT NULL COMMENT '소속 프로젝트 고유 번호 (FK)',
    event_name      VARCHAR(255) NOT NULL COMMENT '이벤트 이름',
    description     TEXT COMMENT '이벤트 설명',
    start_date      DATETIME COMMENT '이벤트 시작 일시',
    end_date        DATETIME COMMENT '이벤트 종료 일시',
    location        VARCHAR(255) COMMENT '이벤트 장소',
    participant_limit INT UNSIGNED COMMENT '참가 인원 제한 (null이면 무제한)',
    pre_registration_form_id BIGINT UNSIGNED COMMENT '연결된 사전 신청서 양식 고유 번호 (FK)',
    linked_qr_code_id BIGINT UNSIGNED COMMENT '이벤트 전용 QR 코드 고유 번호 (FK)',
    team_id         BIGINT UNSIGNED COMMENT '연결된 대표 팀 고유 번호 (FK)',
    event_image_path VARCHAR(255) COMMENT '이벤트 이미지 경로',
    status          ENUM('SCHEDULED', 'ONGOING', 'FINISHED', 'CANCELED') NOT NULL DEFAULT 'SCHEDULED' COMMENT '이벤트 상태',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부 (복구 미제공)',
    FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (pre_registration_form_id) REFERENCES pre_registration_forms(form_id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (linked_qr_code_id) REFERENCES qr_codes(qr_code_id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '이벤트 정보';

CREATE INDEX idx_events_project_id ON events(project_id);
CREATE INDEX idx_events_status ON events(status);
CREATE INDEX idx_events_delete_yn ON events(delete_yn);
CREATE INDEX idx_events_team_id ON events(team_id);

--!!!!!!!!!!!!!!! events, teams 테이블 순환참조로 인한 CREATE 오류 해결법.
-- 1. events 테이블 생성 (team_id에 대한 FK 제약조건은 제외)
CREATE TABLE events (
    event_id        BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    project_id      BIGINT UNSIGNED NOT NULL,
    team_id         BIGINT UNSIGNED NULL, -- 컬럼은 추가
    -- ... 나머지 컬럼들 ...
    FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE RESTRICT ON UPDATE CASCADE
    -- ... 다른 FK들 ...
    -- team_id에 대한 FK는 여기서 정의하지 않음
);

-- 2. teams 테이블 생성 (이제 events 테이블이 존재하므로 생성 가능)
CREATE TABLE teams (
    team_id             BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    event_id            BIGINT UNSIGNED NOT NULL,
    -- ... 나머지 컬럼들 ...
    FOREIGN KEY (event_id) REFERENCES events(event_id) ON DELETE CASCADE ON UPDATE CASCADE
    -- ... 다른 FK들 ...
);

-- 3. ALTER TABLE을 이용해 events 테이블에 FK 제약조건 추가
ALTER TABLE events
    ADD CONSTRAINT fk_events_team_id
        FOREIGN KEY (team_id)
        REFERENCES teams(team_id)
        ON DELETE SET NULL
        ON UPDATE CASCADE;
--!!!!!!!!!!!!!!!

CREATE TABLE attendees (
    attendee_id         BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '참가자(신청) 고유 번호 (PK)',
    event_id            BIGINT UNSIGNED NOT NULL COMMENT '참여 이벤트 고유 번호 (FK)',
    participation_type  ENUM('INDIVIDUAL', 'TEAM') NULL DEFAULT 'INDIVIDUAL' COMMENT '참가 유형 (개인/팀)',
    team_id             BIGINT UNSIGNED NULL DEFAULT NULL COMMENT '소속된 참가 팀 ID (FK, participation_type=''TEAM''인 경우)',
    form_id             BIGINT UNSIGNED COMMENT '제출한 신청서 양식 고유 번호 (FK, 직접 등록 시 null 가능)',
    submission_data     JSON NOT NULL COMMENT '신청 시 제출된 데이터 (양식 필드 기반 Key-Value)',
    attendee_name       VARCHAR(100) COMMENT '참가자 이름 (조회 편의성)',
    attendee_contact    VARCHAR(50) COMMENT '참가자 연락처 (조회 편의성)',
    attendee_email      VARCHAR(255) COMMENT '참가자 이메일 (조회 편의성)',
    attended_yn         ENUM('Y', 'N', 'PENDING') NOT NULL DEFAULT 'PENDING' COMMENT '실제 참석 여부',
    attended_confirm_yn ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '참석 권한 여부(Y-가능, N-불가능)',
    attended_date       DATETIME COMMENT '참석 처리 일시',
    confirmation_code   VARCHAR(50) NULL COMMENT '참석 확인용 고유 코드 (QR 생성용)',
    registration_date   DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '신청 등록 날짜',
    registered_by_admin_user_email VARCHAR(255) COMMENT '관리자 직접 등록 시 해당 관리자 user_email (FK)',
    create_user_email   VARCHAR(255) COMMENT '생성자 user_email (FK, 신청자 본인 또는 관리자)',
    create_date         DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email   VARCHAR(255) COMMENT '수정자 user_email (FK, 주로 참석여부 변경 등)',
    last_update_date    DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email   VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date         DATETIME COMMENT '삭제 일시',
    use_yn              ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn           ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부 (복구 미제공)',

    FOREIGN KEY (event_id) REFERENCES events(event_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (team_id) REFERENCES teams(team_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (form_id) REFERENCES pre_registration_forms(form_id) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (registered_by_admin_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE

) COMMENT '이벤트 참가 신청자 및 참석 정보 (중복 참여 허용)';

-- 인덱스(Index) 정의
CREATE INDEX idx_attendees_event_id ON attendees(event_id);
CREATE INDEX idx_attendees_team_id ON attendees(team_id);
CREATE INDEX idx_attendees_participation_type ON attendees(participation_type);
CREATE INDEX idx_attendees_attended_yn ON attendees(attended_yn);
CREATE INDEX idx_attendees_delete_yn ON attendees(delete_yn);
CREATE INDEX idx_attendees_attendee_email ON attendees(attendee_email);

-- 이벤트 참가자 팀 정보
CREATE TABLE `teams` (
  `team_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '참가 팀 고유 번호 (PK)',
  `event_id` bigint(20) unsigned NOT NULL COMMENT '소속 이벤트 고유 번호 (FK, events.event_id 참조)',
  `team_name` varchar(255) NOT NULL COMMENT '팀 이름 (이벤트 내에서 고유)',
  `team_code` varchar(20) DEFAULT NULL COMMENT '사용자에게 표시될 팀 고유 코드 (선택, 전체 시스템에서 고유)',
  `description` text DEFAULT NULL COMMENT '팀 소개 및 설명',
  `max_members` int(10) unsigned DEFAULT NULL COMMENT '팀 최대 인원 제한 (NULL이면 무제한)',
  `team_status` enum('ACTIVE','LOCKED','PENDING_APPROVAL') NOT NULL DEFAULT 'ACTIVE' COMMENT '팀 상태 (ACTIVE:활성, LOCKED:잠김, PENDING_APPROVAL:승인대기)',
  `profile_img_path` varchar(512) DEFAULT NULL COMMENT '팀 프로필 이미지 파일 경로',
  `join_qr_code_id` bigint(20) unsigned DEFAULT NULL COMMENT '팀 가입용 QR 코드 고유 번호 (FK, qr_codes.qr_code_id 참조)',
  `leader_attendee_id` bigint(20) unsigned DEFAULT NULL COMMENT '팀 리더의 참가자 ID (FK, attendees.attendee_id 참조)',
  `leader_name` varchar(100) DEFAULT NULL COMMENT '팀 리더 이름',
  `leader_phone` varchar(50) DEFAULT NULL COMMENT '팀 리더 연락처',
  `use_yn` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
  `delete_yn` enum('Y','N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
  `create_user_email` varchar(255) DEFAULT NULL COMMENT '팀 정보를 등록한 관리자 user_email (FK)',
  `create_date` datetime DEFAULT current_timestamp() COMMENT '생성 시기',
  `update_user_email` varchar(255) DEFAULT NULL COMMENT '수정자 user_email (FK)',
  `last_update_date` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '수정 시기',
  `delete_user_email` varchar(255) DEFAULT NULL COMMENT '삭제자 user_email (FK)',
  `delete_date` datetime DEFAULT NULL COMMENT '삭제 일시',
  PRIMARY KEY (`team_id`),
  UNIQUE KEY `uk_team_name_event_delete` (`event_id`,`team_name`,`delete_yn`),
  UNIQUE KEY `join_qr_code_id` (`join_qr_code_id`),
  UNIQUE KEY `uk_team_code` (`team_code`), -- 이 부분이 수정되었습니다.
  KEY `create_user_email` (`create_user_email`),
  KEY `update_user_email` (`update_user_email`),
  KEY `delete_user_email` (`delete_user_email`),
  KEY `fk_event_teams_leader_attendee` (`leader_attendee_id`),
  KEY `idx_event_teams_status` (`team_status`),
  CONSTRAINT `fk_event_teams_leader_attendee` FOREIGN KEY (`leader_attendee_id`) REFERENCES `attendees` (`attendee_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `teams_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `events` (`event_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `teams_ibfk_2` FOREIGN KEY (`join_qr_code_id`) REFERENCES `qr_codes` (`qr_code_id`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `teams_ibfk_3` FOREIGN KEY (`create_user_email`) REFERENCES `users` (`user_email`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `teams_ibfk_4` FOREIGN KEY (`update_user_email`) REFERENCES `users` (`user_email`) ON DELETE SET NULL ON UPDATE CASCADE,
  CONSTRAINT `teams_ibfk_5` FOREIGN KEY (`delete_user_email`) REFERENCES `users` (`user_email`) ON DELETE SET NULL ON UPDATE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='이벤트 참가자 팀 정보 (팀 가입 QR, 선택적 리더 연동)';

CREATE TABLE qr_scan_logs (
    scan_log_id     BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '스캔 로그 고유 번호 (PK)',
    qr_code_id      BIGINT UNSIGNED NOT NULL COMMENT '스캔된 QR 코드 고유 번호 (FK)',
    scan_time       DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '스캔 발생 시각',
    ip_address      VARCHAR(45) COMMENT '스캔한 사용자의 IP 주소',
    user_agent      TEXT COMMENT '스캔한 사용자의 User Agent 문자열 (기기/브라우저 정보)',
    location_data   JSON COMMENT '스캔 위치 정보 (GPS 등, 수집 시)',
    scanner_user_email VARCHAR(255) COMMENT '스캔한 사용자 user_email (FK, 로그인 상태인 경우)',
    is_unique_scan  BOOLEAN DEFAULT TRUE COMMENT '유니크 스캔 여부 (판단 로직 필요)',
    FOREIGN KEY (qr_code_id) REFERENCES qr_codes(qr_code_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (scanner_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT 'QR 코드 스캔 발생 기록 (통계 데이터 원천)';

CREATE INDEX idx_qr_scan_logs_qr_code_id ON qr_scan_logs(qr_code_id);
CREATE INDEX idx_qr_scan_logs_scan_time ON qr_scan_logs(scan_time);

CREATE TABLE exchange_approval_logs (
    approval_log_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '승인 로그 고유 번호 (PK)',
    qr_code_id      BIGINT UNSIGNED NOT NULL COMMENT '승인 처리된 교환권 QR 코드 고유 번호 (FK)',
    approver_user_email VARCHAR(255) NOT NULL COMMENT '승인 처리한 사용자 user_email (FK)',
    approval_time   DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '승인 처리 시각',
    notes           TEXT COMMENT '승인 처리 관련 메모',
    FOREIGN KEY (qr_code_id) REFERENCES qr_codes(qr_code_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (approver_user_email) REFERENCES users(user_email) ON DELETE RESTRICT ON UPDATE CASCADE
) COMMENT '교환권 QR 코드 사용 승인 기록';

CREATE INDEX idx_exchange_approval_logs_qr_code_id ON exchange_approval_logs(qr_code_id);
CREATE INDEX idx_exchange_approval_logs_approver_user_email ON exchange_approval_logs(approver_user_email);

CREATE TABLE refresh_token (
    token_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '리프레시 토큰 ID',
    user_email VARCHAR(255) NOT NULL COMMENT '사용자 ID (FK)',
    token VARCHAR(500) NOT NULL COMMENT '리프레시 토큰 값',
    expiry_date DATETIME NOT NULL COMMENT '만료 일시',
    create_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 일시',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 일시',
    CONSTRAINT uk_refresh_token UNIQUE (token),
    CONSTRAINT fk_refresh_token_users FOREIGN KEY (user_email) REFERENCES users (user_email) ON DELETE CASCADE,
    INDEX idx_refresh_token_user (user_email)
) COMMENT '리프레시 토큰 정보 테이블';

CREATE TABLE event_benefits (
    benefit_id      BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '혜택 고유 번호 (PK)',
    event_id        BIGINT UNSIGNED NOT NULL COMMENT '혜택이 속한 이벤트 고유 번호 (FK, events.event_id 참조)',
    benefit_code    VARCHAR(100) NOT NULL COMMENT '혜택 내부 식별 코드 (영문/숫자, 예: WELCOME_DRINK)',
    benefit_name    VARCHAR(255) NOT NULL COMMENT '혜택 이름 (화면에 표시될 이름, 예: 웰컴 드링크)',
    description     TEXT COMMENT '혜택 상세 설명',
    status          ENUM('ACTIVE', 'INACTIVE') NOT NULL DEFAULT 'ACTIVE' COMMENT '혜택 상태',
    quantity        INT NULL DEFAULT NULL COMMENT '초기 재고 수량',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    UNIQUE KEY uk_event_benefit_code (event_id, benefit_code) COMMENT '이벤트 내 혜택 코드는 고유해야 함',
    FOREIGN KEY (event_id) REFERENCES events(event_id) ON DELETE CASCADE ON UPDATE CASCADE, -- 이벤트 삭제 시 관련 혜택도 삭제
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '이벤트별 제공 혜택 정의';

CREATE INDEX idx_event_benefits_event_id ON event_benefits(event_id);
CREATE INDEX idx_event_benefits_status ON event_benefits(status);
CREATE INDEX idx_event_benefits_delete_yn ON event_benefits(delete_yn);

CREATE TABLE attendee_benefit_redemption (
    redemption_id   BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '혜택 사용 기록 고유 번호 (PK)',
    attendee_id     BIGINT UNSIGNED NOT NULL COMMENT '혜택을 사용한 참가자 고유 번호 (FK, attendees.attendee_id 참조)',
    benefit_id      BIGINT UNSIGNED NOT NULL COMMENT '사용한 혜택 고유 번호 (FK, event_benefits.benefit_id 참조)',
    redeemed_at     DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '혜택 사용(승인) 일시',
    redeemed_by_user_email VARCHAR(255) NOT NULL COMMENT '혜택 사용을 승인한 관리자/직원 user_email (FK, users.user_email 참조)',
    memo            TEXT COMMENT '처리 관련 메모 (선택)',
    -- 감사/상태 컬럼은 필요에 따라 추가 (예: 취소 기능이 있다면 status, 취소 일시/사유 등)
    -- create_date 등은 redeemed_at으로 대체 가능해 보임

    UNIQUE KEY uk_attendee_benefit (attendee_id, benefit_id) COMMENT '한 참가자는 각 혜택을 한 번만 사용할 수 있음',
    FOREIGN KEY (attendee_id) REFERENCES attendees(attendee_id) ON DELETE CASCADE ON UPDATE CASCADE, -- 참가자 삭제 시 사용 기록도 삭제
    FOREIGN KEY (benefit_id) REFERENCES event_benefits(benefit_id) ON DELETE RESTRICT ON UPDATE CASCADE, -- 혜택 정의가 삭제되면 사용 기록도 문제 -> RESTRICT 또는 다른 정책 고려
    FOREIGN KEY (redeemed_by_user_email) REFERENCES users(user_email) ON DELETE RESTRICT ON UPDATE CASCADE -- 사용 처리자 정보는 유지 필요
) COMMENT '참가자의 개별 혜택 사용 기록';

CREATE INDEX idx_attendee_benefit_redemption_attendee_id ON attendee_benefit_redemption(attendee_id);
CREATE INDEX idx_attendee_benefit_redemption_benefit_id ON attendee_benefit_redemption(benefit_id);
CREATE INDEX idx_attendee_benefit_redemption_redeemed_at ON attendee_benefit_redemption(redeemed_at);

CREATE TABLE `roles` (
  `role_id` varchar(50) NOT NULL COMMENT '역할 고유 ID (예: SUPER_ADMIN, PROJECT_ADMIN)',
  `role_name` varchar(100) NOT NULL COMMENT '역할 이름 (한국어)',
  `description` text DEFAULT NULL COMMENT '역할 설명',
  `create_date` datetime DEFAULT current_timestamp() COMMENT '생성 시기',
  `last_update_date` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '수정 시기',
  PRIMARY KEY (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='시스템 역할 정의 테이블';

CREATE TABLE `webservice_log` (
  `log_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '로그 번호',
  `user_token` varchar(50) NOT NULL COMMENT '사용자 아이디 token',
  `user_email` varchar(50) DEFAULT NULL COMMENT '사용자 이메일ID',
  `referer` varchar(500) DEFAULT NULL COMMENT '이전 페이지',
  `request_uri` varchar(200) NOT NULL COMMENT '요청 페이지',
  `request_params` text DEFAULT NULL COMMENT '요청 파라미터',
  `request_time` datetime NOT NULL COMMENT '요청 시간',
  `request_host` varchar(32) NOT NULL COMMENT '요청 호스트',
  `request_agent` varchar(500) DEFAULT NULL COMMENT '요청 Agent',
  `response_status` smallint(6) DEFAULT NULL COMMENT 'HTTP 응답코드',
  `session_id` varchar(32) NOT NULL COMMENT '접속 세션아이디',
  `tracking` enum('Y','N') NOT NULL DEFAULT 'N',
  PRIMARY KEY (`log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=299017 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 문의 테이블
CREATE TABLE `inquiries` (
  `inquiry_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '문의 ID',
  `project_id` bigint(20) unsigned NOT NULL COMMENT '프로젝트 ID',
  `user_email` varchar(255) NOT NULL COMMENT '문의 작성자 이메일',
  `inquiry_title` varchar(255) NOT NULL COMMENT '문의 제목',
  `inquiry_content` text NOT NULL COMMENT '문의 내용',
  `inquiry_type` varchar(50) NOT NULL COMMENT '문의 유형 (ACCOUNT-계정, TECHNICAL-기술, USAGE-이용, ETC-기타)',
  `inquiry_status` varchar(50) NOT NULL DEFAULT 'PENDING' COMMENT '문의 상태 (PENDING-접수됨, PROCESSING-처리중, COMPLETED-완료)',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT '생성 일시',
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '수정 일시',
  `delete_yn` char(1) NOT NULL DEFAULT 'N' COMMENT '삭제 여부 (Y/N)',
  `delete_user_email` varchar(255) DEFAULT NULL COMMENT '삭제한 사용자 이메일',
  `delete_date` timestamp NULL DEFAULT NULL COMMENT '삭제 일시',
  PRIMARY KEY (`inquiry_id`),
  KEY `idx_inquiries_project_id` (`project_id`),
  KEY `idx_inquiries_user_email` (`user_email`),
  KEY `idx_inquiries_status_type` (`inquiry_status`,`inquiry_type`),
  KEY `idx_inquiries_delete_yn` (`delete_yn`),
  CONSTRAINT `fk_inquiries_project_id` FOREIGN KEY (`project_id`) REFERENCES `projects` (`project_id`) ON DELETE CASCADE,
  CONSTRAINT `fk_inquiries_user_email` FOREIGN KEY (`user_email`) REFERENCES `users` (`user_email`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='문의';

-- 문의 댓글 테이블
CREATE TABLE `inquiry_comments` (
    `comment_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '댓글 ID',
    `inquiry_id` BIGINT NOT NULL COMMENT '문의 ID',
    `user_email` VARCHAR(255) NOT NULL COMMENT '댓글 작성자 이메일',
    `comment_content` TEXT NOT NULL COMMENT '댓글 내용',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '생성 일시',
    PRIMARY KEY (`comment_id`),
    INDEX `idx_inquiry_comments_inquiry_id` (`inquiry_id`),
    INDEX `idx_inquiry_comments_user_email` (`user_email`),
    CONSTRAINT `fk_inquiry_comments_inquiry_id` FOREIGN KEY (`inquiry_id`) REFERENCES `inquiries` (`inquiry_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_inquiry_comments_user_email` FOREIGN KEY (`user_email`) REFERENCES `users` (`user_email`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='문의 댓글';

-- 문의 첨부파일 테이블
CREATE TABLE `inquiry_attachments` (
    `attachment_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '첨부파일 ID',
    `inquiry_id` BIGINT NULL COMMENT '문의 ID (문의 자체 첨부 시)',
    `comment_id` BIGINT NULL COMMENT '댓글 ID (댓글 첨부 시)',
    `original_file_name` VARCHAR(255) NOT NULL COMMENT '원본 파일명',
    `stored_file_path` VARCHAR(1024) NOT NULL COMMENT '저장된 파일 경로',
    `file_size` BIGINT NOT NULL COMMENT '파일 크기 (bytes)',
    `mime_type` VARCHAR(100) COMMENT 'MIME 타입',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '생성 일시',
    PRIMARY KEY (`attachment_id`),
    INDEX `idx_inquiry_attachments_inquiry_id` (`inquiry_id`),
    INDEX `idx_inquiry_attachments_comment_id` (`comment_id`),
    CONSTRAINT `fk_inquiry_attachments_inquiry_id` FOREIGN KEY (`inquiry_id`) REFERENCES `inquiries` (`inquiry_id`) ON DELETE CASCADE,
    CONSTRAINT `fk_inquiry_attachments_comment_id` FOREIGN KEY (`comment_id`) REFERENCES `inquiry_comments` (`comment_id`) ON DELETE CASCADE,
    CONSTRAINT `chk_attachment_parent` CHECK (
        (`inquiry_id` IS NOT NULL AND `comment_id` IS NULL) OR 
        (`inquiry_id` IS NULL AND `comment_id` IS NOT NULL)
    )
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='문의 첨부파일';

-- 알림 테이블
CREATE TABLE `notifications` (
    `notification_id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '알림 ID',
    `user_email` VARCHAR(255) NOT NULL COMMENT '알림 수신자 이메일',
    `inquiry_id` BIGINT NOT NULL COMMENT '관련 문의 ID',
    `message` VARCHAR(500) NOT NULL COMMENT '알림 메시지',
    `is_read` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '읽음 여부',
    `notification_type` VARCHAR(100) NOT NULL COMMENT '알림 유형 (e.g., INQUIRY_STATUS_UPDATED, NEW_COMMENT)',
    `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '생성 일시',
    PRIMARY KEY (`notification_id`),
    INDEX `idx_notifications_user_email_is_read` (`user_email`, `is_read`),
    INDEX `idx_notifications_inquiry_id` (`inquiry_id`),
    CONSTRAINT `fk_notifications_user_email` FOREIGN KEY (`user_email`) REFERENCES `users` (`user_email`) ON DELETE CASCADE,
    CONSTRAINT `fk_notifications_inquiry_id` FOREIGN KEY (`inquiry_id`) REFERENCES `inquiries` (`inquiry_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='알림';

-- QnA 질문 테이블
CREATE TABLE qna_questions (
    qna_question_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'QnA 질문 ID (PK)',
    project_id      BIGINT UNSIGNED NOT NULL COMMENT '소속 프로젝트 고유 번호 (FK, projects.project_id 참조)',
    title           VARCHAR(255) NOT NULL COMMENT '질문 제목',
    content         TEXT NOT NULL COMMENT '질문 내용',
    create_user_email VARCHAR(255) COMMENT '질문 작성자 user_email (FK, users.user_email 참조)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK, users.user_email 참조)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK, users.user_email 참조)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    view_count      BIGINT UNSIGNED DEFAULT 0 COMMENT '조회수',
    FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE RESTRICT ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT 'QnA 질문 게시판';

CREATE INDEX idx_qna_questions_project_id ON qna_questions(project_id);
CREATE INDEX idx_qna_questions_create_user_email ON qna_questions(create_user_email);
CREATE INDEX idx_qna_questions_delete_yn ON qna_questions(delete_yn);

-- QnA 답변 테이블
CREATE TABLE qna_answers (
    qna_answer_id   BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'QnA 답변 ID (PK)',
    qna_question_id BIGINT UNSIGNED NOT NULL UNIQUE COMMENT '원본 질문 ID (FK, qna_questions.qna_question_id 참조, 1:1 관계)',
    answer_content  TEXT NOT NULL COMMENT '답변 내용',
    create_user_email VARCHAR(255) COMMENT '답변 작성자 user_email (FK, users.user_email 참조)',
    create_date     DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK, users.user_email 참조)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK, users.user_email 참조)',
    delete_date     DATETIME COMMENT '삭제 일시',
    use_yn          ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn       ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    FOREIGN KEY (qna_question_id) REFERENCES qna_questions(qna_question_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT 'QnA 답변 게시판';

CREATE INDEX idx_qna_answers_create_user_email ON qna_answers(create_user_email);
CREATE INDEX idx_qna_answers_delete_yn ON qna_answers(delete_yn);

-- 전역 기본 권한 설정 테이블
CREATE TABLE global_default_permissions (
    setting_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '설정 고유 번호 (PK)',
    can_read ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '기본 읽기 권한',
    can_write ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '기본 쓰기(생성) 권한',
    can_update ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '기본 수정 권한',
    can_delete ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '기본 삭제 권한',
    description VARCHAR(500) DEFAULT '전역 기본 권한 설정' COMMENT '설정 설명',
    is_active ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '활성화 여부',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '전역 기본 권한 설정 (모든 사용자의 기본 CRUD 권한)';

-- 사용자별 전역 권한 오버라이드 테이블
CREATE TABLE user_global_permissions (
    user_permission_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '사용자 전역 권한 고유 번호 (PK)',
    user_email VARCHAR(255) NOT NULL COMMENT '사용자 이메일 (FK, users.user_email 참조)',
    can_read ENUM('Y', 'N') DEFAULT NULL COMMENT '읽기 권한 (NULL이면 전역 기본값 사용)',
    can_write ENUM('Y', 'N') DEFAULT NULL COMMENT '쓰기(생성) 권한 (NULL이면 전역 기본값 사용)',
    can_update ENUM('Y', 'N') DEFAULT NULL COMMENT '수정 권한 (NULL이면 전역 기본값 사용)',
    can_delete ENUM('Y', 'N') DEFAULT NULL COMMENT '삭제 권한 (NULL이면 전역 기본값 사용)',
    permission_note TEXT COMMENT '권한 부여 사유 또는 메모',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date DATETIME COMMENT '삭제 일시',
    use_yn ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    UNIQUE KEY uk_user_global_permission (user_email, delete_yn),
    FOREIGN KEY (user_email) REFERENCES users(user_email) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '사용자별 전역 권한 오버라이드 (특정 사용자의 모든 메뉴에 대한 기본 권한 설정)';

CREATE TABLE `setting_mail_server` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `alarm_type` varchar(50) NOT NULL COMMENT '알림타입',
  `smtp_email` varchar(100) NOT NULL COMMENT '알림보낼 이메일',
  `smtp_username` varchar(100) NOT NULL COMMENT '이메일 사용자명',
  `smtp_server` varchar(100) DEFAULT NULL COMMENT '메일 발송 서버',
  `smtp_port` smallint(5) unsigned DEFAULT 0 COMMENT '메일 발송 서버 포트',
  `smtp_authorize_required` enum('Y','N') DEFAULT 'Y' COMMENT '메일 발송 서버 인증 필요여부',
  `smtp_secure_type` varchar(50) DEFAULT NULL COMMENT '메일 발송 서버 보안 인증 타입',
  `smtp_login_id` varchar(50) DEFAULT NULL COMMENT '메일 발송 서버 인증 아이디',
  `smtp_login_pw` varchar(100) DEFAULT NULL COMMENT '메일 발송 서버 인증 비밀번호',
  `smtp_use_yn` enum('Y','N') DEFAULT 'N' COMMENT '사용 여부',
  `create_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '작성자',
  `create_date` datetime NOT NULL COMMENT '작성일시',
  `last_update_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '최종수정자',
  `last_update_date` datetime DEFAULT NULL COMMENT '최종수정일시',
  `delete_yn` enum('Y','N') DEFAULT 'N' COMMENT '삭제 여부',
  `delete_id` varchar(50) DEFAULT NULL COMMENT '삭제자 ID',
  `delete_date` datetime DEFAULT NULL COMMENT '삭제일시',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `alarm_type` (`alarm_type`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='메일 발송 서버 설정';

CREATE TABLE `setting_mail_format` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `server_id` int(10) unsigned NOT NULL DEFAULT 0 COMMENT '메일알림 설정ID',
  `title` varchar(200) NOT NULL COMMENT '메일제목',
  `mail_content` text NOT NULL COMMENT '메일내용',
  `start_date` datetime DEFAULT NULL COMMENT '사용 시작일시',
  `expire_date` datetime DEFAULT NULL COMMENT '사용 종료일시',
  `use_yn` enum('Y','N') DEFAULT 'Y' COMMENT '사용 여부',
  `create_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '작성자',
  `create_date` datetime NOT NULL COMMENT '작성일시',
  `last_update_id` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '최종수정자',
  `last_update_date` datetime DEFAULT NULL COMMENT '최종수정일시',
  `delete_yn` enum('Y','N') DEFAULT 'N' COMMENT '삭제 여부',
  `delete_id` varchar(50) DEFAULT NULL COMMENT '삭제자 ID',
  `delete_date` datetime DEFAULT NULL COMMENT '삭제일시',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `fk_setting_mail_format_id` (`server_id`) USING BTREE,
  CONSTRAINT `fk_setting_mail_format_id` FOREIGN KEY (`server_id`) REFERENCES `setting_mail_server` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='메일 양식';

CREATE TABLE `log_mail_send` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '고유번호',
  `from_email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '발신 이메일',
  `to_email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '수신 이메일',
  `mail_properties` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '메일 서버 설정값',
  `mail_format_id` int(11) unsigned DEFAULT NULL COMMENT '메일 템플릿 번호',
  `mail_subject` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '메일 제목',
  `mail_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '메일 내용',
  `mail_content_type` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '메일 타입',
  `send_request_time` datetime NOT NULL COMMENT '발송 요청 일시',
  `send_request_userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '발신요청자 ID',
  `send_result` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '발신결과',
  `send_result_time` datetime DEFAULT NULL COMMENT '발신결과일시',
  `send_result_message` text DEFAULT NULL COMMENT '발신 결과 세부내용',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='메일 발송 로그';


