package kr.wayplus.wayplus_qr.config;

import kr.wayplus.wayplus_qr.dto.response.SearchTypeInfo;
import org.springframework.stereotype.Component;
import jakarta.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

@Component
public class SearchTypeRegistry {
    private final Map<String, List<SearchTypeInfo>> registry = new HashMap<>();
    private final Map<String, Set<String>> validValues = new HashMap<>();

    @PostConstruct
    public void initialize() {
        register("project", List.of(
            new SearchTypeInfo("projectName", "프로젝트명"),
            new SearchTypeInfo("description", "설명"),
            new SearchTypeInfo("projectAdminUserEmail", "관리자"),
            new SearchTypeInfo("status", "상태"),
            new SearchTypeInfo("createUserEmail", "생성자"),
            new SearchTypeInfo("createDate", "생성일"),
            new SearchTypeInfo("lastUpdateDate", "최종 수정일")
        ));

        register("users", List.of(
            new SearchTypeInfo("userEmail", "이메일(ID)"),
            new SearchTypeInfo("name", "이름"),
            new SearchTypeInfo("roleId", "역할"),
            new SearchTypeInfo("status", "상태"),
            new SearchTypeInfo("createDate", "생성일")
        ));

        register("qrcode", List.of(
            new SearchTypeInfo("qrCodeName", "QR 이름"),
            new SearchTypeInfo("qrType", "QR 타입"),
            new SearchTypeInfo("targetContent", "콘텐츠"),
            new SearchTypeInfo("status", "상태"),
            new SearchTypeInfo("validFromDate", "유효 시작일"),
            new SearchTypeInfo("validToDate", "유효 종료일")
        ));

        register("landingPage", List.of(
            new SearchTypeInfo("pageTitle", "랜딩 페이지 이름"),
            new SearchTypeInfo("validFromDate", "유효 시작일"),
            new SearchTypeInfo("validToDate", "유효 종료일")
        ));

        register("event", List.of(
            new SearchTypeInfo("eventName", "이벤트 이름"),
            new SearchTypeInfo("description", "설명"),
            new SearchTypeInfo("startDate", "시작 날짜"),
            new SearchTypeInfo("endDate", "종료 날짜"),
            new SearchTypeInfo("preRegistrationFormName", "연동된 사전 신청서 이름")
        ));

        register("preRegistrationForm", List.of(
            new SearchTypeInfo("formName", "사전 신청서 이름"),
            new SearchTypeInfo("description", "설명"),
            new SearchTypeInfo("createDate", "생성일"),
            new SearchTypeInfo("lastUpdateDate", "수정일")
        ));

        // 참석자 검색 타입
        register("attendee", List.of(
            new SearchTypeInfo("attendeeName", "이름"),
            new SearchTypeInfo("attendeeEmail", "이메일"),
            new SearchTypeInfo("attendeeContact", "연락처"),
            new SearchTypeInfo("teamName", "소속 팀"),
            new SearchTypeInfo("createDate", "등록일시")
        ));

        // 문의 검색 타입
        register("inquiry", List.of(
            new SearchTypeInfo("inquiryTitle", "제목"),
            new SearchTypeInfo("inquiryType", "문의 유형"),
            new SearchTypeInfo("userEmail", "작성자"),
            new SearchTypeInfo("createDate", "문의 일자")
        ));

        // QnA 검색 타입
        register("qna", List.of(
            new SearchTypeInfo("title", "QnA 제목"),
            new SearchTypeInfo("content", "QnA 내용"),
            new SearchTypeInfo("answerContent", "답변 상태"),
            new SearchTypeInfo("createUserEmail", "작성자"),
            new SearchTypeInfo("createDate", "작성 일자")
        ));

        // 팀 검색 타입
        register("team", List.of(
            new SearchTypeInfo("teamName", "팀 이름"),
            new SearchTypeInfo("teamCode", "팀 코드"),
            new SearchTypeInfo("leaderName", "팀장 이름"),
            new SearchTypeInfo("teamStatus", "팀 상태"),
            new SearchTypeInfo("createDate", "등록일시")
        ));
    }

    private void register(String entityType, List<SearchTypeInfo> types) {
        String key = entityType.toLowerCase();
        registry.put(key, Collections.unmodifiableList(types));
        validValues.put(key, types.stream()
            .map(SearchTypeInfo::getValue)
            .collect(Collectors.toUnmodifiableSet())
        );
    }

    public List<SearchTypeInfo> getSearchTypes(String entityType) {
        return registry.getOrDefault(entityType.toLowerCase(), Collections.emptyList());
    }

    public boolean isValidSearchType(String entityType, String searchType) {
        if (searchType == null || searchType.isBlank()) {
            return true;
        }
        Set<String> valid = validValues.get(entityType.toLowerCase());
        return valid != null && valid.contains(searchType);
    }
}
