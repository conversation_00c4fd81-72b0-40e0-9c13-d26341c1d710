package kr.wayplus.wayplus_qr.entity;

import lombok.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PreRegistrationForm {
    private Long formId;
    private Long projectId;
    private String formName;
    private String description;
    private String completionMessage;
    private String autoConfirmYn;
    private String privacyPolicyAgreementText;
    private Boolean requireConsent;
    private String createUserEmail;
    private LocalDateTime createDate;
    private String updateUserEmail;
    private LocalDateTime lastUpdateDate;
    private String deleteUserEmail;
    private LocalDateTime deleteDate;
    private String useYn;
    private String deleteYn;
}
