package kr.wayplus.wayplus_qr.aspect;

import kr.wayplus.wayplus_qr.annotation.RequireMenuPermission;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import kr.wayplus.wayplus_qr.service.ManageMenuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

/**
 * 메뉴 권한 체크를 위한 AOP Aspect
 * 
 * @RequireMenuPermission 어노테이션이 적용된 메서드에 대해
 * 사용자의 메뉴 접근 권한을 체크합니다.
 */
@Aspect
@Component
@RequiredArgsConstructor
@Slf4j
public class MenuPermissionAspect {

    private final ManageMenuService manageMenuService;

    /**
     * @RequireMenuPermission 어노테이션이 적용된 메서드 실행 전에 권한을 체크합니다.
     */
    @Before("@annotation(requireMenuPermission)")
    public void checkMenuPermission(JoinPoint joinPoint, RequireMenuPermission requireMenuPermission) {
        log.debug("Checking menu permission for method: {}", joinPoint.getSignature().getName());
        
        // 현재 인증된 사용자 정보 가져오기
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            log.warn("No authenticated user found for menu permission check");
            throw new QRcodeException(ErrorCode.UNAUTHORIZED);
        }

        String userEmail = authentication.getName();
        String menuCode = requireMenuPermission.menuCode();
        String permissionType = requireMenuPermission.permission().getValue();

        log.debug("Checking permission - User: {}, Menu: {}, Permission: {}", 
                userEmail, menuCode, permissionType);

        // SUPER_ADMIN은 모든 권한을 가짐
        if (authentication.getAuthorities().stream()
                .anyMatch(authority -> "SUPER_ADMIN".equals(authority.getAuthority()))) {
            log.debug("SUPER_ADMIN user {} has full access to menu {}", userEmail, menuCode);
            return;
        }

        // 메뉴 권한 체크
        boolean hasPermission = manageMenuService.hasMenuPermission(menuCode, userEmail, permissionType);
        
        if (!hasPermission) {
            log.warn("User {} does not have {} permission for menu {}", 
                    userEmail, permissionType, menuCode);
            throw new QRcodeException(ErrorCode.FORBIDDEN);
        }

        log.debug("User {} has {} permission for menu {}", userEmail, permissionType, menuCode);
    }
}
