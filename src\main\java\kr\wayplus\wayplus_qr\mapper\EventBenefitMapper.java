package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.entity.EventBenefit;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

/**
 * MyBatis Mapper for event_benefits.
 */
@Mapper
public interface EventBenefitMapper {

    /**
     * 특정 이벤트의 활성(ACTIVE) 혜택 목록 조회
     */
    List<EventBenefit> selectActiveBenefitsByEventId(@Param("eventId") Long eventId);

    /**
     * 혜택 ID로 조회 (단건)
     */
    EventBenefit selectBenefitById(@Param("benefitId") Long benefitId);

    // 신규: 혜택 삽입
    int insertEventBenefit(EventBenefit benefit);

    // 혜택 정보 수정 (이름, 설명, 수량, 상태 등)
    int updateEventBenefit(EventBenefit benefit);

    // 특정 혜택이 사용된 횟수 조회 (attendee_benefit_redemption)
    int countRedeemedByBenefitId(@Param("benefitId") Long benefitId);

    // 신규: 동일 이벤트 내 benefitCode 중복 여부 확인
    Optional<EventBenefit> selectBenefitByCodeAndEventId(@Param("benefitCode") String benefitCode,
                                                         @Param("eventId") Long eventId);

    // 이벤트 ID로 모든 혜택 삭제
    int deleteBenefitsByEventId(@Param("eventId") Long eventId);

    // 이벤트 ID로 모든 혜택 사용 기록 삭제 (추가된 쿼리 대응)
    int deleteRedemptionsByEventId(@Param("eventId") Long eventId);

    // 특정 이벤트에 속한 모든 혜택 조회 (페이징 및 상태 필터링)
    List<EventBenefit> selectBenefitsByEventId(@Param("eventId") Long eventId,
                                               @Param("status") String status,
                                               @Param("offset") int offset,
                                               @Param("limit") int limit);

    List<EventBenefit> selectNonDeletedBenefitsByEventId(@Param("eventId") Long eventId);

    /**
     * 특정 혜택 논리 삭제
     */
    int logicalDeleteEventBenefit(@Param("benefitId") Long benefitId, @Param("userEmail") String userEmail);
}
