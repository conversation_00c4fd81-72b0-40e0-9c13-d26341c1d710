package kr.wayplus.wayplus_qr.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import kr.wayplus.wayplus_qr.entity.QrCode;
import kr.wayplus.wayplus_qr.entity.QrCodeStatus;
import lombok.Builder;
import lombok.Getter;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.time.LocalDateTime;

@Getter
@Builder
public class QrCodeResponseDto {
    private Long qrCodeId;
    private Long projectId;
    private String projectName;
    private String qrName;
    private String qrType;
    private QrCodeStatus status;
    private String targetContent;
    private Long linkedEventId;
    private Long linkedLandingPageId;
    private String description;
    private Long scanCount;
    private String imageUrl;
    private String qrInstalledImagePath;
    private String designOptions;
    private String installationLocation;
    private String installationLocationLat;
    private String installationLocationLng;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
    private LocalDateTime createDate;
    private String createUser;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
    private LocalDateTime updateDate;
    private String updateUser;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
    private LocalDateTime validFromDate;

    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
    private LocalDateTime validToDate;

    public static QrCodeResponseDto fromEntity(QrCode qrCode) {
        String calculatedImageUrl = null;
        String calculatedInstalledImageUrl = null;
        String relativeImagePath = qrCode.getImagePath(); // DB에 저장된 상대 경로
        String relativeInstalledImagePath = qrCode.getQrInstalledImagePath(); // DB에 저장된 상대 경로

        if (relativeImagePath != null && !relativeImagePath.isBlank()) {
            // Windows 경로 구분자(\)를 URL 구분자(/)로 변경
            String urlPath = relativeImagePath.replace("\\", "/");
            // ServletUriComponentsBuilder를 사용하여 현재 요청의 기본 URL(http://localhost:8081 등)을 가져오고
            // 여기에 /qr-images/ 와 변환된 파일 경로를 붙여 절대 URL 생성
            calculatedImageUrl = ServletUriComponentsBuilder.fromCurrentContextPath()
                    .path("/qr-images/") // WebConfig에서 설정한 URL 접두사
                    .path(urlPath)      // 상대 이미지 경로
                    .toUriString();
        }

        if (relativeInstalledImagePath != null && !relativeInstalledImagePath.isBlank()) {
            // Windows 경로 구분자(\)를 URL 구분자(/)로 변경
            String urlPath = relativeInstalledImagePath.replace("\\", "/");
            // ServletUriComponentsBuilder를 사용하여 현재 요청의 기본 URL(http://localhost:8081 등)을 가져오고
            // 여기에 /qr-images/ 와 변환된 파일 경로를 붙여 절대 URL 생성
            calculatedInstalledImageUrl = ServletUriComponentsBuilder.fromCurrentContextPath()
                    .path("/qr-images/") // WebConfig에서 설정한 URL 접두사
                    .path(urlPath)      // 상대 이미지 경로
                    .toUriString();
        }

        return QrCodeResponseDto.builder()
                .qrCodeId(qrCode.getQrCodeId())
                .projectId(qrCode.getProjectId())
                .projectName(qrCode.getProjectName()) // Assumes QrCode model has projectName populated from join
                .qrName(qrCode.getQrName())
                .qrType(qrCode.getQrType())
                .status(qrCode.getStatus())
                .targetContent(qrCode.getTargetContent())
                .linkedEventId(qrCode.getLinkedEventId())
                .linkedLandingPageId(qrCode.getLinkedLandingPageId())
                .description(qrCode.getDescription())
                .scanCount(qrCode.getScanCount())
                .imageUrl(calculatedImageUrl) // 계산된 전체 절대 URL 설정
                .qrInstalledImagePath(calculatedInstalledImageUrl)
                .designOptions(qrCode.getDesignOptions())
                .installationLocation(qrCode.getInstallationLocation())
                .installationLocationLat(qrCode.getInstallationLocationLat())
                .installationLocationLng(qrCode.getInstallationLocationLng())
                .createDate(qrCode.getCreateDate())
                // .createUser(qrCode.getCreateUser()) // QrCode 엔티티 확인 필요, 일단 주석 처리
                .updateDate(qrCode.getUpdateDate())
                // .updateUser(qrCode.getUpdateUser()) // QrCode 엔티티 확인 필요, 일단 주석 처리
                .validFromDate(qrCode.getValidFromDate())
                .validToDate(qrCode.getValidToDate())
                .build();
    }
}
