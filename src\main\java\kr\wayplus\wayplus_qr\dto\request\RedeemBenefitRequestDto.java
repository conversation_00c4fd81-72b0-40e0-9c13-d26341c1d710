package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * 혜택 사용(승인) 처리 요청 DTO.
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RedeemBenefitRequestDto {
    /** 스캔된 참가자 QR의 confirmationCode */
    @NotNull
    private String confirmationCode;

    /** 사용(승인)할 혜택 ID */
    @NotNull
    private Long benefitId;
}
