<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.AttendeeBenefitRedemptionMapper">

    <resultMap id="RedemptionResultMap" type="kr.wayplus.wayplus_qr.entity.AttendeeBenefitRedemption">
        <id property="redemptionId" column="redemption_id"/>
        <result property="attendeeId" column="attendee_id"/>
        <result property="benefitId" column="benefit_id"/>
        <result property="redeemedAt" column="redeemed_at"/>
        <result property="redeemedByUserEmail" column="redeemed_by_user_email"/>
        <result property="memo" column="memo"/>
    </resultMap>

    <!-- 특정 참가자의 사용 내역 -->
    <select id="selectRedemptionsByAttendeeId" resultMap="RedemptionResultMap">
        SELECT *
        FROM attendee_benefit_redemption
        WHERE attendee_id = #{attendeeId}
    </select>

    <!-- 참가자-혜택 단일 조회 -->
    <select id="selectByAttendeeAndBenefit" resultMap="RedemptionResultMap">
        SELECT *
        FROM attendee_benefit_redemption
        WHERE attendee_id = #{attendeeId}
          AND benefit_id = #{benefitId}
        LIMIT 1
    </select>

    <!-- 사용 기록 삽입 -->
    <insert id="insertRedemption" parameterType="kr.wayplus.wayplus_qr.entity.AttendeeBenefitRedemption" useGeneratedKeys="true" keyProperty="redemptionId">
        INSERT INTO attendee_benefit_redemption (
            attendee_id, benefit_id, redeemed_at, redeemed_by_user_email, memo
        ) VALUES (
            #{attendeeId}, #{benefitId}, NOW(), #{redeemedByUserEmail}, #{memo}
        )
    </insert>

    <!-- 사용 기록 삭제 (취소) -->
    <delete id="deleteRedemption" parameterType="map">
        DELETE FROM attendee_benefit_redemption
        WHERE attendee_id = #{attendeeId}
          AND benefit_id = #{benefitId}
    </delete>
</mapper>
