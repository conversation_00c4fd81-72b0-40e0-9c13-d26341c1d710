package kr.wayplus.wayplus_qr.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QrCode {
    private Long qrCodeId;
    private String qrUuid;
    private Long projectId;
    private String qrName;
    private String qrType; // Enum: URL, TEXT, VCARD, WIFI, EVENT_ATTENDANCE, LANDING_PAGE, EXCHANGE_COUPON
    private QrCodeStatus status; // Enum: ACTIVE, INACTIVE, EXPIRED, USED
    private String targetContent;
    private String description;
    private Long linkedLandingPageId;
    private Long linkedEventId;
    private String designOptions; // JSON
    private String imagePath; // qrImagePath -> imagePath 로 변경
    private String logoPath; // 로고 이미지 경로
    private Long scanCount;
    private LocalDateTime validFromDate;
    private LocalDateTime validToDate;
    private String installationLocation;
    private String installationLocationLat;
    private String installationLocationLng;
    private String qrInstalledImagePath;
    private String createUserEmail;
    private LocalDateTime createDate;
    private String updateUser; // updateUserEmail -> updateUser 로 변경
    private LocalDateTime updateDate; // lastUpdateDate -> updateDate 로 변경
    private String deleteUserEmail;
    private LocalDateTime deleteDate;
    private String useYn; // Enum: Y, N
    private String deleteYn; // Enum: Y, N

    // Project Name - For joining with projects table
    private String projectName;
}
