package kr.wayplus.wayplus_qr.dto.response;

import com.fasterxml.jackson.annotation.JsonUnwrapped;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.Page;
import java.util.List;

@Getter
@Setter
@AllArgsConstructor
public class ListResponseDto<T> {
    @JsonUnwrapped
    private Page<T> page;
    private List<SearchTypeInfo> availableSearchTypes;
    private Object answerType; // 답변 유형 정보를 담는 필드
    private List<AnswerTypeInfo> answerSearchTypes; // 답변 유형 목록 정보
    
    // 기존 생성자 유지 (하위 호환성)
    public ListResponseDto(Page<T> page, List<SearchTypeInfo> availableSearchTypes) {
        this.page = page;
        this.availableSearchTypes = availableSearchTypes;
        this.answerType = null;
        this.answerSearchTypes = null;
    }
}
