package kr.wayplus.wayplus_qr.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import kr.wayplus.wayplus_qr.dto.AttendeeManualCheckDto;
import kr.wayplus.wayplus_qr.dto.request.AttendeeRequestDto;
import kr.wayplus.wayplus_qr.dto.request.UpdateAttendeesConfirmStatusRequestDto;
import kr.wayplus.wayplus_qr.dto.response.*;
import kr.wayplus.wayplus_qr.entity.*;
import kr.wayplus.wayplus_qr.entity.enums.UserRole;
import kr.wayplus.wayplus_qr.entity.QrCodeStatus;
import kr.wayplus.wayplus_qr.exception.CustomEventException;
import kr.wayplus.wayplus_qr.exception.CustomAttendeeException;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.mapper.AttendeeMapper;
import kr.wayplus.wayplus_qr.mapper.EventMapper;
import kr.wayplus.wayplus_qr.mapper.PreRegistrationFormMapper;
import kr.wayplus.wayplus_qr.mapper.ProjectMapper;
import kr.wayplus.wayplus_qr.mapper.QrCodeMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import kr.wayplus.wayplus_qr.service.QrCodeGeneratorService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import kr.wayplus.wayplus_qr.dto.request.BulkAttendeeExcelRequestDto;
import kr.wayplus.wayplus_qr.dto.response.BulkRegistrationResultDto;

import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AttendeeService {
    @Value("${upload.qr-file.path}")
    private String qrCodeImagePath;
    

    private final AttendeeMapper attendeeMapper;
    private final EventMapper eventMapper; 
    private final QrCodeMapper qrCodeMapper; 
    private final ProjectMapper projectMapper;
    private final PreRegistrationFormMapper preRegistrationFormMapper;
    private final PreRegistrationFormService preRegistrationFormService;
    private final QrCodeGeneratorService qrCodeGeneratorService;
    private final TeamService teamService;
    private final UserMapper userMapper;
    private final ObjectMapper objectMapper; 
    

    /**
     * 사전 등록 신청 처리 (QR코드 생성 및 저장 포함)
     * @param attendeeRequestDto 사용자 입력 데이터
     * @param formDto 사전 등록 양식 정보
     * @return 등록된 Attendee 객체와 QR 이미지 경로를 포함하는 DTO
     */
    @Transactional
    public AttendeeRegistrationResult registerAttendee(AttendeeRequestDto attendeeRequestDto, PreRegistrationFormResponseDto formDto, EventResponseDto eventDto) { 
        if (attendeeRequestDto.getEventId() == null) {
            log.error("Event ID is null in AttendeeRequestDto at the beginning of registerAttendee method.");
            throw new CustomEventException(ErrorCode.INVALID_INPUT_VALUE, "Internal Server Error: Event ID is missing.");
        }

        log.info("Registering attendee for event ID: {}", attendeeRequestDto.getEventId()); // eventId 로깅 추가

        // 1. 고유 확인 코드 생성
        String confirmationCode = UUID.randomUUID().toString().substring(0, 8).toUpperCase();

        // 2. Attendee 객체 생성 (Setter 사용)
        Attendee attendee = Attendee.builder()
                .eventId(attendeeRequestDto.getEventId())
                .teamId(attendeeRequestDto.getTeamId())
                .attendeeName(attendeeRequestDto.getAttendeeName())
                .attendeeEmail(attendeeRequestDto.getAttendeeEmail())
                .attendeeContact(attendeeRequestDto.getAttendeeContact())
                .formId(formDto.getFormId()) 
                .build();
        // submitData는 JSON 문자열로 변환하여 저장
        try {
            attendee.setSubmissionData(objectMapper.writeValueAsString(attendeeRequestDto.getSubmissionData()));
        } catch (JsonProcessingException e) {
            log.error("Error converting submission data to JSON for attendee {}: {}", attendeeRequestDto.getEventId(), e.getMessage());
            attendee.setSubmissionData("[]"); // 오류 발생 시 빈 JSON 배열 저장
        }
        attendee.setConfirmationCode(confirmationCode);
        // 자동 확정 여부('auto_confirm_yn')에 따라 attendedConfirmYn 설정
        String flag = "Y".equals(formDto.getAutoConfirmYn()) ? "Y" : "N";
        if ( attendeeRequestDto.getAttendedConfirmYn() != null) {
            flag = attendeeRequestDto.getAttendedConfirmYn();
        } 
        attendee.setAttendedConfirmYn(flag); 
        attendee.setAttendedYn( attendeeRequestDto.getAttendedYn() == null ? "PENDING" : attendeeRequestDto.getAttendedYn()); // 초기값 'N' 설정 추가
        attendee.setCreateUserEmail(null);
        attendee.setUseYn("Y");
        attendee.setDeleteYn("N");

        // 3. 팀 정원 확인 (팀 ID가 있는 경우)
        if (attendee.getTeamId() != null) {
            try {
                teamService.validateTeamCapacity(attendee.getTeamId(), null, eventDto.getProjectId());
                log.info("Team capacity validation passed for team ID: {}", attendee.getTeamId());
            } catch (Exception e) {
                log.warn("Team capacity validation failed for team ID: {}, Error: {}", attendee.getTeamId(), e.getMessage());
                throw e; // 팀 정원 초과 시 예외를 그대로 전파
            }
        }

        // 4. 참석자 정보 DB 저장
        try {
            attendeeMapper.insertAttendee(attendee);
            log.info("Attendee registered successfully with ID: {} for Event ID: {}", attendee.getAttendeeId(), attendee.getEventId());
        } catch (DataIntegrityViolationException e) {
            log.error("Data integrity violation while registering attendee for event {}: {}", attendee.getEventId(), e.getMessage());
            // 이메일 또는 확인 코드 중복 가능성
            if (e.getMessage() != null && e.getMessage().contains("Duplicate entry")) {
                if (e.getMessage().contains("'attendees.email_event_id_unique'")) {
                    throw new CustomEventException(ErrorCode.EMAIL_DUPLICATION, "Email already registered for this event.");
                } else if (e.getMessage().contains("'attendees.confirmation_code_unique'")) {
                    throw new CustomEventException(ErrorCode.QR_CODE_GENERATION_FAILED, "Failed to generate unique confirmation code. Please try again.");
                }
            }
            throw new CustomEventException(ErrorCode.INTERNAL_SERVER_ERROR, "Failed to register attendee due to data integrity issue.");
        } catch (DataAccessException e) {
            log.error("Database access error during attendee registration for email {}: {}", attendee.getAttendeeEmail(), e.getMessage(), e);
            throw new CustomEventException(ErrorCode.DATABASE_ERROR, "Attendee registration failed due to a database error.");
        } catch (Exception e) {
            log.error("Error saving attendee for event {}: {}", attendee.getEventId(), e.getMessage(), e);
            throw new CustomEventException(ErrorCode.INTERNAL_SERVER_ERROR, "Failed to save attendee information.");
        }

        // 4. QR 코드 생성 및 저장
        String qrCodeFileName = null;
        try {
            String fileName = "qr_" + attendee.getConfirmationCode().replaceAll("[^a-zA-Z0-9_]", "_") + ".svg"; // 안전한 파일 이름 생성
            Path directoryPath = Paths.get(qrCodeImagePath);
            Files.createDirectories(directoryPath); // 디렉토리 생성 (없으면)
            String filePath = directoryPath.resolve(fileName).toString(); // 전체 파일 경로 생성

            qrCodeGeneratorService.generateQrCodeSvgFile(
                    confirmationCode,          // content
                    300,                       // width
                    300,                       // height
                    filePath,                  // filePath
                    "#000000",                 // foregroundColor (검정)
                    "#FFFFFF",                 // backgroundColor (흰색)
                    null,                      // finderColor (기본값 사용)
                    "square",                  // finderType (기본값)
                    null,                      // logoInputStream (로고 없음)
                    0.0,                       // logoRatio
                    com.google.zxing.qrcode.decoder.ErrorCorrectionLevel.M     // errorCorrectionLevel (중간)
            );

            qrCodeFileName = fileName; // 저장된 파일 이름 저장
            Event event = eventMapper.selectEventById(attendee.getEventId())
                    .orElseThrow(() -> new CustomEventException(ErrorCode.EVENT_NOT_FOUND, "Event not found with ID: " + attendee.getEventId()));
            eventDto = EventResponseDto.fromEntity(event);

            QrCode qrCode = new QrCode();
            qrCode.setProjectId(eventDto.getProjectId()); // event -> eventDto
            // qr_name에 confirmationCode를 추가하여 고유성 보장
            qrCode.setQrName(eventDto.getEventName() + " QR-" + confirmationCode);
            qrCode.setQrType("EVENT_ATTENDANCE");
            qrCode.setTargetContent(confirmationCode);
            qrCode.setLinkedEventId(attendee.getEventId());
            qrCode.setQrUuid(UUID.randomUUID().toString());
            qrCode.setCreateUserEmail(null); // 생성자는 본인 이메일로
            qrCode.setCreateDate(LocalDateTime.now());
            qrCode.setStatus(QrCodeStatus.ACTIVE);
            qrCode.setUseYn("Y");
            qrCode.setDeleteYn("N");
            qrCode.setImagePath("/" + qrCodeFileName);

            qrCodeMapper.insertQrCode(qrCode);
            log.info("QR Code metadata saved to qr_codes table with ID: {}", qrCode.getQrCodeId());

        } catch (DataIntegrityViolationException e) {
            log.error("Data integrity violation during QR code registration for attendee {}: {}", attendee.getAttendeeId(), e.getMessage(), e);
            throw new CustomEventException(ErrorCode.QR_CODE_REGISTRATION_FAILED_DUPLICATE, "QR code registration failed due to data integrity issue (e.g., duplicate entry).");
        } catch (DataAccessException e) {
            log.error("Database access error during QR code registration for attendee {}: {}", attendee.getAttendeeId(), e.getMessage(), e);
            throw new CustomEventException(ErrorCode.DATABASE_ERROR, "QR code registration failed due to a database error.");
        } catch (Exception e) { // QRcodeException 포함한 일반 예외 처리
            log.error("Failed to generate or save QR code for attendee ID {}: {}", attendee.getAttendeeId(), e.getMessage(), e);
            throw new CustomEventException(ErrorCode.QR_CODE_GENERATION_FAILED, "QR 코드 생성 또는 저장 중 오류가 발생했습니다.");
        }

        // 결과 반환
        return new AttendeeRegistrationResult(attendee, qrCodeFileName); // Attendee와 QR 파일 이름 반환
    }

    /**
     * 특정 이벤트의 상세 정보와 참석자 목록(페이지네이션)을 함께 조회합니다. (복원된 메소드)
     *
     * @param eventId  조회할 이벤트 ID
     * @param pageable 페이지네이션 정보
     * @return 이벤트 정보 및 참석자 목록 DTO
     */
    @Transactional(readOnly = true)
    public EventWithAttendeesResponseDto getEventWithAttendees(Long eventId, Pageable pageable) {
        // 1. 이벤트 정보 조회
        Event event = eventMapper.selectEventById(eventId)
                .orElseThrow(() -> new CustomEventException(
                        ErrorCode.EVENT_NOT_FOUND,
                        "이벤트를 찾을 수 없습니다: ID=" + eventId));

        // 2. 해당 이벤트의 참석자 목록 조회 (페이지네이션 적용)
        List<AttendeeSummaryDto> attendees = attendeeMapper.selectAttendeesByEventId(eventId, pageable);

        // 3. 해당 이벤트의 전체 참석자 수 조회 (페이지네이션 정보 생성용)
        long totalAttendees = attendeeMapper.countAttendeesByEventId(eventId);

        // 4. Page 객체 생성
        Page<AttendeeSummaryDto> attendeePage = new PageImpl<>(attendees, pageable, totalAttendees);

        // 5. 최종 응답 DTO 생성 및 반환
        EventResponseDto eventDetailsDto = EventResponseDto.fromEntity(event);

        // SubmissionData 변환 로직 적용
        transformSubmissionData(attendeePage.getContent());

        return EventWithAttendeesResponseDto.builder()
                .eventDetails(eventDetailsDto)
                .attendees(attendeePage)
                .build();
    }


    /**
     * 관리자가 선택한 특정 프로젝트의 모든 이벤트 참석자 목록 조회 (페이지네이션)
     * @param adminEmail 관리자 이메일 (권한 확인용)
     * @param projectId  프로젝트 ID
     * @param pageable   페이지 정보
     * @return 특정 프로젝트의 모든 참석자 목록 페이지
     */
    @Transactional(readOnly = true)
    public Page<AttendeeSummaryDto> getAttendeesForAdminProject(String adminEmail, Long projectId, String searchType, String searchKeyword, Pageable pageable) {

        // 1. 관리자 이메일로 사용자 조회
        User adminUser = userMapper.selectUserByEmail(adminEmail)
                .orElseThrow(() -> new AccessDeniedException("Admin user not found with email: " + adminEmail));

        // 2. 관리자가 해당 프로젝트에 접근 권한이 있는지 확인
        List<String> adminRoles = List.of("SUPER_ADMIN", "PROJECT_ADMIN");
        String userRole = userMapper.selectUserRoleByEmail(adminEmail)
                .orElseThrow(() -> new CustomEventException(ErrorCode.USER_NOT_FOUND, "사용자를 찾을 수 없습니다: " + adminEmail));
        if (!userRole.equals(UserRole.SUPER_ADMIN.name())) {
            boolean isAdminOfProject = projectMapper.isUserAdminOfProject(adminUser.getUserEmail(), projectId, adminRoles);
            if (!isAdminOfProject) {
                throw new AccessDeniedException("User " + adminEmail + " does not have admin access to project " + projectId);
            }
        }

        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "attendeeName": searchColumn = "attendee_name"; break;
                case "attendeeEmail": searchColumn = "attendee_email"; break;
                case "attendeeContact": searchColumn = "attendee_contact"; break;
                case "teamName": searchColumn = "team_name"; break;
                case "createDate": searchColumn = "create_date"; break;
            }
        }

        // 3. 프로젝트 ID와 페이지 정보로 참석자 목록 조회
        List<AttendeeSummaryDto> attendees = attendeeMapper.selectAttendeesByProjectId(projectId, searchColumn, searchKeyword, pageable);
        if (attendees == null) {
            attendees = Collections.emptyList();
        }

        // SubmissionData 변환 로직 적용
        transformSubmissionData(attendees);

        // 4. 프로젝트 ID와 검색어로 전체 참석자 수 조회
        long totalAttendees = attendeeMapper.countAttendeesByProjectId(projectId, searchColumn, searchKeyword);

        // 5. Page 객체 생성 및 반환
        return new PageImpl<>(attendees, pageable, totalAttendees);
    }

    /**
     * 관리자가 특정 이벤트의 참석자를 삭제합니다. (복원된 메소드)
     *
     * @param adminEmail 관리자 이메일 (권한 확인용)
     * @param eventId    이벤트 ID
     * @param attendeeId 삭제할 참석자 ID
     */
    @Transactional
    public void deleteAttendee(String adminEmail, Long eventId, Long attendeeId) {
        // 1. 관리자 정보 확인
        userMapper.selectUserByEmail(adminEmail)
                .orElseThrow(() -> new AccessDeniedException("Admin user not found: " + adminEmail));

        // 2. 이벤트 정보 조회 (프로젝트 ID 확인 및 존재 확인용)
        Event event = eventMapper.selectEventById(eventId)
                .orElseThrow(() -> new CustomEventException(ErrorCode.EVENT_NOT_FOUND, "Event not found: ID=" + eventId));

        // 3. 관리자가 해당 이벤트가 속한 프로젝트의 관리자인지 확인
        List<String> adminRoles = List.of("SUPER_ADMIN", "PROJECT_ADMIN");
        String userRole = userMapper.selectUserRoleByEmail(adminEmail)
                .orElseThrow(() -> new CustomEventException(ErrorCode.USER_NOT_FOUND, "사용자를 찾을 수 없습니다: " + adminEmail));
        if (!userRole.equals(UserRole.SUPER_ADMIN.name())) {
            boolean isAdminOfProject = projectMapper.isUserAdminOfProject(adminEmail, event.getProjectId(), adminRoles);
            if (!isAdminOfProject) {
                throw new AccessDeniedException("User " + adminEmail + " does not have admin access to the project containing event " + eventId);
            }
        }

        // 4. 삭제할 참석자 정보 조회 (존재 확인 및 이벤트 소속 확인용)
        Attendee attendeeToDelete = attendeeMapper.selectAttendeeById(attendeeId)
                .orElseThrow(() -> new CustomEventException(ErrorCode.ATTENDEE_NOT_FOUND, "Attendee not found: ID=" + attendeeId));

        // 5. 참석자가 실제로 해당 이벤트에 속하는지 확인
        if (!attendeeToDelete.getEventId().equals(eventId)) {
            throw new CustomEventException(ErrorCode.ATTENDEE_NOT_FOUND, "Attendee " + attendeeId + " does not belong to event " + eventId);
        }

        // 6. 참석자 삭제 실행 (논리적 삭제: deleteYn='Y', deleteDate, deleteUserEmail 업데이트)
        int updatedRows = attendeeMapper.markAttendeeAsDeleted(attendeeId, adminEmail);
        if (updatedRows == 0) {
            log.warn("Attempted to delete attendee {} but it was already deleted or not found.", attendeeId);
        } else {
            log.info("Attendee marked as deleted successfully: ID={}, Deleted by: {}", attendeeId, adminEmail);
        }
    }

    /**
     * 확인 코드로 참석 상태 변경 (복원된 메소드)
     * @param confirmationCode 확인 코드
     * @param adminEmail 관리자 이메일
     */
    @Transactional
    public void markAttendanceByCode(String confirmationCode, String adminEmail) {
        log.info("Attempting to mark attendance for confirmation code: {}", confirmationCode);

        // --- 스캔된 QR 코드 자체의 스캔 수 증가 (별도 트랜잭션) --- 
        // confirmationCode (QR 내용) 를 사용하여 해당 QR 코드를 직접 찾음
        qrCodeMapper.selectQrCodeByTargetContent(confirmationCode).ifPresentOrElse(
                qrCode -> {
                    String qrUuid = qrCode.getQrUuid();
                    log.info("Found QR code (UUID: {}) matching targetContent (confirmationCode: {}). Incrementing scan count.", qrUuid, confirmationCode);
                    // qrCodeScanService.incrementScanCount(qrUuid);
                },
                () -> {
                    // 이론적으로 참석자 등록 시 QR이 생성되므로 코드가 유효하다면 찾아야 함.
                    // 못 찾는 경우는 QR이 비활성화되었거나, 삭제되었거나, DB 불일치 등 예외적인 상황.
                    log.warn("Could not find an active EVENT_ATTENDANCE QR code with targetContent matching the confirmation code: {}. Scan count cannot be incremented.", confirmationCode);
                }
        );
        // --- 스캔 수 증가 로직 끝 --- 

        // 1. 코드로 참석자 조회
        AttendeeSummaryDto attendee = attendeeMapper.selectAttendeeByConfirmationCode(confirmationCode)
                .orElseThrow(() -> {
                    log.warn("Attendee not found with confirmation code: {}", confirmationCode);
                    return new CustomEventException(ErrorCode.ENTITY_NOT_FOUND, "Attendee not found with confirmation code: " + confirmationCode);
                });

        // 2. 이벤트 정보 조회
        Event event = eventMapper.selectEventById(attendee.getEventId())
                .orElseThrow(() -> {
                    log.error("Event not found for attendee with confirmation code: {}", confirmationCode);
                    // 참석자와 연결된 이벤트가 없는 경우는 서버 오류로 간주
                    return new CustomEventException(ErrorCode.INTERNAL_SERVER_ERROR, "Associated event not found.");
                });

        LocalDateTime now = LocalDateTime.now();

        // 이벤트 기간 확인 (null 처리)
        boolean isBeforeEventStart = event.getStartDate() != null && now.isBefore(event.getStartDate());
        boolean isAfterEventEnd = event.getEndDate() != null && now.isAfter(event.getEndDate());

        if (isBeforeEventStart || isAfterEventEnd) {
            log.warn("Attendance attempt outside of event period for code {}. Event period: {} - {}",
                     confirmationCode, event.getStartDate(), event.getEndDate());
            throw new CustomEventException(ErrorCode.EVENT_NOT_IN_PROGRESS);
        }

        // 3. 프로젝트 관리자 권한 확인
        List<String> adminRoles = List.of("SUPER_ADMIN", "PROJECT_ADMIN");
        String userRole = userMapper.selectUserRoleByEmail(adminEmail)
                        .orElseThrow(() -> new CustomEventException(ErrorCode.USER_NOT_FOUND, "사용자를 찾을 수 없습니다: " + adminEmail));
        if (!userRole.equals(UserRole.SUPER_ADMIN.name())) {
            boolean isAdminOfProject = projectMapper.isUserAdminOfProject(adminEmail, event.getProjectId(), adminRoles);
            if (!isAdminOfProject) {
                log.warn("User {} is not admin of project {} for QR scan code {}", adminEmail, event.getProjectId(), confirmationCode);
                throw new AccessDeniedException("User " + adminEmail + " does not have admin access to project " + event.getProjectId());
            }
        }

        // 4. 참석 확정 여부 확인 ('N'이면 참석 불가)
        if ("N".equals(attendee.getAttendedConfirmYn())) {
            log.warn("Attendance not allowed for attendee with confirmation code {} (attendedConfirmYn is 'N')", confirmationCode);
            throw new CustomEventException(ErrorCode.ATTENDANCE_NOT_ALLOWED);
        }

        // 5. 이미 참석했는지 확인
        if ("Y".equals(attendee.getAttendedYn())) {
            log.info("Attendee with confirmation code {} has already attended.", confirmationCode);
            throw new CustomEventException(ErrorCode.ALREADY_ATTENDED, "이미 참석한 참석자입니다.");
        }

        // 6. 참석 상태 업데이트 ('Y'로 변경) 및 참석 시간 기록
        attendeeMapper.updateAttendedConfirmYnAndDateByCode(confirmationCode, "Y", now);
        log.info("Successfully marked attendance for confirmation code: {}", confirmationCode);
    }

    /**
     * 수동 참석 확인을 위한 참석자 검색 (복원된 메소드)
     * @param eventId 이벤트 ID
     * @param name 참석자 이름 (부분 일치 검색, null 가능)
     * @return 검색된 참석자 목록 (AttendeeManualCheckDto 리스트)
     */
    public List<AttendeeManualCheckDto> searchAttendeesForManualCheck(Long eventId, String name) {
        log.info("Searching attendees for manual check. Event ID: {}, Name filter: '{}'", eventId, name);
        String searchName = (name != null && !name.trim().isEmpty()) ? "%" + name.trim() + "%" : null; // 부분 일치 검색
        List<AttendeeManualCheckDto> attendees = attendeeMapper.selectAttendeesForManualCheck(eventId, searchName);
        log.info("Found {} attendees for manual check.", attendees.size());
        return attendees;
    }

    /**
     * 수동으로 참석 상태 업데이트 (관리자용)
     * @param attendeeId 참석자 ID
     * @param attended 참석으로 처리할지 여부 (true: 참석, false: 미참석)
     */
    @Transactional
    public void updateAttendanceStatusManually(Long attendeeId, boolean attended) {
        log.info("Attempting to manually update attendance status for attendee ID: {} to attended={}", attendeeId, attended);

        // 1. ID로 참석자 조회
        Attendee attendee = attendeeMapper.selectAttendeeById(attendeeId)
                .orElseThrow(() -> {
                    log.warn("Attendee not found with ID: {}", attendeeId);
                    // ErrorCode.ATTENDEE_NOT_FOUND 사용하는 것이 더 적절해 보입니다.
                    // return new CustomEventException(ErrorCode.ENTITY_NOT_FOUND, "Attendee not found with ID: " + attendeeId);
                    return new CustomEventException(ErrorCode.ATTENDEE_NOT_FOUND);
                });

        // 2. 참석 상태 및 시간 결정
        String attendedYn = attended ? "Y" : "N";
        LocalDateTime attendedDate = attended ? LocalDateTime.now() : null;

        // 3. 참석 상태 및 시간 업데이트
        int updatedRows = attendeeMapper.updateAttendanceStatus(attendeeId, attendedYn, attendedDate);
        
        if (updatedRows > 0) {
            log.info("Successfully manually updated attendance status for attendee ID: {} to attendedYn={}, attendedDate={}", attendeeId, attendedYn, attendedDate);
        } else {
            // 업데이트가 실패한 경우 (예: ID가 갑자기 사라짐 - 거의 발생하지 않음)
            log.error("Failed to update attendance status for attendee ID: {}. No rows affected.", attendeeId);
            // 필요하다면 DATABASE_ERROR 와 같은 에러를 발생시킬 수 있습니다.
            // throw new CustomEventException(ErrorCode.DATABASE_ERROR, "Failed to update attendee status.");
        }
    }

    /**
     * 여러 참가자의 참석 확정 상태 (attended_confirm_yn)를 일괄 업데이트합니다.
     *
     * @param requestDto 참가자 ID 목록과 업데이트할 상태 값 ('Y' 또는 'N')
     */
    @Transactional
    public void updateAttendeesConfirmStatus(UpdateAttendeesConfirmStatusRequestDto requestDto) {
        if (requestDto == null || requestDto.getAttendeeIds() == null || requestDto.getAttendeeIds().isEmpty() || requestDto.getAttendedConfirmYn() == null) {
            log.warn("Invalid request for updating attendees confirm status: request or attendeeIds or status is null/empty.");
            // DTO 레벨에서 @NotEmpty, @NotNull 등으로 처리되지만, 추가 방어 코드
            throw new IllegalArgumentException("참가자 ID 목록과 상태 값은 필수입니다.");
        }

        List<Long> attendeeIds = requestDto.getAttendeeIds();
        String attendedConfirmYn = requestDto.getAttendedConfirmYn();

        log.info("Attempting to update attended_confirm_yn to '{}' for {} attendees: {}",
                attendedConfirmYn, attendeeIds.size(), attendeeIds);

        attendeeMapper.updateAttendeesConfirmStatus(attendeeIds, attendedConfirmYn);

        log.info("Successfully updated attended_confirm_yn to '{}' for {} attendees.",
                attendedConfirmYn, attendeeIds.size());
    }

    // --- Helper Methods ---

    /**
     * 참석자 목록의 SubmissionData를 fieldName -> fieldLabel로 변환합니다.
     * @param attendees 참석자 DTO 목록
     */
    private void transformSubmissionData(List<AttendeeSummaryDto> attendees) {
        if (attendees == null || attendees.isEmpty()) {
            log.debug("transformSubmissionData: Attendee list is null or empty. Skipping transformation.");
            return;
        }
        log.debug("transformSubmissionData: Starting transformation for {} attendees.", attendees.size());

        // 각 DTO에 대해 변환 수행
        for (AttendeeSummaryDto attendeeDto : attendees) {
            Long attendeeId = attendeeDto.getAttendeeId();
            Long formId = attendeeDto.getFormId();
            log.debug("transformSubmissionData: Processing attendeeId={}, formId={}", attendeeId, formId);

            if (formId == null) {
                log.warn("transformSubmissionData: Skipping attendeeId={} due to null formId.", attendeeId);
                continue; // formId가 없으면 변환 불가
            }

            if (attendeeDto.getSubmissionData() == null || attendeeDto.getSubmissionData().isEmpty()) {
                 log.warn("transformSubmissionData: Skipping attendeeId={} (formId={}) due to null or empty submissionData.", attendeeId, formId);
                continue; // 원본 데이터 없으면 변환 불가
            }

            Map<String, Object> originalSubmissionData = attendeeDto.getSubmissionData();
            log.debug("transformSubmissionData: AttendeeId={}, Original submissionData keys: {}", attendeeId, originalSubmissionData.keySet());


            try {
                 // 폼 필드 정의 가져오기 (폼 존재 여부 확인용)
                log.debug("transformSubmissionData: Checking existence of form definition for formId={}", formId);
                PreRegistrationFormResponseDto formDto =preRegistrationFormService.getFormById(formId);

                if (formDto == null) {
                    log.error("transformSubmissionData: Form definition for formId={} not found or deleted. Skipping transformation for attendeeId={}.", formId, attendeeId);
                    continue;
                }
                 // 실제 변환에 사용할 필드 목록 가져오기 (삭제된 필드 포함)
                 log.debug("transformSubmissionData: Retrieving all fields (including deleted) for formId={}", formId);
                 List<PreRegistrationFormField> allFields = preRegistrationFormService.getAllFieldsForForm(formId);

                 if (allFields == null || allFields.isEmpty()) {
                     log.warn("transformSubmissionData: Form definition for formId={} has no fields (even deleted ones). Skipping transformation for attendeeId={}.", formId, attendeeId);
                     continue;
                 }
                 log.debug("transformSubmissionData: Successfully retrieved {} fields (including deleted) for formId={}.", allFields.size(), formId);


                Map<String, String> fieldLabelMap = allFields.stream()
                        .filter(field -> field.getFieldName() != null && field.getFieldLabel() != null)
                        .collect(Collectors.toMap(
                                field -> field.getFieldName(),
                                field -> field.getFieldLabel(),
                                (existing, replacement) -> existing // 중복 키 처리
                        ));

                 log.debug("transformSubmissionData: Created fieldLabelMap for formId={} with size: {}", formId, fieldLabelMap.size());

                if (fieldLabelMap.isEmpty()) {
                    log.warn("transformSubmissionData: fieldLabelMap is empty for formId={}. AttendeeId={} submission data might not be fully transformed.", formId, attendeeId);
                    // 라벨 맵이 비어있어도 필드명으로라도 변환 시도
                }

                // 변환된 데이터를 담을 새 Map (순서 유지를 위해 LinkedHashMap 사용)
                Map<String, Object> transformedSubmissionData = new LinkedHashMap<>();

                // 원래 submissionData 순회하며 변환
                for (Map.Entry<String, Object> entry : originalSubmissionData.entrySet()) {
                    String fieldName = entry.getKey();
                    // fieldLabelMap이 비어있을 경우 getOrDefault가 fieldName을 반환
                    String fieldLabel = fieldLabelMap.getOrDefault(fieldName, fieldName);
                    transformedSubmissionData.put(fieldLabel, entry.getValue());
                }
                 log.debug("transformSubmissionData: AttendeeId={}, Transformed submissionData keys: {}", attendeeId, transformedSubmissionData.keySet());


                // 변환된 Map으로 DTO의 submissionData 업데이트
                attendeeDto.setSubmissionData(transformedSubmissionData);
                 log.debug("transformSubmissionData: Successfully transformed and updated submissionData for attendeeId={}", attendeeId);

            } catch (Exception e) {
                 log.error("transformSubmissionData: Error transforming submission data for attendeeId={} (formId={}). Original data might be kept.", attendeeId, formId, e);
                 // 에러 발생 시 원본 데이터 유지 (이미 attendeeDto에 있음)
            }
        }
         log.debug("transformSubmissionData: Finished transformation process.");
    }

    // 참가자 정렬 필드 매핑
    private static final Map<String, String> ATTENDEE_SORT_PROPERTY_MAP = Map.of(
            "attendeeId", "a.attendee_id",
            "attendeeName", "a.attendee_name",
            "attendeeContact", "a.attendee_contact",
            "attendeeEmail", "a.attendee_email",
            "registrationDate", "a.registration_date",
            "attendedYn", "a.attended_yn",
            "attendedConfirmYn", "a.attended_confirm_yn",
            "eventId", "e.event_id", // 이벤트 관련 필드 추가
            "eventName", "e.event_name",
            "formId", "a.form_id"     // 신청서 ID 추가
            // 필요에 따라 추가
    );

    /**
     * SUPER_ADMIN: 모든 참가자 목록 조회 (페이징 및 프로젝트/이벤트/신청서 필터링)
     */
    @Transactional(readOnly = true)
    public Page<AttendeeSummaryDto> getAllAttendeesForSuperAdmin(Long projectId, Long eventId, Long formId
        , String searchType, String searchKeyword, Pageable pageable) {
        
        //검색타입 설정. 실제 DB 컬럼과 연동.
        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "attendeeName": searchColumn = "attendee_name"; break;
                case "attendeeContact": searchColumn = "attendee_contact"; break;
                case "teamName": searchColumn = "team_name"; break;
                case "createDate": searchColumn = "create_date"; break;
            }
        }

        List<AttendeeSummaryDto> attendees = attendeeMapper.selectAttendeesForSuperAdmin(projectId, eventId, formId, searchColumn, searchKeyword, pageable);
        long total = attendeeMapper.countAttendeesForSuperAdmin(projectId, eventId, formId, searchColumn, searchKeyword);

        transformSubmissionData(attendees);

        return new PageImpl<>(attendees, pageable, total);
    }

    /**
     * 페이징 및 정렬 파라미터 검증 및 준비 (Attendee 용)
     */
    private Map<String, Object> validateAndPrepareAttendeeParams(Long projectId, Long eventId, Long formId, String searchType, String searchKeyword, Pageable pageable) {

        Map<String, Object> params = new HashMap<>();
        params.put("offset", pageable.getOffset());
        params.put("pageSize", pageable.getPageSize());

        // 필터링 파라미터 추가
        if (projectId != null) {
            params.put("projectId", projectId);
        }
        if (eventId != null) {
            params.put("eventId", eventId);
        }
        if (formId != null) {
            params.put("formId", formId);
        }
        if (searchKeyword != null && !searchKeyword.isEmpty()) {
            params.put("searchKeyword", searchKeyword);
        }

        String sortColumn = "a.registration_date"; // 기본 정렬 컬럼 (attendees 테이블 alias 'a' 사용)
        String sortDirection = "DESC"; // 기본 정렬 방향

        if (pageable.getSort().isSorted()) {
            Sort.Order order = pageable.getSort().iterator().next(); // 첫 번째 정렬 조건만 사용
            String property = order.getProperty();
            if (ATTENDEE_SORT_PROPERTY_MAP.containsKey(property)) {
                sortColumn = ATTENDEE_SORT_PROPERTY_MAP.get(property);
                sortDirection = order.getDirection().name();
            } else {
                log.warn("Invalid sort property received for attendee: {}. Using default sort.", property);
            }
        }

        params.put("sortColumn", sortColumn);
        params.put("sortDirection", sortDirection);
        log.debug("Prepared attendee query params: {}", params);
        return params;
    }

    /**
     * 엑셀 파일을 이용한 대량 사전 신청 등록
     * @param requestDto 엑셀 파일과 기본 정보를 포함한 요청 DTO
     * @return 대량 등록 결과
     */
    @Transactional
    public BulkRegistrationResultDto registerAttendeesFromExcel(BulkAttendeeExcelRequestDto requestDto) {
        log.info("Starting bulk attendee registration from Excel for event ID: {}", requestDto.getEventId());
        
        List<String> errorMessages = new ArrayList<>();
        StringBuilder failedRegistrationsBuilder = new StringBuilder();
        int successCount = 0;
        int totalCount = 0;
        
        try {
            // 1. 이벤트 존재 확인
            Event event = eventMapper.selectEventById(requestDto.getEventId())
                    .orElseThrow(() -> new CustomEventException(ErrorCode.EVENT_NOT_FOUND, "Event not found with ID: " + requestDto.getEventId()));
            EventResponseDto eventDto = EventResponseDto.fromEntity(event);
            
            PreRegistrationFormResponseDto formDto = new PreRegistrationFormResponseDto();
            
            // 3. 엑셀 파일 파싱
            List<AttendeeRequestDto> attendeeList = parseExcelFile(requestDto.getExcelFile(), requestDto, errorMessages);
            totalCount = attendeeList.size();
            
            // 4. 각 참석자 등록 처리
            for (int i = 0; i < attendeeList.size(); i++) {
                AttendeeRequestDto attendeeRequestDto = attendeeList.get(i);
                int rowNumber = i + 2; // 엑셀 행 번호 (헤더 제외)
                
                try {
                    // 개별 참석자 등록
                    registerAttendee(attendeeRequestDto, formDto, eventDto);
                    successCount++;
                    log.debug("Successfully registered attendee: {} (row {})", attendeeRequestDto.getAttendeeName(), rowNumber);
                } catch (Exception e) {
                    log.warn("Failed to register attendee: {} (row {}), Error: {}", 
                            attendeeRequestDto.getAttendeeName(), rowNumber, e.getMessage());
                    
                    String failedInfo = String.format("Row %d - %s (%s): %s", 
                            rowNumber, attendeeRequestDto.getAttendeeName(), 
                            attendeeRequestDto.getAttendeeEmail(), e.getMessage());
                    if (failedRegistrationsBuilder.length() > 0) {
                        failedRegistrationsBuilder.append("\n");
                    }
                    failedRegistrationsBuilder.append(failedInfo);
                }
            }
            
        } catch (CustomEventException e) {
            log.error("Event-related error during bulk attendee registration: {}", e.getMessage(), e);
            throw new CustomAttendeeException(e.getErrorCode(), "이벤트 관련 오류: " + e.getMessage());
        } catch (Exception e) {
            log.error("Unexpected error during bulk attendee registration: {}", e.getMessage(), e);
            throw new CustomAttendeeException(ErrorCode.INTERNAL_SERVER_ERROR, "엑셀 파일 처리 중 예상치 못한 오류가 발생했습니다: " + e.getMessage());
        }
        
        int failureCount = totalCount - successCount;
        
        BulkRegistrationResultDto result = BulkRegistrationResultDto.builder()
                .totalCount(totalCount)
                .successCount(successCount)
                .failureCount(failureCount)
                .errorMessages(errorMessages)
                .failedRegistrations(failedRegistrationsBuilder.toString())
                .build();
        
        log.info("Bulk registration completed. Total: {}, Success: {}, Failed: {}", 
                totalCount, successCount, failureCount);
        
        return result;
    }
    
    /**
     * 엑셀 파일을 파싱하여 AttendeeRequestDto 리스트로 변환
     * @param excelFile 엑셀 파일
     * @param requestDto 요청 DTO (기본값 포함)
     * @param errorMessages 오류 메시지 리스트
     * @return 파싱된 참석자 정보 리스트
     */
    private List<AttendeeRequestDto> parseExcelFile(MultipartFile excelFile, 
                                                   BulkAttendeeExcelRequestDto requestDto, 
                                                   List<String> errorMessages) {
        List<AttendeeRequestDto> attendeeList = new ArrayList<>();
        
        try (Workbook workbook = createWorkbook(excelFile)) {
            Sheet sheet = workbook.getSheetAt(0); // 첫 번째 시트 사용
            
            // 헤더 행 검증 (첫 번째 행)
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) {
                errorMessages.add("엑셀 파일에 헤더 행이 없습니다.");
                return attendeeList;
            }
            
            // 필수 컬럼 인덱스 찾기
            Map<String, Integer> columnIndexMap = findColumnIndexes(headerRow, errorMessages);
            if (!columnIndexMap.containsKey("name") || !columnIndexMap.containsKey("email")) {
                errorMessages.add("필수 컬럼(이름, 이메일)이 누락되었습니다.");
                return attendeeList;
            }
            
            // 데이터 행 처리 (두 번째 행부터)
            for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if (row == null || isEmptyRow(row)) {
                    continue; // 빈 행 건너뛰기
                }
                
                try {
                    AttendeeRequestDto attendeeDto = parseRowToAttendeeDto(row, columnIndexMap, requestDto);
                    if (attendeeDto != null) {
                        attendeeList.add(attendeeDto);
                    }
                } catch (Exception e) {
                    errorMessages.add(String.format("행 %d 파싱 오류: %s", rowIndex + 1, e.getMessage()));
                }
            }
            
        } catch (Exception e) {
            log.error("Error parsing Excel file: {}", e.getMessage(), e);
            errorMessages.add("엑셀 파일 읽기 오류: " + e.getMessage());
        }
        
        return attendeeList;
    }
    
    /**
     * 엑셀 파일 타입에 따라 Workbook 생성
     */
    private Workbook createWorkbook(MultipartFile excelFile) throws Exception {
        String fileName = excelFile.getOriginalFilename();
        if (fileName == null) {
            throw new IllegalArgumentException("파일 이름이 없습니다.");
        }
        
        if (fileName.endsWith(".xlsx")) {
            return new XSSFWorkbook(excelFile.getInputStream());
        } else if (fileName.endsWith(".xls")) {
            return new HSSFWorkbook(excelFile.getInputStream());
        } else {
            throw new IllegalArgumentException("지원하지 않는 파일 형식입니다. (.xls, .xlsx만 지원)");
        }
    }
    
    /**
     * 헤더 행에서 컬럼 인덱스 찾기
     */
    private Map<String, Integer> findColumnIndexes(Row headerRow, List<String> errorMessages) {
        Map<String, Integer> columnIndexMap = new HashMap<>();
        
        for (int cellIndex = 0; cellIndex < headerRow.getLastCellNum(); cellIndex++) {
            Cell cell = headerRow.getCell(cellIndex);
            if (cell != null) {
                String headerValue = getCellValueAsString(cell).trim().toLowerCase();
                
                // 컬럼 매핑 (다양한 헤더명 지원)
                if (headerValue.contains("이름") || headerValue.contains("name") || headerValue.equals("성명")) {
                    columnIndexMap.put("name", cellIndex);
                } else if (headerValue.contains("이메일") || headerValue.contains("email") || headerValue.contains("메일")) {
                    columnIndexMap.put("email", cellIndex);
                } else if (headerValue.contains("연락처") || headerValue.contains("전화") || headerValue.contains("phone") || headerValue.contains("contact")) {
                    columnIndexMap.put("contact", cellIndex);
                }
            }
        }
        
        return columnIndexMap;
    }
    
    /**
     * 행이 비어있는지 확인
     */
    private boolean isEmptyRow(Row row) {
        for (int cellIndex = 0; cellIndex < row.getLastCellNum(); cellIndex++) {
            Cell cell = row.getCell(cellIndex);
            if (cell != null && !getCellValueAsString(cell).trim().isEmpty()) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 엑셀 행을 AttendeeRequestDto로 변환
     */
    private AttendeeRequestDto parseRowToAttendeeDto(Row row, Map<String, Integer> columnIndexMap, 
                                                    BulkAttendeeExcelRequestDto requestDto) {
        String name = getCellValueAsString(row.getCell(columnIndexMap.get("name"))).trim();
        String email = getCellValueAsString(row.getCell(columnIndexMap.get("email"))).trim();
        
        // 필수 필드 검증
        if (name.isEmpty() || email.isEmpty()) {
            throw new IllegalArgumentException("이름과 이메일은 필수 입력 항목입니다.");
        }
        
        // 이메일 형식 간단 검증
        if (!email.contains("@") || !email.contains(".")) {
            throw new IllegalArgumentException("올바르지 않은 이메일 형식입니다: " + email);
        }
        
        String contact = "";
        if (columnIndexMap.containsKey("contact")) {
            contact = getCellValueAsString(row.getCell(columnIndexMap.get("contact"))).trim();
        }
        
        return AttendeeRequestDto.builder()
                .eventId(requestDto.getEventId())
                .teamId(requestDto.getTeamId())
                .attendeeName(name)
                .attendeeEmail(email)
                .attendeeContact(contact)
                .attendedYn(requestDto.getAttendedYn())
                .attendedConfirmYn(requestDto.getAttendedConfirmYn())
                .submissionData(new HashMap<>()) // 빈 맵으로 초기화
                .skipValidation(true) // 대량 등록 시 일부 검증 스킵
                .build();
    }
    
    /**
     * 셀 값을 문자열로 변환
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    // 숫자를 문자열로 변환 (소수점 제거)
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    public AttendeeSummaryDto getAttendeeByConfirmationCode(String confirmationCode) {
        AttendeeSummaryDto attendee = attendeeMapper.selectAttendeeByConfirmationCode(confirmationCode)
                .orElseThrow(() -> {
                    log.warn("Attendee not found with confirmation code: {}", confirmationCode);
                    return new CustomEventException(ErrorCode.ENTITY_NOT_FOUND, "Attendee not found with confirmation code: " + confirmationCode);
                });
        transformSubmissionData(Collections.singletonList(attendee));
        return attendee;
    }
}