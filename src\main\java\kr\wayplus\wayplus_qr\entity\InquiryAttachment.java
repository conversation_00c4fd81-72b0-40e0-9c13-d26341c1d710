package kr.wayplus.wayplus_qr.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 문의 첨부파일 Entity - inquiry_attachments 매핑.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryAttachment {
    private Long attachmentId;
    private Long inquiryId;   // nullable (댓글 첨부 시 null)
    private Long commentId;   // nullable (문의 첨부 시 null)
    private String originalFileName;
    private String storedFilePath;
    private Long fileSize;
    private String mimeType;
    private LocalDateTime createdAt;
}
