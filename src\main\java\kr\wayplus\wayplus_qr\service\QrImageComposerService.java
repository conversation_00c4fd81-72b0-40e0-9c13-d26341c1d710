package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.QrCodeDesignOptionsDto;

public interface QrImageComposerService {
    /**
     * 배경 이미지와 QR 코드를 합성하여 PNG 이미지로 생성합니다.
     *
     * @param contentToEncode QR 코드로 인코딩될 문자열
     * @param designOptions QR 코드 디자인 옵션
     * @param backgroundImagePath 배경 이미지 파일 경로
     * @param logoImagePath 로고 이미지 파일 경로 (없으면 null)
     * @param outputPngPath 출력 PNG 파일 경로
     */
    void generateQrWithBackgroundAsPng(
            String contentToEncode,
            QrCodeDesignOptionsDto designOptions,
            String backgroundImagePath,
            String logoImagePath,
            String outputPngPath
    );
}
