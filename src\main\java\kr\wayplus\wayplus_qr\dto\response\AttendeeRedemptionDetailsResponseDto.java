package kr.wayplus.wayplus_qr.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 참가자 QR 스캔 시, 참가자 기본 정보와 보유 혜택(사용 상태 포함)을 응답하기 위한 DTO.
 */
@Getter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AttendeeRedemptionDetailsResponseDto {
    /** 참가자 고유 ID */
    private Long attendeeId;
    /** 참가자 이름 */
    private String attendeeName;
    /** 참가자 이메일 */
    private String attendeeEmail;

    /** 이벤트 고유 ID */
    private Long eventId;
    /** 이벤트 이름 */
    private String eventName;

    /** QR 확인 코드 (화면 필요 시) */
    private String confirmationCode;

    /**
     * 참가 신청(등록) 시각
     */
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
    private LocalDateTime registrationDate;

    /**
     * 참가자에게 할당된 혜택들의 현재 사용 상태 리스트
     */
    private List<BenefitStatusDto> benefits;
}
