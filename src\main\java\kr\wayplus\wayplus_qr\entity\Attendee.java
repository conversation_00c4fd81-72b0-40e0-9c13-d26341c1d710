package kr.wayplus.wayplus_qr.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data 
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Attendee {
    private Long attendeeId;
    private Long eventId;
    private Long teamId;
    private Long formId;
    private String submissionData;
    private String attendeeName;
    private String attendeeContact;
    private String attendeeEmail;
    private String attendedYn;
    private String eventName;
    private LocalDateTime startDate;

    private String confirmationCode;
    private String qrCodePath; 
    private String attendedConfirmYn; 
    private LocalDateTime attendedDate; 

    private LocalDateTime registrationDate;
    private String registeredByAdminUserEmail;
    private String createUserEmail;
    private LocalDateTime createDate;
    private String updateUserEmail;
    private LocalDateTime lastUpdateDate;
    private String deleteUserEmail;
    private LocalDateTime deleteDate;
    private String useYn;
    private String deleteYn; 
}
