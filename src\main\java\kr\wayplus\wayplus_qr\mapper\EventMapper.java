package kr.wayplus.wayplus_qr.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import kr.wayplus.wayplus_qr.dto.request.EventUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.DailyCountDto;
import kr.wayplus.wayplus_qr.entity.Event;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Mapper
public interface EventMapper {
    int insertEvent(Event event);
    Optional<Event> selectEventById(@Param("eventId") Long eventId);
    List<Event> selectEvents(
        @Param("projectId") Long projectId,
        @Param("status") String status,
        @Param("searchColumn") String searchColumn,
        @Param("searchKeyword") String searchKeyword,
        @Param("pageable") Pageable pageable
    );
    int updateEvent(Event event);
    int logicalDeleteEvent(@Param("eventId") Long eventId, @Param("userEmail") String userEmail);
    int countEvents(
        @Param("projectId") Long projectId,
        @Param("status") String status,
        @Param("searchColumn") String searchColumn,
        @Param("searchKeyword") String searchKeyword
    );

    int updateEvent(EventUpdateRequestDto requestDto);

    int deleteEvent(Long eventId);

    // ********** 전체 이벤트 수 조회 추가 **********
    long selectTotalEventCount();

    // 기간별 일일 이벤트 생성 수 조회
    List<DailyCountDto> selectDailyEventCreationCounts(@Param("startDate") String startDate, @Param("endDate") String endDate);

    // 기간별 일일 이벤트 생성 수 (프로젝트별) 조회
    List<DailyCountDto> selectDailyEventCreationCountsByProject(@Param("projectId") Long projectId,
                                                               @Param("startDate") String startDate,
                                                               @Param("endDate") String endDate);

    // --- Super Admin용 메서드 --- 

    /**
     * SUPER_ADMIN: 모든 이벤트 목록 조회 (페이징 및 동적 정렬)
     * @return 이벤트 목록
     */
    List<Event> selectAllEventsForSuperAdmin(
        @Param("searchColumn") String searchColumn,
        @Param("searchKeyword") String searchKeyword,
        @Param("pageable") Pageable pageable);

    /**
     * SUPER_ADMIN: 모든 이벤트 개수 조회
     * @return 이벤트 총 개수
     */
    long countAllEventsForSuperAdmin(
        @Param("searchColumn") String searchColumn,
        @Param("searchKeyword") String searchKeyword);

    /**
     * SUPER_ADMIN: 특정 프로젝트의 이벤트 목록 조회 (페이징 및 동적 정렬)
     * @return 이벤트 목록
     */
    List<Event> selectEventsByProjectIdForSuperAdmin(
        @Param("projectId") Long projectId,
        @Param("searchColumn") String searchColumn,
        @Param("searchKeyword") String searchKeyword,
        @Param("pageable") Pageable pageable);

    /**
     * SUPER_ADMIN: 특정 프로젝트의 이벤트 개수 조회
     * @param projectId 프로젝트 ID
     * @return 이벤트 총 개수
     */
    long countEventsByProjectIdForSuperAdmin(
        @Param("projectId") Long projectId,
        @Param("searchColumn") String searchColumn,
        @Param("searchKeyword") String searchKeyword);
    
    /**
     * 이벤트에 사전 등록 양식 ID를 연결
     * @param eventId 이벤트 ID
     * @param preRegistrationFormId 사전 등록 양식 ID
     * @return 업데이트된 행 수
     */
    int updateEventPreRegistrationFormId(
        @Param("eventId") Long eventId, 
        @Param("preRegistrationFormId") Long preRegistrationFormId,
        @Param("userEmail") String userEmail
    );
    
    /**
     * 사전 등록 양식 조회
     * @param formId 사전 등록 양식 ID
     * @return 사전 등록 양식 정보
     */
    Map<String, Object> selectPreRegistrationFormById(@Param("formId") Long formId);
    
    /**
     * 새 사전 등록 양식 저장
     * @param form 사전 등록 양식 정보
     * @return 삽입된 행 수
     */
    int insertPreRegistrationForm(Map<String, Object> form);
    
    /**
     * 사전 등록 양식의 필드 목록 조회
     * @param formId 사전 등록 양식 ID
     * @return 필드 목록
     */
    List<Map<String, Object>> selectFormFieldsByFormId(@Param("formId") Long formId);
    
    /**
     * 이벤트 ID로 랜딩 페이지 조회
     * @param params 파라미터(eventId, status 등)
     * @return 랜딩 페이지 정보
     */
    Map<String, Object> selectLandingPageByEventId(@Param("params") Map<String, Object> params);
    
    /**
     * 랜딩 페이지 삽입
     * @param landingPage 랜딩 페이지 정보
     * @return 삽입된 행 수
     */
    int insertLandingPage(Map<String, Object> landingPage);
    
    /**
     * 랜딩 페이지 ID로 버전 조회
     * @param landingPageId 랜딩 페이지 ID
     * @return 랜딩 페이지 버전 목록
     */
    List<Map<String, Object>> selectLandingPageVersionsByLandingPageId(@Param("landingPageId") Long landingPageId);
    
    /**
     * 랜딩 페이지 버전 삽입
     * @param version 랜딩 페이지 버전 정보
     * @return 삽입된 행 수
     */
    int insertLandingPageVersion(Map<String, Object> version);
    
    /**
     * 폼 필드 삽입 (DDL 구조에 맞게 수정)
     * @param field 필드 정보
     * @return 삽입된 행 수
     */
    int insertFormField(Map<String, Object> field);
    
    /**
     * 프로젝트 ID로 랜딩 페이지 조회 (DDL 구조에 맞게 추가)
     * @param params 조회 파라미터
     * @return 랜딩 페이지 정보
     */
    Map<String, Object> selectLandingPageByProjectId(@Param("params") Map<String, Object> params);
}
