-- =====================================================
-- 메뉴 기반 API 접근 제한 시스템 DDL
-- 생성일: 2025-07-02
-- 설명: 사용자별 메뉴별 CRUD 권한을 세밀하게 제어하는 시스템
-- =====================================================

-- =====================================================
-- 1. 기존 테이블 수정 (CRUD 권한 컬럼 추가)
-- =====================================================

-- 메뉴별 역할 접근 권한 테이블에 CRUD 권한 컬럼 추가
ALTER TABLE manage_menu_role_permissions 
ADD COLUMN can_read ENUM('Y', 'N') DEFAULT 'Y' COMMENT '읽기 권한',
ADD COLUMN can_write ENUM('Y', 'N') DEFAULT 'N' COMMENT '쓰기(생성) 권한',
ADD COLUMN can_update ENUM('Y', 'N') DEFAULT 'N' COMMENT '수정 권한',
ADD COLUMN can_delete ENUM('Y', 'N') DEFAULT 'N' COMMENT '삭제 권한';

-- 개별 사용자별 메뉴 접근 권한 테이블에 CRUD 권한 컬럼 추가
ALTER TABLE manage_menu_user_permissions 
ADD COLUMN can_read ENUM('Y', 'N') DEFAULT 'Y' COMMENT '읽기 권한',
ADD COLUMN can_write ENUM('Y', 'N') DEFAULT 'N' COMMENT '쓰기(생성) 권한',
ADD COLUMN can_update ENUM('Y', 'N') DEFAULT 'N' COMMENT '수정 권한',
ADD COLUMN can_delete ENUM('Y', 'N') DEFAULT 'N' COMMENT '삭제 권한';

-- =====================================================
-- 2. 새로운 테이블 생성
-- =====================================================

-- 전역 기본 권한 설정 테이블
CREATE TABLE global_default_permissions (
    setting_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '설정 고유 번호 (PK)',
    can_read ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '기본 읽기 권한',
    can_write ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '기본 쓰기(생성) 권한',
    can_update ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '기본 수정 권한',
    can_delete ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '기본 삭제 권한',
    description VARCHAR(500) DEFAULT '전역 기본 권한 설정' COMMENT '설정 설명',
    is_active ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '활성화 여부',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '전역 기본 권한 설정 (모든 사용자의 기본 CRUD 권한)';

-- 사용자별 전역 권한 오버라이드 테이블
CREATE TABLE user_global_permissions (
    user_permission_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '사용자 전역 권한 고유 번호 (PK)',
    user_email VARCHAR(255) NOT NULL COMMENT '사용자 이메일 (FK, users.user_email 참조)',
    can_read ENUM('Y', 'N') DEFAULT NULL COMMENT '읽기 권한 (NULL이면 전역 기본값 사용)',
    can_write ENUM('Y', 'N') DEFAULT NULL COMMENT '쓰기(생성) 권한 (NULL이면 전역 기본값 사용)',
    can_update ENUM('Y', 'N') DEFAULT NULL COMMENT '수정 권한 (NULL이면 전역 기본값 사용)',
    can_delete ENUM('Y', 'N') DEFAULT NULL COMMENT '삭제 권한 (NULL이면 전역 기본값 사용)',
    permission_note TEXT COMMENT '권한 부여 사유 또는 메모',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date DATETIME COMMENT '삭제 일시',
    use_yn ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    UNIQUE KEY uk_user_global_permission (user_email, delete_yn),
    FOREIGN KEY (user_email) REFERENCES users(user_email) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '사용자별 전역 권한 오버라이드 (특정 사용자의 모든 메뉴에 대한 기본 권한 설정)';

-- 메뉴-API 매핑 테이블
CREATE TABLE menu_api_mappings (
    mapping_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT '매핑 고유 번호 (PK)',
    menu_id BIGINT UNSIGNED NOT NULL COMMENT '메뉴 고유 번호 (FK)',
    api_pattern VARCHAR(500) NOT NULL COMMENT 'API 패턴 (예: /api/way/qr-codes/**)',
    http_method ENUM('GET', 'POST', 'PUT', 'DELETE', 'PATCH', '*') NOT NULL COMMENT 'HTTP 메서드 (* = 모든 메서드)',
    required_permission ENUM('read', 'write', 'update', 'delete') NOT NULL COMMENT '필요한 권한 유형',
    priority INT UNSIGNED DEFAULT 0 COMMENT '매칭 우선순위 (높을수록 우선)',
    is_active ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '활성화 여부',
    description VARCHAR(1000) COMMENT '매핑 설명',
    create_user_email VARCHAR(255) COMMENT '생성자 user_email (FK)',
    create_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '생성 시기',
    update_user_email VARCHAR(255) COMMENT '수정자 user_email (FK)',
    last_update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '수정 시기',
    delete_user_email VARCHAR(255) COMMENT '삭제자 user_email (FK)',
    delete_date DATETIME COMMENT '삭제 일시',
    use_yn ENUM('Y', 'N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
    delete_yn ENUM('Y', 'N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
    INDEX idx_api_pattern (api_pattern),
    INDEX idx_menu_method (menu_id, http_method),
    INDEX idx_active_priority (is_active, priority DESC),
    FOREIGN KEY (menu_id) REFERENCES manage_menus(menu_id) ON DELETE CASCADE ON UPDATE CASCADE,
    FOREIGN KEY (create_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (update_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE,
    FOREIGN KEY (delete_user_email) REFERENCES users(user_email) ON DELETE SET NULL ON UPDATE CASCADE
) COMMENT '메뉴-API 매핑 (어떤 API가 어떤 메뉴에 속하고 어떤 권한이 필요한지 정의)';

-- =====================================================
-- 3. 초기 데이터 삽입
-- =====================================================

-- 전역 기본 권한 설정 (모든 사용자가 모든 메뉴에 CRUD 전부 가능)
INSERT INTO global_default_permissions (can_read, can_write, can_update, can_delete, description, create_user_email)
VALUES ('Y', 'Y', 'Y', 'Y', '시스템 기본 권한: 모든 사용자가 모든 메뉴에 CRUD 전부 가능', '<EMAIL>');

-- =====================================================
-- 4. 메뉴-API 매핑 초기 데이터 (주요 메뉴들)
-- =====================================================

-- QR 관리 메뉴 API 매핑
INSERT INTO menu_api_mappings (menu_id, api_pattern, http_method, required_permission, priority, description, create_user_email) VALUES
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/qr-codes/**', 'GET', 'read', 10, 'QR 코드 조회 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/qr-codes', 'POST', 'write', 10, 'QR 코드 생성 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/qr-codes/**', 'PUT', 'update', 10, 'QR 코드 수정 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/qr-codes/**', 'DELETE', 'delete', 10, 'QR 코드 삭제 API', '<EMAIL>');

-- 프로젝트 관리 메뉴 API 매핑
INSERT INTO menu_api_mappings (menu_id, api_pattern, http_method, required_permission, priority, description, create_user_email) VALUES
((SELECT menu_id FROM manage_menus WHERE menu_code = 'PROJECT_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/projects/**', 'GET', 'read', 10, '프로젝트 조회 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'PROJECT_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/projects', 'POST', 'write', 10, '프로젝트 생성 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'PROJECT_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/projects/**', 'PUT', 'update', 10, '프로젝트 수정 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'PROJECT_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/projects/**', 'DELETE', 'delete', 10, '프로젝트 삭제 API', '<EMAIL>');

-- 사용자 관리 메뉴 API 매핑
INSERT INTO menu_api_mappings (menu_id, api_pattern, http_method, required_permission, priority, description, create_user_email) VALUES
((SELECT menu_id FROM manage_menus WHERE menu_code = 'USER_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/users/**', 'GET', 'read', 10, '사용자 조회 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'USER_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/users', 'POST', 'write', 10, '사용자 생성 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'USER_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/users/**', 'PUT', 'update', 10, '사용자 수정 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'USER_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/users/**', 'DELETE', 'delete', 10, '사용자 삭제 API', '<EMAIL>');

-- 이벤트 관리 메뉴 API 매핑
INSERT INTO menu_api_mappings (menu_id, api_pattern, http_method, required_permission, priority, description, create_user_email) VALUES
((SELECT menu_id FROM manage_menus WHERE menu_code = 'EVENT_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/events/**', 'GET', 'read', 10, '이벤트 조회 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'EVENT_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/events', 'POST', 'write', 10, '이벤트 생성 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'EVENT_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/events/**', 'PUT', 'update', 10, '이벤트 수정 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'EVENT_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/events/**', 'DELETE', 'delete', 10, '이벤트 삭제 API', '<EMAIL>');

-- 랜딩페이지 관리 메뉴 API 매핑
INSERT INTO menu_api_mappings (menu_id, api_pattern, http_method, required_permission, priority, description, create_user_email) VALUES
((SELECT menu_id FROM manage_menus WHERE menu_code = 'LANDING_PAGE_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/landing-pages/**', 'GET', 'read', 10, '랜딩페이지 조회 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'LANDING_PAGE_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/landing-pages', 'POST', 'write', 10, '랜딩페이지 생성 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'LANDING_PAGE_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/landing-pages/**', 'PUT', 'update', 10, '랜딩페이지 수정 API', '<EMAIL>'),
((SELECT menu_id FROM manage_menus WHERE menu_code = 'LANDING_PAGE_MANAGEMENT' AND delete_yn = 'N' LIMIT 1), '/api/way/landing-pages/**', 'DELETE', 'delete', 10, '랜딩페이지 삭제 API', '<EMAIL>');

-- =====================================================
-- 5. 권한 확인을 위한 뷰 생성 (선택사항)
-- =====================================================

-- 사용자별 메뉴별 최종 권한을 조회하는 뷰
CREATE VIEW v_user_menu_final_permissions AS
SELECT
    u.user_email,
    u.name as user_name,
    m.menu_id,
    m.menu_code,
    m.menu_name,
    -- 읽기 권한 결정 (개별 > 역할 > 전역 > 기본)
    COALESCE(
        ump.can_read,
        rmp.can_read,
        ugp.can_read,
        gdp.can_read,
        'N'
    ) as final_can_read,
    -- 쓰기 권한 결정
    COALESCE(
        ump.can_write,
        rmp.can_write,
        ugp.can_write,
        gdp.can_write,
        'N'
    ) as final_can_write,
    -- 수정 권한 결정
    COALESCE(
        ump.can_update,
        rmp.can_update,
        ugp.can_update,
        gdp.can_update,
        'N'
    ) as final_can_update,
    -- 삭제 권한 결정
    COALESCE(
        ump.can_delete,
        rmp.can_delete,
        ugp.can_delete,
        gdp.can_delete,
        'N'
    ) as final_can_delete
FROM users u
CROSS JOIN manage_menus m
LEFT JOIN manage_menu_user_permissions ump ON u.user_email = ump.user_email AND m.menu_id = ump.menu_id AND ump.delete_yn = 'N' AND ump.use_yn = 'Y'
LEFT JOIN manage_menu_role_permissions rmp ON u.role_id = rmp.role_id AND m.menu_id = rmp.menu_id AND rmp.delete_yn = 'N' AND rmp.use_yn = 'Y'
LEFT JOIN user_global_permissions ugp ON u.user_email = ugp.user_email AND ugp.delete_yn = 'N' AND ugp.use_yn = 'Y'
CROSS JOIN global_default_permissions gdp
WHERE u.delete_yn = 'N'
  AND m.delete_yn = 'N'
  AND m.status = 'ACTIVE'
  AND gdp.is_active = 'Y';

-- =====================================================
-- 6. 사용 예시 (주석)
-- =====================================================

/*
-- 예시 1: A사용자가 모든 메뉴에 읽기만 가능하도록 설정
INSERT INTO user_global_permissions (user_email, can_read, can_write, can_update, can_delete, permission_note, create_user_email)
VALUES ('<EMAIL>', 'Y', 'N', 'N', 'N', '읽기 전용 사용자', '<EMAIL>');

-- 예시 2: B사용자가 QR관리 메뉴만 RUD 가능, 나머지는 R만 가능하도록 설정
-- 2-1. 전역적으로 읽기만 가능하게 설정
INSERT INTO user_global_permissions (user_email, can_read, can_write, can_update, can_delete, permission_note, create_user_email)
VALUES ('<EMAIL>', 'Y', 'N', 'N', 'N', '기본 읽기 전용, QR관리만 예외', '<EMAIL>');

-- 2-2. QR관리 메뉴만 예외적으로 RUD 가능하게 설정
INSERT INTO manage_menu_user_permissions (menu_id, user_email, is_accessible, can_read, can_write, can_update, can_delete, permission_note, create_user_email)
VALUES (
    (SELECT menu_id FROM manage_menus WHERE menu_code = 'QR_MANAGEMENT' AND delete_yn = 'N' LIMIT 1),
    '<EMAIL>', 'Y', 'Y', 'N', 'Y', 'Y', 'QR관리 특별 권한', '<EMAIL>'
);

-- 예시 3: 특정 사용자의 최종 권한 확인
SELECT * FROM v_user_menu_final_permissions
WHERE user_email = '<EMAIL>'
ORDER BY menu_code;
*/
