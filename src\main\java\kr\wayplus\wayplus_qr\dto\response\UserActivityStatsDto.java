package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 사용자 활동 통계 DTO
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserActivityStatsDto {
    
    // 기본 사용자 통계
    private BasicStats basic;
    
    // 사용자별 QR 코드 생성 통계
    private QrCreationStats qrCreation;
    
    // 사용자별 이벤트 생성 통계
    private EventCreationStats eventCreation;
    
    // 사용자별 랜딩 페이지 생성 통계
    private LandingPageStats landingPage;
    
    // 사용자별 교환권 승인 통계
    private ExchangeApprovalStats exchangeApproval;
    
    // 시간별 사용자 활동 통계
    private TimeBasedStats timeStats;
    
    // 관리자별 가장 많이 호출한 URI 통계
    private Map<String, AdminStat> adminStat;
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BasicStats {
        private Long totalUsers;                // 총 사용자 수
        private Long activeUsers;               // 활성 사용자 수
        private Long inactiveUsers;             // 비활성 사용자 수
        private Map<String, Long> usersByRole;  // 역할별 사용자 수
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class QrCreationStats {
        private Map<String, UserQrStats> topQrCreators;  // 상위 QR 코드 생성자 통계
        private Map<String, Long> qrTypeDistribution;    // QR 코드 타입별 생성 분포
        private Double avgQrCodesPerUser;                // 사용자당 평균 QR 코드 수
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserQrStats {
        private Long totalQrCodes;                      // 총 QR 코드 수
        private Long activeQrCodes;                     // 활성 QR 코드 수
        private Long totalScans;                        // 총 스캔 수
        private Map<String, Long> qrTypeDistribution;   // QR 코드 타입별 분포
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class EventCreationStats {
        private Map<String, UserEventStats> topEventCreators;  // 상위 이벤트 생성자 통계
        private Double avgEventsPerUser;                       // 사용자당 평균 이벤트 수
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserEventStats {
        private Long totalEvents;                // 총 이벤트 수
        private Long activeEvents;               // 활성 이벤트 수
        private Long totalAttendees;             // 총 참석자 수
        private Double avgAttendeesPerEvent;     // 이벤트당 평균 참석자 수
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LandingPageStats {
        private Map<String, UserLandingPageStats> topLandingPageCreators;  // 상위 랜딩 페이지 생성자 통계
        private Double avgLandingPagesPerUser;                             // 사용자당 평균 랜딩 페이지 수
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UserLandingPageStats {
        private Long totalLandingPages;          // 총 랜딩 페이지 수
        private Long activeLandingPages;         // 활성 랜딩 페이지 수
        private Long totalViews;                 // 총 조회 수
        private Double avgViewsPerLandingPage;   // 랜딩 페이지당 평균 조회 수
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExchangeApprovalStats {
        private Map<String, Long> topApprovers;          // 상위 승인자별 승인 횟수
        private Long totalApprovals;                     // 총 승인 횟수
        private Double avgApprovalsPerApprover;          // 승인자당 평균 승인 횟수
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeBasedStats {
        private List<String> dates;                      // 날짜 배열
        private Map<String, List<Long>> dailyActivities; // 일별 활동 통계 (사용자별)
        private Map<Integer, Long> hourlyActivities;     // 시간대별 활동 통계
        private Map<String, Long> weekdayActivities;     // 요일별 활동 통계
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AdminStat {
        private String requestUri;    // 가장 많이 호출한 URI
        private Long count;           // 호출 횟수
    }
}
