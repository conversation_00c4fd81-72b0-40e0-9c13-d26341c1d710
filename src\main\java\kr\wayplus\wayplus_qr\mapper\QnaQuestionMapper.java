package kr.wayplus.wayplus_qr.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import kr.wayplus.wayplus_qr.dto.response.QnaQuestionResponseDto;

@Mapper
public interface QnaQuestionMapper {
    
    // 질문 등록 
    int insertQuestion(
        @Param("projectId") Long projectId,
        @Param("title") String title, 
        @Param("content") String content, 
        @Param("createUserEmail") String createUserEmail
    );
    
    // 최근 등록된 질문의 ID 조회
    Long selectLastInsertId();
    
    // 특정 ID의 질문 조회
    QnaQuestionResponseDto selectQuestionById(@Param("qnaQuestionId") Long qnaQuestionId);
    
    // 질문 목록 조회 (페이징, 검색 기능 포함)
    List<QnaQuestionResponseDto> selectQuestionsList(
        @Param("answerType") String answerType,
        @Param("searchKeyword") String searchKeyword,
        @Param("searchType") String searchType,
        @Param("offset") int offset,
        @Param("limit") int limit
    );
    
    // 질문 총 개수 조회 (페이징용)
    int selectQuestionsCount(
        @Param("answerType") String answerType,
        @Param("searchKeyword") String searchKeyword,
        @Param("searchType") String searchType
    );
    
    // 질문 수정
    int updateQuestion(
        @Param("qnaQuestionId") Long qnaQuestionId,
        @Param("title") String title,
        @Param("content") String content,
        @Param("updateUserEmail") String updateUserEmail
    );
    
    // 조회수 증가
    int updateViewCount(@Param("qnaQuestionId") Long qnaQuestionId);
    
    // 질문 삭제 (논리적 삭제)
    int deleteQuestion(
        @Param("qnaQuestionId") Long qnaQuestionId,
        @Param("deleteUserEmail") String deleteUserEmail
    );
}
