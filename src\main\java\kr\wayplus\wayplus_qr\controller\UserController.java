package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.dto.request.UserCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.UserUpdateRequestDto;
import kr.wayplus.wayplus_qr.config.SearchTypeRegistry;
import kr.wayplus.wayplus_qr.dto.request.PasswordChangeRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.AvailableProjectAdminDto;
import kr.wayplus.wayplus_qr.dto.response.ErrorDetail;
import kr.wayplus.wayplus_qr.dto.response.ListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.QrCodeResponseDto;
import kr.wayplus.wayplus_qr.dto.response.SearchTypeInfo;
import kr.wayplus.wayplus_qr.dto.response.UserListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.UserResponseDto;
import kr.wayplus.wayplus_qr.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/way")
@RequiredArgsConstructor
public class UserController {

    private final UserService userService;
    private final SearchTypeRegistry searchTypeRegistry;

    /**
     * 모든 사용자 목록을 조회합니다. (SUPER_ADMIN 역할 필요)
     *
     * @param projectId 특정 프로젝트 사용자만 조회할 경우 프로젝트 ID
     * @return 사용자 목록 DTO 리스트를 포함한 ApiResponseDto
     */
    @GetMapping("/users/list")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<ListResponseDto<UserListResponseDto>>> getUserList(
            @RequestParam(name = "projectId", required = false) Long projectId,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @RequestParam(value = "onlyProjectSearch", required = false) Boolean onlyProjectSearch,
            @PageableDefault(size = 10, sort = "createDate,desc") Pageable pageable
    ) {
        if (searchType != null && !searchTypeRegistry.isValidSearchType("users", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }

        log.info("Request received to get user list (Admin access) with pagination: {}, projectId: {}", pageable, projectId);

        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("users");
        Page<UserListResponseDto> userPage = userService.getUserList(projectId, searchType, searchKeyword, onlyProjectSearch, pageable);
        ListResponseDto<UserListResponseDto> responseDto = new ListResponseDto<>(userPage, availableSearchTypes);
 
        
        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    /**
     * 신규 사용자를 생성합니다.
     * SUPER_ADMIN 또는 PROJECT_ADMIN 권한이 필요합니다.
     *
     * @param requestDto 사용자 생성 요청 데이터
     * @return 생성된 사용자 정보
     */
    @PostMapping("/users/create")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<UserResponseDto>> createUser(@Valid @RequestBody UserCreateRequestDto requestDto) {
        log.info("Request received to create a new user: {}", requestDto.getUserEmail());
        UserResponseDto createdUser = userService.createUser(requestDto);
        log.info("Successfully created user: {}", createdUser.getUserEmail());
        // 성공 시 201 Created 상태 코드와 함께 응답 반환
        return ResponseEntity.status(HttpStatus.CREATED).body(ApiResponseDto.success(createdUser));
    }

    /**
     * 사용자 정보를 수정합니다.
     * SUPER_ADMIN 또는 PROJECT_ADMIN 권한이 필요합니다.
     *
     * @param userEmail 수정할 사용자의 이메일
     * @param requestDto 수정할 사용자 정보
     * @return 수정된 사용자 정보
     */
    @PutMapping("/users/modify/{userEmail}")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<UserResponseDto>> modifyUser(
            @PathVariable("userEmail") String userEmail,
            @Valid @RequestBody UserUpdateRequestDto requestDto) {
        log.info("Request received to modify user with email: {}", userEmail);
        UserResponseDto modifiedUser = userService.modifyUser(userEmail, requestDto);
        log.info("Successfully modified user with email: {}", userEmail);
        return ResponseEntity.ok(ApiResponseDto.success(modifiedUser));
    }

    /**
     * 사용자 상세 정보를 조회합니다.
     * 인증된 사용자만 접근 가능합니다.
     *
     * @param userEmail 조회할 사용자의 이메일
     * @return 사용자 상세 정보 (UserResponseDto)
     */
    @GetMapping("/users/{userEmail}") 
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponseDto<UserResponseDto>> getUserDetails(@PathVariable("userEmail") String userEmail) {
        log.info("Request received to get details for user: {}", userEmail);
        // getUserProfileByEmail을 사용하여 프로젝트 ID 포함된 정보 조회
        UserResponseDto userDetails = userService.getUserProfileByEmail(userEmail);
        log.info("Successfully retrieved details for user: {}", userEmail);
        return ResponseEntity.ok(ApiResponseDto.success(userDetails));
    }

    /**
     * 사용자를 논리적으로 삭제합니다.
     * SUPER_ADMIN 또는 PROJECT_ADMIN 권한이 필요합니다.
     *
     * @param userEmail 삭제할 사용자의 이메일
     * @return 성공 여부 응답
     */
    @DeleteMapping("/users/{userEmail}")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> deleteUser(@PathVariable("userEmail") String userEmail) {
        log.info("Request received to delete user with email: {}", userEmail);
        userService.deleteUser(userEmail);
        log.info("Successfully deleted user with email: {}", userEmail);
        // 성공 시 data 필드는 null 또는 비워둠
        return ResponseEntity.ok(ApiResponseDto.success(null));
    }

    /**
     * 현재 로그인된 사용자의 프로필 정보를 조회합니다.
     *
     * @return 사용자 프로필 정보 DTO(UserResponseDto)를 포함한 ApiResponseDto
     */
    @GetMapping("/users/profile")
    @PreAuthorize("isAuthenticated()")
    public ResponseEntity<ApiResponseDto<UserResponseDto>> getUserProfile() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            // 이 경우는 @PreAuthorize에 의해 차단되지만, 방어적으로 코드 작성
            log.warn("Unauthorized access attempt to /users/profile");
            // 401 Unauthorized 반환 (ErrorDetail 포함)
            ErrorDetail errorDetail = ErrorDetail.builder()
                                                .code("UNAUTHENTICATED")
                                                .message("인증되지 않은 사용자입니다.")
                                                .build();
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error(errorDetail));
        }

        String userEmail = authentication.getName(); // 현재 로그인한 사용자의 이메일(ID)
        log.info("Request received to get profile for user: {}", userEmail);

        // UserService를 통해 프로필 정보 조회 (프로젝트 ID 목록 포함)
        UserResponseDto userProfileDto = userService.getUserProfileByEmail(userEmail);

        log.info("Successfully retrieved profile for user: {}", userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(userProfileDto));
    }

    /**
     * 사용자 비밀번호 변경
     *
     * @param userEmail 비밀번호를 변경할 사용자의 이메일
     * @param passwordChangeRequestDto 새 비밀번호 정보 DTO
     * @return 성공 메시지를 포함한 ApiResponseDto
     */
    @PutMapping("/users/{userEmail}/password")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<Void>> changePassword(
            @PathVariable("userEmail") String userEmail,
            @Valid @RequestBody PasswordChangeRequestDto passwordChangeRequestDto) {
        log.info("Request received to change password for userEmail: {}", userEmail);
        userService.changePassword(userEmail, passwordChangeRequestDto);
        // Void 반환 타입에 맞춰 data=null로 성공 응답 생성 (deleteUser 참고)
        return ResponseEntity.ok(ApiResponseDto.success(null));
    }

    /**
     * 할당 가능한 프로젝트 관리자 목록을 조회합니다.
     * 현재 어떤 프로젝트에도 할당되지 않은 PROJECT_ADMIN 역할의 사용자 목록을 반환합니다.
     * SUPER_ADMIN 또는 PROJECT_ADMIN 권한이 필요합니다.
     *
     * @return 할당 가능한 프로젝트 관리자 목록 (UserResponseDto) 리스트를 포함한 ApiResponseDto
     */
    @GetMapping("/users/available-project-admins")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<List<UserResponseDto>>> getAvailableProjectAdmins() {
        log.info("Request received to get available project admins");
        List<UserResponseDto> availableAdmins = userService.getAvailableProjectAdmins();
        return ResponseEntity.ok(ApiResponseDto.success(availableAdmins));
    }

    /**
     * 특정 프로젝트 수정 시 할당 가능한 프로젝트 관리자 목록을 조회합니다.
     * 주어진 projectId의 현재 관리자 또는 다른 프로젝트에 할당되지 않은 PROJECT_ADMIN 역할의 사용자 목록을 반환합니다.
     * 반환 목록에는 각 사용자가 현재 프로젝트의 관리자인지 여부 (isAssignedToCurrentProject)가 포함됩니다.
     * SUPER_ADMIN 또는 PROJECT_ADMIN 권한이 필요합니다.
     *
     * @param projectId 조회할 프로젝트의 ID
     * @return 할당 가능한 프로젝트 관리자 (AvailableProjectAdminDto) 목록을 포함한 ApiResponseDto
     */
    @GetMapping("/users/available-project-admins/{projectId}")
    @PreAuthorize("hasAuthority('PROJECT_ADMIN')")
    public ResponseEntity<ApiResponseDto<List<AvailableProjectAdminDto>>> getAvailableProjectAdminsForProject(@PathVariable("projectId") Long projectId) {
        log.info("Request received to get available project admins for project ID: {}", projectId);
        List<AvailableProjectAdminDto> availableAdmins = userService.getAvailableProjectAdminsForProject(projectId);
        return ResponseEntity.ok(ApiResponseDto.success(availableAdmins));
    }

    /**
     * 사용자 역할을 업데이트합니다.
     * SUPER_ADMIN 권한이 필요합니다.
     *
     * @param userIdx 사용자 ID
     * @param roleUpdateRequestDto 사용자 역할 업데이트 요청 데이터
     * @return 성공 여부 응답
     */
    // @PutMapping("/users/{userIdx}/role")
    // @PreAuthorize("hasAuthority('SUPER_ADMIN')")
    // public ResponseEntity<ApiResponseDto<Void>> updateUserRole(
    //         @PathVariable Long userIdx,
    //         @Valid @RequestBody UserRoleUpdateRequestDto roleUpdateRequestDto) {
    //     log.info("Request received to update role for userIdx: {} to role: {}", userIdx, roleUpdateRequestDto.getRole());
    //     userService.updateUserRole(userIdx, roleUpdateRequestDto);
    //     return ResponseEntity.ok(ApiResponseDto.success(null, "사용자 역할이 성공적으로 업데이트되었습니다."));
    // }
}
