package kr.wayplus.wayplus_qr.util;

import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.entity.WebServiceLog;
import kr.wayplus.wayplus_qr.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.Enumeration;
import java.util.UUID;

@Slf4j
@Component
public class CookieUtil {

    private final UserService userService;

    @Value("${cookie-set.domain}")
    private String COOKIE_DOMAIN;

    @Value("${cookie-set.prefix}")
    private String COOKIE_PREFIX;

    @Value("${cookie-set.tracking-age}")
    private int TRACKER_MAX_AGE;

    @Autowired
    public CookieUtil(UserService userService) {
        this.userService = userService;
    }

    public CookieUtil(UserService userService, String cookieDomain, String cookiePrefix, int trackerMaxAge){
        this.userService = userService;
        this.COOKIE_DOMAIN = cookieDomain;
        this.COOKIE_PREFIX = cookiePrefix;
        this.TRACKER_MAX_AGE = trackerMaxAge;
    }

    public String writeWebTrackingLog(HttpServletRequest request, HttpServletResponse response){
        String message = "";
        try {
            HttpSession session = request.getSession(); 
            User user = null;
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();

            if (authentication != null && authentication.isAuthenticated()) {
                Object principal = authentication.getPrincipal();
                if (principal instanceof User) { 
                    user = (User) principal;
                } else if (principal instanceof String && "anonymousUser".equals(principal.toString())) {
                    log.debug("User is anonymous. No user object to assign for tracking cookie user-specific logic.");
                } else if (principal != null) {
                    log.warn("Authentication principal is not of expected type User or anonymousUser. Principal type: {}", principal.getClass().getName());
                }
            }

            Cookie trackerCookie = getCookieByName(request, "tracker.id");
            if (trackerCookie == null) {
                if (user != null) {
                    if (user.getUserTokenId() == null) {
                        user.setUserTokenId(String.valueOf(UUID.randomUUID()));
                        userService.updateUserNewTokenId(user);
                    }
                    trackerCookie = createCookie("tracker.id", user.getUserTokenId(), TRACKER_MAX_AGE);
                } else {
                    trackerCookie = createCookie("tracker.id", String.valueOf(UUID.randomUUID()), TRACKER_MAX_AGE);
                }
                log.debug("Create Tracker ID... " + trackerCookie.getName() + ":" + trackerCookie.getValue());
            } else {
                log.debug("Exist Tracker ID... " + trackerCookie.getName() + ":" + trackerCookie.getValue());
                if (user != null) {
                    if (user.getUserTokenId() != null && !user.getUserTokenId().equals(trackerCookie.getValue())) {
                        trackerCookie.setValue(user.getUserTokenId());
                        log.debug("User Cookie Not Matched Update Tracker ID... " + trackerCookie.getName() + ":" + trackerCookie.getValue());
                    } else if (user.getUserTokenId() == null){
                        user.setUserTokenId(trackerCookie.getValue());
                        userService.updateUserNewTokenId(user);
                    }
                }
                trackerCookie.setMaxAge(TRACKER_MAX_AGE);
            }
            response.addCookie(trackerCookie);

            if(session.getAttribute("tracker-id") == null){
                log.debug("Create Session Tracker ID... ");
                session.setAttribute("tracker-id", trackerCookie.getValue());
            }
            writeWebServiceLog(request, response, user, trackerCookie);

            message = "Tracking Log Write... Token ID : " + trackerCookie.getValue();
        } catch (Exception e){
            message = e.getMessage();
            log.error(e.getMessage());
        }

        return message;
    }

    /**
     * 설정된 쿠키ID명으로 저장된 쿠키를 찾는다
     * @param request
     * @param cookieId
     * @return savedCookie
     */
    public Cookie getCookieByName(HttpServletRequest request, String cookieId){
        Cookie resultCookie = null;
        Cookie[] cookies = request.getCookies();
        if (cookies != null) { // Null 체크 추가
            for (Cookie cookie : cookies) {
                if (cookie.getName().equals(COOKIE_PREFIX + cookieId)) {
                    resultCookie = cookie;
                    log.debug("Find Cookie... " + cookie.getDomain() + " / " + cookie.getName() + " / " + cookie.getValue() + " / " + cookie.getMaxAge());
                }
            }
        }
        return resultCookie;
    }

    public Cookie createCookie(String cookieId, String value, int maxAge){
        Cookie cookie = new Cookie(COOKIE_PREFIX + cookieId, value);
        cookie.setDomain(COOKIE_DOMAIN);
        cookie.setPath("/");
        cookie.setMaxAge(maxAge);
        return cookie;
    }
    public Cookie createCookieWithOutPrefix(String cookieId, String value, int maxAge){
        Cookie cookie = new Cookie(cookieId, value);
        cookie.setDomain(COOKIE_DOMAIN);
        cookie.setPath("/");
        cookie.setMaxAge(maxAge);
        return cookie;
    }

    /**
     * 로그인 여부 및 Cookie 에 따른 웹 페이지 URL 호출에 대한 로그를 기록한다.
     * @param request
     * @param response
     * @param user
     * @param trackerCookie
     */
    public void writeWebServiceLog(HttpServletRequest request, HttpServletResponse response, User user, Cookie trackerCookie){
        //웹 로그 작성
        WebServiceLog webLog = new WebServiceLog();
        HttpSession session = request.getSession();
        webLog.setSessionId(session.getId());
        if (user != null) {
            webLog.setUserToken(user.getUserTokenId());
            webLog.setUserEmail(user.getUserEmail());
            webLog.setTracking("Y");
        } else {
            webLog.setUserToken(trackerCookie.getValue());
            webLog.setTracking("N");
        }

        if(request.getHeader("referer") != null && request.getHeader("referer").length() > 480) {
            webLog.setReferer(request.getHeader("referer").substring(0, 480));
        } else {
            webLog.setReferer(request.getHeader("referer"));
        }

        if(request.getHeader("user-agent") != null && request.getHeader("user-agent").length() > 480) {
            webLog.setRequestAgent(request.getHeader("user-agent").substring(0, 480));
        } else {
            webLog.setRequestAgent(request.getHeader("user-agent"));
        }

        webLog.setRequestHost(request.getRemoteHost());
        webLog.setRequestUri(request.getRequestURI());
        webLog.setRequestParams(paramsToQueryString(request));
        webLog.setResponseStatus(response.getStatus());
        userService.writeUserWebLog(webLog);
    }

    public String paramsToQueryString(HttpServletRequest request) {
        if (request == null) { return null; }

        Enumeration<String> params = request.getParameterNames();
        if (params == null) { return null; }

        StringBuilder sb = new StringBuilder();
        boolean first = true;

        while (params.hasMoreElements()) {
            String paramName = params.nextElement();
            if (paramName == null) {
                continue;
            }

            String value = request.getParameter(paramName);

            if (value == null || value.trim().isEmpty()) {
                continue;
            }

            if(first) {
                sb.append("?");
                first = false;
            } else {
                sb.append("&");
            }

            sb.append(paramName).append("=").append(value.trim());
        }

        return sb.toString();
    }
}
