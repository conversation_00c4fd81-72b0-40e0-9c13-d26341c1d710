package kr.wayplus.wayplus_qr.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import kr.wayplus.wayplus_qr.entity.EventStatus;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.multipart.MultipartFile;

@JsonIgnoreProperties(ignoreUnknown = true)
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class EventUpdateRequestDto {
    private Long teamId;
    private String eventName;
    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime startDate;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime endDate;

    private String location;
    private Integer participantLimit;
    private Long preRegistrationFormId;
    private Long linkedQrCodeId;
    private EventStatus status;
    private MultipartFile eventImageFile;
    private Boolean removeImage;

    // Add field for benefit information in JSON format
    private String benefitsJson;
}
