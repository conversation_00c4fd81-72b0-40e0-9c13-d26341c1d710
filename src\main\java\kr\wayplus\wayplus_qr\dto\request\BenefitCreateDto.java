package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class BenefitCreateDto {
    @NotBlank
    @Size(max = 50)
    private String benefitCode;

    @NotBlank
    @Size(max = 100)
    private String benefitName;

    @Size(max = 255)
    private String description;

    // NULL == 무제한, 0이상
    private Integer quantity;

    // 업데이트/식별용: 이벤트 혜택 PK (event_benefits.benefit_id)
    private Long eventBenefitId; // alias for benefitId in some payloads

    // 프론트엔드에서 benefitId 필드명으로 전달될 수도 있어 둘 다 허용
    private Long benefitId;
}
