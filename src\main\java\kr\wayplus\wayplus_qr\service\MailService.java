package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.entity.LogMailSend;
import kr.wayplus.wayplus_qr.entity.SettingMailFormat;
import kr.wayplus.wayplus_qr.entity.SettingMailServer;
import kr.wayplus.wayplus_qr.mapper.MessageSenderMapper;
import kr.wayplus.wayplus_qr.util.LoggerUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.Properties;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import jakarta.mail.Authenticator;
import jakarta.mail.PasswordAuthentication;
import jakarta.mail.Session;
import jakarta.mail.Transport;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;
import jakarta.servlet.http.HttpServletRequest;

@Slf4j
@Service
@RequiredArgsConstructor
public class MailService {
    
    @Value("${wayplus.frontUrl}")
    private String frontUrl;

    private final LoggerService loggerService;

    private final MessageSenderMapper messageSenderMapper;

    private final LoggerUtil loggerUtil;

    public void sendMailFromSet(String alarmType, int formatId, String userEmail, String wayplusInitialPassword) throws Exception{
        log.debug("메일 발송 시작");
        SettingMailServer mailServer = messageSenderMapper.selectMailServer(alarmType);
        if(mailServer == null) throw new RuntimeException("SMTP 서버 정보를 찾을 수 없습니다.");

        String subject = "";
        String content = "";

        SettingMailFormat mailFormat = messageSenderMapper.selectMailFormat(formatId);
        if(mailFormat == null) { // Null 체크를 먼저 수행
            throw new RuntimeException("발신 메일 양식을 찾을 수 없습니다. formatId: " + formatId);
        }
        subject = mailFormat.getTitle();
        content = mailFormat.getMailContent();

        if(StringUtils.hasText(userEmail)) {
            Properties properties = new Properties();
            properties.put("mail.smtp.host", mailServer.getSmtpServer());
            properties.put("mail.smtp.port", mailServer.getSmtpPort());
            properties.put("mail.smtp.auth", "Y".equals(mailServer.getSmtpAuthorizeRequired())); // null safe
            properties.put("mail.smtp.starttls.enable", "TLS".equals(mailServer.getSmtpSecureType())); // null safe
            String secureType = mailServer.getSmtpSecureType();
            if("TLS".equals(secureType) || "SSL".equals(secureType)) {
                properties.put("mail.smtp.starttls.trust", mailServer.getSmtpServer());
            }

            Session mailSession = Session.getInstance(properties, new Authenticator() {
                @Override
                protected PasswordAuthentication getPasswordAuthentication() {
                    return new PasswordAuthentication(mailServer.getSmtpLoginId(), mailServer.getSmtpLoginPw());
                }
            });

            MimeMessage message = new MimeMessage(mailSession);
            MimeMessageHelper helper = new MimeMessageHelper(message, true, "UTF-8"); // true는 multipart 메시지를 의미

            helper.setFrom(new InternetAddress(mailServer.getSmtpEmail(), mailServer.getSmtpUsername(), "UTF-8"));
            helper.setTo(userEmail);
            helper.setSubject(subject);
            helper.setReplyTo(new InternetAddress(mailServer.getSmtpEmail()));
            
            // 텍스트 버전 생성 (MimeMessageHelper가 HTML과 함께 처리)
            String plainText = "WayPlus QR 계정 생성 안내\n\n" +
                         "안녕하세요,\n" +
                         content.replaceAll("<[^>]*>", "") + "\n\n" +
                         "아이디: " + userEmail + "\n" +
                         "초기 비밀번호: " + wayplusInitialPassword + "\n\n" +
                         "위의 정보로 로그인 후, 보안을 위해 비밀번호를 변경해주시기 바랍니다.\n" +
                         "로그인: " + frontUrl + "/login\n\n" +
                         "문의사항이 있으시면 언제든지 고객센터로 연락주시기 바랍니다.\n" +
                         "감사합니다.\n\n" +
                         " 2025 WayPlus. All rights reserved.\n" +
                         "본 메일은 발신 전용으로 회신되지 않습니다.";
            
            // HTML 버전 생성 및 로고 이미지 첨부
            String htmlContent = getMailHTML(content, userEmail, wayplusInitialPassword);
            helper.setText(plainText, htmlContent); // 일반 텍스트와 HTML 콘텐츠 설정

            LogMailSend logMailSend = new LogMailSend(mailServer.getSmtpEmail(), userEmail, loggerUtil.getPropertiesAsString(properties), formatId, mailFormat.getTitle(), mailFormat.getMailContent(), "text/html; charset=utf-8", userEmail);
            logMailSend.setId(loggerService.writeMailingLog(logMailSend));
            try {
                Transport.send(message);
                logMailSend.setSendResult("SUCCESS");
                loggerService.updateMailingLogResult(logMailSend);
            }catch (Exception e){
                log.error("Mail Send Error : " + e.getMessage());
                logMailSend.setSendResult("ERROR");
                logMailSend.setSendResultMessage(e.getMessage());
                loggerService.updateMailingLogResult(logMailSend);
            }
            

            log.debug("메일 발송 완료");
        }else{
            log.debug("메일 수신자가 없습니다. (userEmail: {})", userEmail);
        }
        
        log.debug("메일 발송 종료");
    }

    public String getMailHTML(String content, String userEmail, String wayplusInitialPassword){
        String mailHTML = "<!DOCTYPE html>\r\n" + //
        "<html>\r\n" + //
        "<head>\r\n" + //
        "    <meta charset=\"UTF-8\">\r\n" + //
        "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\r\n" + //
        "    <title>WayPlus QR코드 시스템 계정 생성 안내</title>\r\n" + //
        "</head>\r\n" + //
        "<body style=\"margin: 0; padding: 0; color: #333333; background-color: #f7f7f7; line-height: 1.6;\">\r\n" + //
        "    <div style=\"font-family: Pretendard; max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; overflow: hidden; box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);\">\r\n" + //
        "        <!-- 헤더 -->\r\n" + //
        "        <div style=\"padding: 30px 40px; text-align: center; background-color: #ffffff; border-bottom: 1px solid #eeeeee;\">\r\n" + //
        "            <img src=\"https://wayplus.kr/icon/ci_color.svg\" alt=\"WayPlus 로고\" style=\"max-width: 180px; height: auto;\">\r\n" + //
        "        </div>\r\n" + //
        "        \r\n" + //
        "        <!-- 본문 -->\r\n" + //
        "        <div style=\"padding: 40px; background-color: #ffffff;\">\r\n" + //
        "            <h1 style=\"margin: 0 0 20px; color: #0056b3; font-size: 24px; font-weight: 600; text-align: center;\">계정 생성 완료</h1>\r\n" + //
        "            \r\n" + //
        "            <p style=\"margin: 0 0 16px; font-size: 16px;\">안녕하세요, 웨이플러스입니다.</p>\r\n" + //
        "            <p style=\"margin: 0 0 16px; font-size: 16px;\">" + content + "</p>\r\n" + //
        "            \r\n" + //
        "            <div style=\"margin: 30px 0; padding: 20px; background-color: #f8f9fa; border-radius: 6px; border-left: 4px solid #0056b3;\">\r\n" + //
        "                <p style=\"margin: 0 0 8px; font-size: 16px;\"><strong>아이디:</strong> " + userEmail + "</p>\r\n" + //
        "                <p style=\"margin: 0; font-size: 16px;\"><strong>초기 비밀번호:</strong> " + wayplusInitialPassword + "</p>\r\n" + //
        "                <p style=\"margin-top:5px; font-size: 13px;\">* 대소문자를 구분해주세요.</p>\r\n" + //
        "            </div>\r\n" + //
        "            \r\n" + //
        "            <p style=\"margin: 0 0 16px; font-size: 16px;\">위의 정보로 로그인 후, 보안을 위해 비밀번호를 변경해주시기 바랍니다.</p>\r\n" + //
        "            \r\n" + //
        "            <div style=\"margin: 30px 0; text-align: center;\">\r\n" + //
        "                <a href=\"" + frontUrl + "/login\" style=\"display: inline-block; padding: 12px 24px; background-color: #0056b3; color: #ffffff; text-decoration: none; font-weight: 600; border-radius: 4px; font-size: 16px;\">로그인하기</a>\r\n" + //
        "            </div>\r\n" + //
        "            \r\n" + //
        "            <p style=\"margin: 30px 0 16px; font-size: 16px;\">문의사항이 있으시면 언제든지 고객센터로 연락주시기 바랍니다.</p>\r\n" + //
        "            <p style=\"margin: 0; font-size: 16px;\">감사합니다.</p>\r\n" + //
        "        </div>\r\n" + //
        "        \r\n" + //
        "        <!-- 푸터 -->\r\n" + //
        "        <div style=\"padding: 20px 40px; text-align: center; background-color: #f7f7f7; color: #666666; font-size: 14px; border-top: 1px solid #eeeeee;\">\r\n" + //
        "            <p style=\"margin: 0 0 8px;\">&copy; 2025 WayPlus. All rights reserved.</p>\r\n" + //
        "            <p style=\"margin: 0;\">본 메일은 발신 전용으로 회신되지 않습니다.</p>\r\n" + //
        "        </div>\r\n" + //
        "    </div>\r\n" + //
        "</body>\r\n" + //
        "</html>";

        return mailHTML;
    }
        

    
}
