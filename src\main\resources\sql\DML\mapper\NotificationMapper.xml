<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.NotificationMapper">

    <!-- 결과 매핑 -->
    <resultMap id="notificationResultMap" type="kr.wayplus.wayplus_qr.entity.Notification">
        <id property="notificationId" column="notification_id" />
        <result property="userEmail" column="user_email" />
        <result property="inquiryId" column="inquiry_id" />
        <result property="message" column="message" />
        <result property="isRead" column="is_read" />
        <result property="notificationType" column="notification_type" />
        <result property="createdAt" column="created_at" />
    </resultMap>

    <!-- 알림 생성 -->
    <insert id="insertNotification" parameterType="kr.wayplus.wayplus_qr.entity.Notification" useGeneratedKeys="true" keyProperty="notificationId">
        INSERT INTO notifications (
            user_email,
            inquiry_id,
            message,
            is_read,
            notification_type,
            created_at
        ) VALUES (
            #{userEmail},
            #{inquiryId},
            #{message},
            #{isRead},
            #{notificationType},
            NOW()
        )
    </insert>

    <!-- ID로 알림 조회 -->
    <select id="selectNotificationById" parameterType="long" resultMap="notificationResultMap">
        SELECT 
            notification_id,
            user_email,
            inquiry_id,
            message,
            is_read,
            notification_type,
            created_at
        FROM notifications
        WHERE notification_id = #{notificationId}
    </select>

    <!-- 사용자별 알림 목록 조회 (페이징) -->
    <select id="selectNotificationsByUserEmail" resultMap="notificationResultMap">
        SELECT 
            notification_id,
            user_email,
            inquiry_id,
            message,
            is_read,
            notification_type,
            created_at
        FROM notifications
        WHERE user_email = #{userEmail}
        ORDER BY created_at DESC
        LIMIT #{pageable.pageSize} OFFSET #{pageable.offset}
    </select>

    <!-- 사용자별 읽지 않은 알림 개수 조회 -->
    <select id="countUnreadNotificationsByUserEmail" resultType="long">
        SELECT COUNT(*)
        FROM notifications
        WHERE user_email = #{userEmail}
          AND is_read = false
    </select>

    <!-- 알림 읽음 처리 -->
    <update id="markNotificationAsRead">
        UPDATE notifications
        SET is_read = true
        WHERE notification_id = #{notificationId}
    </update>

    <!-- 사용자의 모든 알림 읽음 처리 -->
    <update id="markAllNotificationsAsRead">
        UPDATE notifications
        SET is_read = true
        WHERE user_email = #{userEmail}
          AND is_read = false
    </update>

    <!-- 알림 삭제 -->
    <delete id="deleteNotification">
        DELETE FROM notifications
        WHERE notification_id = #{notificationId}
    </delete>

    <!-- 문의에 속한 모든 알림 삭제 -->
    <delete id="deleteNotificationsByInquiryId">
        DELETE FROM notifications
        WHERE inquiry_id = #{inquiryId}
    </delete>

</mapper>
