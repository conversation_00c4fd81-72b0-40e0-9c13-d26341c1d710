spring:
  profiles:
    project:
      name: wayplus_qr
    group:
        dev:
          - default
          - dev
        server:
          - default
          - server
---
spring:
  config:
    activate:
      on-profile:
        - default
        - dev
        - server
  thymeleaf:
    cache: false
    check-template-location: true
    prefix: classpath:/templates/
    suffix: .html
  devtools:
    livereload:
      enabled: true
    restart:
      enabled: true
  freemarker:
    cache: false
  servlet:
    session:
      timeout: 30m
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
server:
  error:
    include-exception: false
    include-stacktrace: never
    include-message: never
    whitelabel:
      enabled: true
  compression:
    mime-types:
      -text/html
      -text/xml
      -text/plain
      -text/css
      -text/javascript
      -application/javascript
      -application/json
  tomcat:
    uri-encoding: UTF-8
mybatis:
  type-aliases-package: kr.wayplus.wayplus_qr.entity
  configuration:
    default-statement-timeout : 30
    auto-mapping-unknown-column-behavior : warning
    map-underscore-to-camel-case : true
  mapper-locations: classpath:/sql/DML/mapper/**/*.xml
logging:
  # file:
  #   path: "./logs/"
  #   name: "service.log"
  logback:
    rollingpolicy:
      max-history: 10
      max-file-size: 10MB
      file-name-pattern: "service.%d{yyyy-MM-dd}.%i.log"
wayplus:
  initialPassword: wayPassword
  frontUrl: http://*************:9998

qr-quiz-mapping-api:
  base-url: http://*************:8082 # 실제 Thymeleaf 서버 주소로 변경해야 합니다.
  secret-key: WayQRConnect
  header-name: X-API-Key
  endpoints:
    mapping: /api/qr-quiz/mapping
    un-mapping: /api/qr-quiz/un-mapping
  timeout:
    connect: 3000
    read: 5000
---
spring:
  config:
    activate:
      on-profile: "dev"
  datasource:
    hikari:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: ********************************************
      #jdbc-url: ******************************************
      username: wayplus_qr
      password: dnpdlvmffjtmQR00^^
      pool-name: Hikari Connection Pool
      minimum-idle: 2
      maximum-pool-size: 8
  servlet:
    multipart:
      max-file-size: 10MB #파일당
      max-request-size: 10MB #전체 파일
server:
  error:
    include-exception: true
    include-stacktrace: always
    include-message: always
  servlet:
    session:
      timeout: 60m
  address: 0.0.0.0
  port: 8081
  base-url: http://localhost:8081 # QR 리디렉션 URL 구성을 위한 기본 URL
upload:
  file:
    path: "c:/logs/upload/file/2025/way_qr/"
    max-size: 104857600
  qr-file:
    path: "c:/logs/upload/file/2025/way_qr/qr_images/"
    max-size: 104857600
cookie-set:
  domain: "localhost"
  prefix: "kr.co.wayplus."
  tracking: true
  tracking-age: 15638400
jwt:
  secret: '650f35c0475bd41712117f9859ed5283a4d9d696cf2e62dee5c9bc6289669dcf321d78a37ce91bacdd3897f529fa083d9551cf8dfff230d8828c8bb8bbce2a53f85d52d0ec2a7f36e9687352827cf2db0caedce6feba09600771c2d9612bc756403b94ff9badea12b87ccd5f0025e57b43c47379854114a3ecdab57c4cc7cd341f86266e3c68bdf7591892eaeb136477baffade82be793f752159180e7994f8c18e57616e5406b9bca9ed501bb083aba453970538910ee7c070968f2709ab281f12ca2402326f6df1d676205593f2839a70d1cfcb345c4230aa3a1bb52312da760481f2e302576d9bc876817bf38d977643a8f73f1dd69fef93a83d7fe83a517'
  access-token-expiration-ms: 600000 # 10분 (밀리초 단위)
  refresh-token-expiration-ms: 604800000 # 7일 (밀리초 단위)
logging:
  level:
    '[kr.wayplus.wayplus_qr.service]': DEBUG # 대괄호 사용
  file:
    path: "c:/logs/"
    name: "way_qr_dev.log"
  logback:
    rollingpolicy:
      max-history: 10
      max-file-size: 100MB
      file-name-pattern: "way_qr_dev.%d{yyyy-MM-dd}.%i.log"