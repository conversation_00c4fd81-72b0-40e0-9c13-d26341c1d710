package kr.wayplus.wayplus_qr.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.lang.NonNull;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import kr.wayplus.wayplus_qr.config.handler.TokenAuthorizeInterceptor;

@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Value("${upload.qr-file.path}")
    private String qrCodeImagePath;

    @Value("${upload.file.path}")
    private String uploadDir;

    private static final String URL_PREFIX_QR = "/qr-images/";
    private static final String URL_PREFIX_UPLOADS = "/uploads/";

    private final TokenAuthorizeInterceptor tokenAuthorizeInterceptor;

    public WebConfig(TokenAuthorizeInterceptor tokenAuthorizeInterceptor) {
        this.tokenAuthorizeInterceptor = tokenAuthorizeInterceptor;
    }

    @Override
    public void addResourceHandlers(@NonNull ResourceHandlerRegistry registry) {
        // --- QR Code Images --- 
        String qrResourceLocation = "file:" + qrCodeImagePath;
        if (!qrResourceLocation.endsWith("/")) {
            qrResourceLocation += "/";
        }
        registry.addResourceHandler(URL_PREFIX_QR + "**")
                .addResourceLocations(qrResourceLocation);

        // --- Uploaded Images --- 
        String uploadResourceLocation = "file:" + uploadDir;
        if (!uploadResourceLocation.endsWith("/")) {
            uploadResourceLocation += "/";
        }
        registry.addResourceHandler(URL_PREFIX_UPLOADS + "**")
                .addResourceLocations(uploadResourceLocation);
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(tokenAuthorizeInterceptor)
                .addPathPatterns("/**");

        // 메뉴 기반 접근 제어 인터셉터 추가 (필요시 구현)
        // registry.addInterceptor(menuAccessInterceptor)
        //         .addPathPatterns("/api/way/**")
        //         .excludePathPatterns("/api/way/auth/**", "/api/way/public/**");
    }
}
