<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.RefreshTokenMapper">
    <!-- Refresh Token 저장 -->
    <insert id="save" parameterType="kr.wayplus.wayplus_qr.entity.RefreshToken" useGeneratedKeys="true" keyProperty="tokenId">
        INSERT INTO refresh_token (user_email, token, expiry_date)
        VALUES (#{userEmail}, #{token}, #{expiryDate})
    </insert>

    <!-- 사용자 이메일로 Refresh Token 조회 -->
    <select id="selectRefreshTokenByUserEmail" resultType="kr.wayplus.wayplus_qr.entity.RefreshToken">
        SELECT token_id, user_email, token, expiry_date, create_date, last_update_date
        FROM refresh_token
        WHERE user_email = #{userEmail}
    </select>

    <!-- Refresh Token 값으로 토큰 조회 -->
    <select id="selectByToken" resultType="kr.wayplus.wayplus_qr.entity.RefreshToken">
        SELECT token_id, user_email, token, expiry_date, create_date, last_update_date
        FROM refresh_token
        WHERE token = #{token}
    </select>

    <!-- Refresh Token 업데이트 (tokenId 기준) -->
    <update id="update" parameterType="kr.wayplus.wayplus_qr.entity.RefreshToken">
        UPDATE refresh_token
        SET
            token = #{token},
            expiry_date = #{expiryDate},
            last_update_date = CURRENT_TIMESTAMP
        WHERE
            token_id = #{tokenId}
    </update>

    <!-- 사용자 이메일로 Refresh Token 삭제 -->
    <delete id="deleteByUserEmail">
        DELETE FROM refresh_token
        WHERE user_email = #{userEmail}
    </delete>

    <!-- Refresh Token 값으로 토큰 삭제 -->
    <delete id="deleteByToken">
        DELETE FROM refresh_token
        WHERE token = #{token}
    </delete>

</mapper>
