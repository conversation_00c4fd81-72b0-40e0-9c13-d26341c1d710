<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.InquiryAttachmentMapper">

    <!-- 결과 매핑 -->
    <resultMap id="attachmentResultMap" type="kr.wayplus.wayplus_qr.entity.InquiryAttachment">
        <id property="attachmentId" column="attachment_id" />
        <result property="inquiryId" column="inquiry_id" />
        <result property="commentId" column="comment_id" />
        <result property="originalFileName" column="original_file_name" />
        <result property="storedFilePath" column="stored_file_path" />
        <result property="fileSize" column="file_size" />
        <result property="mimeType" column="mime_type" />
        <result property="createdAt" column="created_at" />
    </resultMap>

    <!-- 첨부파일 생성 -->
    <insert id="insertAttachment" parameterType="kr.wayplus.wayplus_qr.entity.InquiryAttachment" useGeneratedKeys="true" keyProperty="attachmentId">
        INSERT INTO inquiry_attachments (
            inquiry_id,
            comment_id,
            original_file_name,
            stored_file_path,
            file_size,
            mime_type,
            created_at
        ) VALUES (
            #{inquiryId},
            #{commentId},
            #{originalFileName},
            #{storedFilePath},
            #{fileSize},
            #{mimeType},
            NOW()
        )
    </insert>

    <!-- ID로 첨부파일 조회 -->
    <select id="selectAttachmentById" parameterType="long" resultMap="attachmentResultMap">
        SELECT 
            attachment_id,
            inquiry_id,
            comment_id,
            original_file_name,
            stored_file_path,
            file_size,
            mime_type,
            created_at
        FROM inquiry_attachments
        WHERE attachment_id = #{attachmentId}
    </select>

    <!-- 문의 ID로 첨부파일 목록 조회 -->
    <select id="selectAttachmentsByInquiryId" parameterType="long" resultMap="attachmentResultMap">
        SELECT 
            attachment_id,
            inquiry_id,
            comment_id,
            original_file_name,
            stored_file_path,
            file_size,
            mime_type,
            created_at
        FROM inquiry_attachments
        WHERE inquiry_id = #{inquiryId}
    </select>

    <!-- 댓글 ID로 첨부파일 목록 조회 -->
    <select id="selectAttachmentsByCommentId" parameterType="long" resultMap="attachmentResultMap">
        SELECT 
            attachment_id,
            inquiry_id,
            comment_id,
            original_file_name,
            stored_file_path,
            file_size,
            mime_type,
            created_at
        FROM inquiry_attachments
        WHERE comment_id = #{commentId}
    </select>

    <!-- 문의 ID 또는 댓글 ID로 첨부파일 목록 조회 -->
    <select id="selectAttachmentsByInquiryIdOrCommentId" resultMap="attachmentResultMap">
        SELECT 
            attachment_id,
            inquiry_id,
            comment_id,
            original_file_name,
            stored_file_path,
            file_size,
            mime_type,
            created_at
        FROM inquiry_attachments
        <where>
            <if test="inquiryId != null">
                inquiry_id = #{inquiryId}
            </if>
            <if test="commentId != null">
                <if test="inquiryId != null">OR</if>
                comment_id = #{commentId}
            </if>
        </where>
    </select>

    <!-- 첨부파일 삭제 -->
    <delete id="deleteAttachment">
        DELETE FROM inquiry_attachments
        WHERE attachment_id = #{attachmentId}
    </delete>

    <!-- 문의에 속한 모든 첨부파일 삭제 -->
    <delete id="deleteAttachmentsByInquiryId">
        DELETE FROM inquiry_attachments
        WHERE inquiry_id = #{inquiryId}
    </delete>

    <!-- 댓글에 속한 모든 첨부파일 삭제 -->
    <delete id="deleteAttachmentsByCommentId">
        DELETE FROM inquiry_attachments
        WHERE comment_id = #{commentId}
    </delete>
    
    <!-- 첨부파일 ID와 댓글 ID로 첨부파일 조회 (안전한 삭제를 위한 권한 검증용) -->
    <select id="selectAttachmentByIdAndCommentId" resultMap="attachmentResultMap">
        SELECT 
            attachment_id,
            inquiry_id,
            comment_id,
            original_file_name,
            stored_file_path,
            file_size,
            mime_type,
            created_at
        FROM inquiry_attachments
        WHERE attachment_id = #{attachmentId}
          AND comment_id = #{commentId}
    </select>

</mapper>
