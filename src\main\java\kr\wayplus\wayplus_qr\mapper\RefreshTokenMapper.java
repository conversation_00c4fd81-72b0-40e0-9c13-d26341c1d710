package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.entity.RefreshToken;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Optional;

@Mapper
public interface RefreshTokenMapper {

    /**
     * Refresh Token 정보를 저장합니다.
     *
     * @param refreshToken 저장할 RefreshToken 객체
     * @return 저장된 행 수 (보통 1)
     */
    int save(RefreshToken refreshToken);

    /**
     * 사용자 이메일로 Refresh Token 정보를 조회합니다.
     * (로그인 시 기존 토큰 확인 또는 갱신 시 사용)
     *
     * @param userEmail 사용자 이메일
     * @return Optional<RefreshToken>
     */
    Optional<RefreshToken> selectRefreshTokenByUserEmail(@Param("userEmail") String userEmail);

    /**
     * Refresh Token 값으로 토큰 정보를 조회합니다.
     * (토큰 갱신 시 유효성 검증용)
     *
     * @param token Refresh Token 문자열
     * @return Optional<RefreshToken>
     */
    Optional<RefreshToken> selectByToken(@Param("token") String token);

    /**
     * Refresh Token 정보를 업데이트합니다. (주로 토큰 값과 만료일 갱신)
     *
     * @param refreshToken 업데이트할 정보가 담긴 RefreshToken 객체 (userEmail 또는 tokenId 기준)
     * @return 업데이트된 행 수 (보통 1)
     */
    int update(RefreshToken refreshToken);

    /**
     * 사용자 이메일 기준으로 Refresh Token을 삭제합니다.
     * (로그아웃 또는 재로그인 시 기존 토큰 삭제)
     *
     * @param userEmail 사용자 이메일
     * @return 삭제된 행 수
     */
    int deleteByUserEmail(@Param("userEmail") String userEmail);

    /**
     * Refresh Token 값으로 토큰을 삭제합니다.
     * (토큰 갱신 실패 또는 명시적 삭제 시)
     *
     * @param token Refresh Token 문자열
     * @return 삭제된 행 수
     */
    int deleteByToken(@Param("token") String token);
}
