package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.response.NotificationCountResponseDto;
import kr.wayplus.wayplus_qr.dto.response.NotificationResponseDto;
import kr.wayplus.wayplus_qr.entity.Notification;
import kr.wayplus.wayplus_qr.entity.NotificationType;
import kr.wayplus.wayplus_qr.entity.enums.UserRole;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import kr.wayplus.wayplus_qr.mapper.NotificationMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class NotificationService {

    private final NotificationMapper notificationMapper;
    private final UserMapper userMapper;

    /**
     * 알림 생성
     * 
     * @param userEmail         수신자 이메일
     * @param inquiryId         관련 문의 ID
     * @param message           알림 메시지
     * @param notificationType  알림 유형
     * @return                  생성된 알림 ID
     */
    @Transactional
    public Long createNotification(String userEmail, Long inquiryId, String message, NotificationType notificationType) {
        Notification notification = Notification.builder()
                .userEmail(userEmail)
                .inquiryId(inquiryId)
                .message(message)
                .isRead(false)
                .notificationType(notificationType)
                .build();

        int insertedCount = notificationMapper.insertNotification(notification);
        if (insertedCount == 0 || notification.getNotificationId() == null) {
            log.error("Failed to insert notification data or retrieve generated ID");
            throw new QRcodeException(ErrorCode.DATABASE_ERROR, "알림 데이터 저장 또는 ID 생성에 실패했습니다.");
        }

        return notification.getNotificationId();
    }

    /**
     * 모든 SUPER_ADMIN에게 알림 생성
     * 
     * @param inquiryId         관련 문의 ID
     * @param message           알림 메시지
     * @param notificationType  알림 유형
     */
    @Transactional
    public void createNotificationForSuperAdmins(Long inquiryId, String message, NotificationType notificationType) {
        // 모든 SUPER_ADMIN 사용자 이메일 가져오기
        List<String> superAdminEmails = userMapper.selectUserListByRole(UserRole.SUPER_ADMIN.name())
                .stream()
                .map(user -> user.getUserEmail())
                .collect(Collectors.toList());

        // 각 SUPER_ADMIN에게 알림 생성
        for (String superAdminEmail : superAdminEmails) {
            createNotification(superAdminEmail, inquiryId, message, notificationType);
        }
    }

    /**
     * 사용자별 알림 목록 조회
     * 
     * @param userEmail  사용자 이메일
     * @param pageable   페이징 정보
     * @return           알림 목록 (페이징)
     */
    @Transactional(readOnly = true)
    public Page<NotificationResponseDto> getNotifications(String userEmail, Pageable pageable) {
        List<Notification> notifications = notificationMapper.selectNotificationsByUserEmail(userEmail, pageable);
        long total = notificationMapper.countUnreadNotificationsByUserEmail(userEmail);

        List<NotificationResponseDto> dtos = notifications.stream()
                .map(NotificationResponseDto::fromEntity)
                .collect(Collectors.toList());

        return new PageImpl<>(dtos, pageable, total);
    }

    /**
     * 사용자별 읽지 않은 알림 개수 조회
     * 
     * @param userEmail  사용자 이메일
     * @return           읽지 않은 알림 개수 DTO
     */
    @Transactional(readOnly = true)
    public NotificationCountResponseDto getUnreadNotificationCount(String userEmail) {
        long count = notificationMapper.countUnreadNotificationsByUserEmail(userEmail);
        return new NotificationCountResponseDto(count);
    }

    /**
     * 알림 읽음 처리
     * 
     * @param notificationId  알림 ID
     * @param userEmail       사용자 이메일
     */
    @Transactional
    public void markNotificationAsRead(Long notificationId, String userEmail) {
        // 알림 조회
        Notification notification = notificationMapper.selectNotificationById(notificationId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.NOTIFICATION_NOT_FOUND, "알림을 찾을 수 없습니다."));

        // 알림 소유자 확인
        if (!notification.getUserEmail().equals(userEmail)) {
            throw new QRcodeException(ErrorCode.ACCESS_DENIED, "알림을 읽을 권한이 없습니다.");
        }

        // 읽음 처리
        notificationMapper.markNotificationAsRead(notificationId);
    }

    /**
     * 사용자의 모든 알림 읽음 처리
     * 
     * @param userEmail  사용자 이메일
     * @return           업데이트된 알림 수
     */
    @Transactional
    public int markAllNotificationsAsRead(String userEmail) {
        return notificationMapper.markAllNotificationsAsRead(userEmail);
    }

    /**
     * 문의에 속한 모든 알림 삭제
     * 
     * @param inquiryId  문의 ID
     */
    @Transactional
    public void deleteNotificationsByInquiryId(Long inquiryId) {
        notificationMapper.deleteNotificationsByInquiryId(inquiryId);
    }
}
