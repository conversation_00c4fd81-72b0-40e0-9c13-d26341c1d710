// package kr.wayplus.wayplus_qr.controller;

// import com.fasterxml.jackson.databind.ObjectMapper;
// import kr.wayplus.wayplus_qr.dto.request.LoginRequestDto;
// import kr.wayplus.wayplus_qr.dto.request.RefreshTokenRequestDto;
// import kr.wayplus.wayplus_qr.dto.response.TokenResponseDto;
// import kr.wayplus.wayplus_qr.service.AuthService;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.DisplayName;
// import org.junit.jupiter.api.Test;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
// import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
// import org.springframework.boot.test.mock.mockito.MockBean;
// import org.springframework.http.MediaType;
// import org.springframework.security.authentication.BadCredentialsException;
// import org.springframework.security.test.context.support.WithMockUser;
// import org.springframework.test.web.servlet.MockMvc;
// import org.springframework.test.web.servlet.ResultActions;

// import static org.mockito.ArgumentMatchers.any;
// import static org.mockito.ArgumentMatchers.anyString;
// import static org.mockito.BDDMockito.given;
// import static org.mockito.Mockito.verify;
// import static org.mockito.Mockito.doNothing;
// import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
// import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

// import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
// import org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration;
// import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;

// @AutoConfigureMockMvc // MockMvc 자동 구성
// @WebMvcTest(controllers = AuthController.class,
//         excludeAutoConfiguration = {SecurityAutoConfiguration.class, UserDetailsServiceAutoConfiguration.class})
// class AuthControllerTest {

//     @Autowired
//     private MockMvc mockMvc; // HTTP 요청 시뮬레이션

//     @Autowired
//     private ObjectMapper objectMapper; // JSON 직렬화/역직렬화

//     @MockBean // AuthService를 가짜 객체(Mock)로 대체
//     private AuthService authService;

//     private LoginRequestDto loginRequestDto;
//     private TokenResponseDto tokenResponseDto;
//     private RefreshTokenRequestDto refreshTokenRequestDto;
//     private TokenResponseDto refreshedTokenResponseDto;

//     @BeforeEach // 각 테스트 실행 전에 호출
//     void setUp() {
//         // 테스트에 사용할 DTO 객체 미리 생성 (빌더 패턴 사용)
//         loginRequestDto = LoginRequestDto.builder()
//                 .userEmail("<EMAIL>")
//                 .password("password")
//                 .build();

//         tokenResponseDto = TokenResponseDto.builder()
//                 .accessToken("mockAccessToken")
//                 .refreshToken("mockRefreshToken")
//                 .expiresIn(1800L) // 초 단위
//                 .build();

//         // 토큰 갱신 테스트용 DTO 추가
//         refreshTokenRequestDto = RefreshTokenRequestDto.builder()
//                 .refreshToken("mockRefreshToken")
//                 .build();

//         refreshedTokenResponseDto = TokenResponseDto.builder()
//                 .accessToken("newMockAccessToken") // 새로운 Access Token
//                 .refreshToken("mockRefreshToken")   // Refresh Token은 그대로일 수 있음 (정책에 따라 다름)
//                 .expiresIn(1800L)
//                 .build();
//     }

//     @Test
//     @DisplayName("로그인 성공")
//     void login_success() throws Exception {
//         // given: AuthService.login 메서드가 호출되면 미리 준비된 tokenResponseDto를 반환하도록 설정
//         given(authService.login(any(LoginRequestDto.class))).willReturn(tokenResponseDto);

//         // when: /api/way/auth/login 엔드포인트로 POST 요청 실행
//         ResultActions resultActions = mockMvc.perform(post("/api/way/auth/login")
//                 .contentType(MediaType.APPLICATION_JSON)
//                 .content(objectMapper.writeValueAsString(loginRequestDto)) // 요청 본문에 DTO를 JSON으로 변환하여 넣음
//                 .with(csrf())); // CSRF 토큰 추가

//         // then: 응답 검증
//         resultActions
//                 .andExpect(status().isOk()) // 상태 코드 200 OK 확인
//                 .andExpect(content().contentType(MediaType.APPLICATION_JSON)) // 응답 컨텐츠 타입 확인
//                 .andExpect(jsonPath("$.success").value(true)) // 표준 응답 구조: success 필드 확인
//                 .andExpect(jsonPath("$.data.accessToken").value(tokenResponseDto.getAccessToken())) // data 내 필드 확인
//                 .andExpect(jsonPath("$.data.refreshToken").value(tokenResponseDto.getRefreshToken()))
//                 .andExpect(jsonPath("$.data.expiresIn").value(tokenResponseDto.getExpiresIn()))
//                 .andExpect(jsonPath("$.message").isEmpty()); // 표준 응답 구조: message 필드는 null 또는 비어있음
//     }

//     @Test
//     @DisplayName("로그인 실패 - 잘못된 비밀번호")
//     void login_fail_invalidPassword() throws Exception {
//         // given: AuthService.login 메서드가 호출되면 BadCredentialsException 발생시키도록 설정
//         String expectedErrorMessage = "아이디 또는 비밀번호가 잘못되었습니다.";
//         given(authService.login(any(LoginRequestDto.class)))
//                 .willThrow(new BadCredentialsException(expectedErrorMessage));

//         // when: 로그인 요청 실행
//         ResultActions resultActions = mockMvc.perform(post("/api/way/auth/login")
//                 .contentType(MediaType.APPLICATION_JSON)
//                 .content(objectMapper.writeValueAsString(loginRequestDto))
//                 .with(csrf())); // CSRF 토큰 추가

//         // then: 응답 검증 (401 Unauthorized 및 표준 에러 응답)
//         resultActions
//                 .andExpect(status().isUnauthorized()) // 상태 코드 401 Unauthorized 확인
//                 .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                 .andExpect(jsonPath("$.success").value(false))
//                 .andExpect(jsonPath("$.error.code").value("AUTHENTICATION_FAILED"))
//                 .andExpect(jsonPath("$.error.message").value(expectedErrorMessage))
//                 .andExpect(jsonPath("$.data").isEmpty());
//     }

//     @Test
//     @DisplayName("로그인 실패 - 존재하지 않는 사용자")
//     void login_fail_userNotFound() throws Exception {
//         // given: AuthService.login 메서드가 호출되면 BadCredentialsException 발생시키도록 설정
//         // 사용자 미존재 시에도 동일한 예외 및 메시지 반환하도록 AuthService가 구현됨
//         String expectedErrorMessage = "아이디 또는 비밀번호가 잘못되었습니다.";
//         given(authService.login(any(LoginRequestDto.class)))
//                 .willThrow(new BadCredentialsException(expectedErrorMessage));

//         // when: 로그인 요청 실행 (다른 이메일 사용, 빌더 패턴 사용)
//         LoginRequestDto nonExistingUserDto = LoginRequestDto.builder()
//                 .userEmail("<EMAIL>")
//                 .password("password")
//                 .build();
//         ResultActions resultActions = mockMvc.perform(post("/api/way/auth/login")
//                 .contentType(MediaType.APPLICATION_JSON)
//                 .content(objectMapper.writeValueAsString(nonExistingUserDto))
//                 .with(csrf())); // CSRF 토큰 추가

//         // then: 응답 검증 (401 Unauthorized 및 표준 에러 응답)
//         resultActions
//                 .andExpect(status().isUnauthorized()) // 상태 코드 401 Unauthorized 확인
//                 .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                 .andExpect(jsonPath("$.success").value(false))
//                 .andExpect(jsonPath("$.error.code").value("AUTHENTICATION_FAILED"))
//                 .andExpect(jsonPath("$.error.message").value(expectedErrorMessage))
//                 .andExpect(jsonPath("$.data").isEmpty());
//     }

//     @Test
//     @DisplayName("토큰 갱신 성공")
//     void refreshToken_success() throws Exception {
//         // given: AuthService.refreshToken 메서드가 호출되면 새로운 토큰 정보 반환하도록 설정
//         given(authService.refreshToken(any(RefreshTokenRequestDto.class))).willReturn(refreshedTokenResponseDto);

//         // when: /api/way/auth/refresh 엔드포인트로 POST 요청 실행
//         ResultActions resultActions = mockMvc.perform(post("/api/way/auth/refresh")
//                 .contentType(MediaType.APPLICATION_JSON)
//                 .content(objectMapper.writeValueAsString(refreshTokenRequestDto))
//                 .with(csrf())); // CSRF 토큰 추가

//         // then: 응답 검증 (200 OK 및 새로운 토큰 정보)
//         resultActions
//                 .andExpect(status().isOk())
//                 .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                 .andExpect(jsonPath("$.success").value(true))
//                 .andExpect(jsonPath("$.data.accessToken").value(refreshedTokenResponseDto.getAccessToken()))
//                 .andExpect(jsonPath("$.data.refreshToken").value(refreshedTokenResponseDto.getRefreshToken())) // 필요시 검증
//                 .andExpect(jsonPath("$.data.expiresIn").value(refreshedTokenResponseDto.getExpiresIn()))
//                 .andExpect(jsonPath("$.message").isEmpty());
//     }

//     @Test
//     @DisplayName("토큰 갱신 실패 - 유효하지 않은 토큰")
//     void refreshToken_fail_invalidToken() throws Exception {
//         // given: AuthService.refreshToken 메서드가 호출되면 예외 발생 (예: IllegalArgumentException)
//         String expectedErrorMessage = "유효하지 않은 Refresh Token 입니다.";
//         given(authService.refreshToken(any(RefreshTokenRequestDto.class)))
//                 .willThrow(new IllegalArgumentException(expectedErrorMessage)); // 적절한 예외 타입 선택 필요

//         // when: 토큰 갱신 요청 실행
//         ResultActions resultActions = mockMvc.perform(post("/api/way/auth/refresh")
//                 .contentType(MediaType.APPLICATION_JSON)
//                 .content(objectMapper.writeValueAsString(refreshTokenRequestDto))
//                 .with(csrf())); // CSRF 토큰 추가

//         // then: 응답 검증 (401 Unauthorized 또는 400 Bad Request 및 표준 에러 응답)
//         // 여기서는 컨트롤러 Advice에서 IllegalArgumentException을 어떻게 처리하는지에 따라 상태 코드가 달라짐
//         // 보통 유효하지 않은 토큰은 401 Unauthorized로 처리하는 경우가 많음
//         resultActions
//                 .andExpect(status().isUnauthorized()) // 또는 .isBadRequest()
//                 .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                 .andExpect(jsonPath("$.success").value(false))
//                 .andExpect(jsonPath("$.error.code").value("INVALID_INPUT_VALUE"))
//                 .andExpect(jsonPath("$.error.message").value(expectedErrorMessage))
//                 .andExpect(jsonPath("$.data").isEmpty());
//     }

//     @Test
//     @DisplayName("로그아웃 성공")
//     @WithMockUser // 가상의 인증된 사용자 (username="user", password="password", roles="USER")로 요청 시뮬레이션
//     void logout_success() throws Exception {
//         // given: AuthService.logout 메서드가 호출되면 아무 작업도 하지 않도록 설정 (void 메서드)
//         doNothing().when(authService).logout(anyString()); // 수정: any() -> anyString()

//         // when: /api/way/auth/logout 엔드포인트로 POST 요청 실행 (인증된 사용자)
//         ResultActions resultActions = mockMvc.perform(post("/api/way/auth/logout")
//                 .contentType(MediaType.APPLICATION_JSON) // 로그아웃은 보통 본문 없음
//                 .with(csrf())); // CSRF 토큰 추가

//         // then: 응답 검증 (200 OK 및 표준 성공 응답)
//         resultActions
//                 .andExpect(status().isOk())
//                 .andExpect(content().contentType(MediaType.APPLICATION_JSON))
//                 .andExpect(jsonPath("$.success").value(true))
//                 .andExpect(jsonPath("$.data").isEmpty())
//                 .andExpect(jsonPath("$.message").isEmpty());

//         // 추가 검증: AuthService.logout 메서드가 호출되었는지 확인
//         verify(authService).logout(anyString()); // 수정: any() -> anyString()
//     }

//     @Test
//     @DisplayName("로그아웃 실패 - 미인증 사용자")
//     void logout_fail_unauthenticated() throws Exception {
//         // given: 별도 설정 없음 (인증되지 않은 상태)

//         // when: /api/way/auth/logout 엔드포인트로 POST 요청 실행 (인증 정보 없음)
//         ResultActions resultActions = mockMvc.perform(post("/api/way/auth/logout")
//                 .contentType(MediaType.APPLICATION_JSON)
//                 .with(csrf())); // CSRF 토큰 추가

//         // then: 응답 검증 (401 Unauthorized)
//         // Spring Security Filter가 요청을 가로채므로 컨트롤러까지 도달하지 않음
//         // 따라서 표준 에러 응답 형식이 아닐 수 있음 (Security 기본 에러 응답)
//         resultActions
//                 .andExpect(status().isUnauthorized());
//     }
// }
