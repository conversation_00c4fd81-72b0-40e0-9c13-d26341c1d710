package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 대량 등록 결과 DTO
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BulkRegistrationResultDto {
    private int totalCount;           // 전체 처리된 행 수
    private int successCount;         // 성공한 등록 수
    private int failureCount;         // 실패한 등록 수
    private List<String> errorMessages; // 오류 메시지 목록
    private String failedRegistrations; // 실패한 등록 상세 정보
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class FailedRegistrationDto {
        private int rowNumber;        // 엑셀 행 번호
        private String attendeeName;  // 참석자 이름
        private String attendeeEmail; // 참석자 이메일
        private String errorMessage;  // 오류 메시지
    }
}