package kr.wayplus.wayplus_qr.entity;

import lombok.*;

import java.time.LocalDateTime;

public class MenuEntity {

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Menu {
        private Long menuId;
        private Long parentMenuId;
        private String menuCode;
        private String menuName;
        private String menuUrl;
        private String menuIcon;
        private Integer menuLevel;
        private Integer displayOrder;
        private String status; // ACTIVE, INACTIVE
        private String createUserEmail;
        private LocalDateTime createDate;
        private String updateUserEmail;
        private LocalDateTime lastUpdateDate;
        private String deleteUserEmail;
        private LocalDateTime deleteDate;
        private String useYn;
        private String deleteYn;
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MenuRolePermission {
        private Long permissionId;
        private Long menuId;
        private String roleId;
        private String isAccessible; // Y, N
        private String createUserEmail;
        private LocalDateTime createDate;
        private String updateUserEmail;
        private LocalDateTime lastUpdateDate;
        private String deleteUserEmail;
        private LocalDateTime deleteDate;
        private String useYn;
        private String deleteYn;
    }

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MenuUserPermission {
        private Long userPermissionId;
        private Long menuId;
        private String userEmail;
        private String isAccessible; // Y, N
        private String canRead; // Y, N - 읽기 권한
        private String canWrite; // Y, N - 쓰기(생성) 권한
        private String canUpdate; // Y, N - 수정 권한
        private String canDelete; // Y, N - 삭제 권한
        private String permissionNote;
        private String createUserEmail;
        private LocalDateTime createDate;
        private String updateUserEmail;
        private LocalDateTime lastUpdateDate;
        private String deleteUserEmail;
        private LocalDateTime deleteDate;
        private String useYn;
        private String deleteYn;
    }
}
