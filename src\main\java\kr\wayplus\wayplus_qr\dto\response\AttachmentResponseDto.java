package kr.wayplus.wayplus_qr.dto.response;

import kr.wayplus.wayplus_qr.entity.InquiryAttachment;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 첨부파일 응답 DTO.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttachmentResponseDto {
    private Long attachmentId;
    private String originalFileName;
    private String storedFilePath;
    private Long fileSize;
    private String mimeType;

    public static AttachmentResponseDto fromEntity(InquiryAttachment entity) {
        return AttachmentResponseDto.builder()
                .attachmentId(entity.getAttachmentId())
                .originalFileName(entity.getOriginalFileName())
                .storedFilePath(entity.getStoredFilePath())
                .fileSize(entity.getFileSize())
                .mimeType(entity.getMimeType())
                .build();
    }
}
