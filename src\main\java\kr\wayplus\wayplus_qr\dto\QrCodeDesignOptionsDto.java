package kr.wayplus.wayplus_qr.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class QrCodeDesignOptionsDto {

    @JsonProperty("dotsOptions")
    private DotsOptions dotsOptions;

    @JsonProperty("backgroundOptions")
    private BackgroundOptions backgroundOptions;

    @JsonProperty("logoRatio")
    private Double logoRatio;

    @JsonProperty("errorCorrectionLevel")
    private String errorCorrectionLevel;

    @JsonProperty("layoutOptions")
    private LayoutOptions layoutOptions;

    @JsonProperty("useA4Canvas")
    private Boolean useA4Canvas;

    @JsonProperty("useBackgroundImage")
    private Boolean useBackgroundImage;

    @JsonProperty("a4Size")
    private A4Size a4Size;

    @JsonProperty("qrLayout")
    private QrLayout qrLayout;

    @JsonProperty("backgroundFitMode")
    private String backgroundFitMode;

    @JsonProperty("a4CanvasImage")
    private String a4CanvasImage;

    @JsonProperty("a4CanvasImagePath")
    private String a4CanvasImagePath;

    @Data
    public static class DotsOptions {
        @JsonProperty("color")
        private String color;

        @JsonProperty("type")
        private String type;

        // 기존 eyeColor와 eyeType은 cornersSquareOptions 또는 cornersDotOptions로 대체될 수 있으므로,
        // 프론트엔드와 협의하여 유지 여부를 결정해야 합니다.
        // 우선 주석 처리하거나, 프론트엔드에서 더 이상 사용하지 않는다면 삭제할 수 있습니다.
        // @JsonProperty("eyeColor")
        // private String eyeColor;

        // @JsonProperty("eyeType")
        // private String eyeType;

        @JsonProperty("cornersSquareOptions")
        private CornersSquareOptions cornersSquareOptions;

        @JsonProperty("cornersDotOptions")
        private CornersDotOptions cornersDotOptions;
    }

    @Data
    public static class CornersSquareOptions {
        @JsonProperty("color")
        private String color;

        @JsonProperty("type")
        private String type;
    }

    @Data
    public static class CornersDotOptions {
        @JsonProperty("color")
        private String color;

        @JsonProperty("type")
        private String type;
    }

    @Data
    public static class BackgroundOptions {
        @JsonProperty("color")
        private String color;
    }

    @Data
    public static class LayoutOptions {
        @JsonProperty("canvas")
        private Canvas canvas;

        @JsonProperty("qrPlacement")
        private QrPlacement qrPlacement;

        @JsonProperty("backgroundImageFit")
        private String backgroundImageFit;

        @Data
        public static class Canvas {
            @JsonProperty("widthPx")
            private Integer widthPx;

            @JsonProperty("heightPx")
            private Integer heightPx;
        }

        @Data
        public static class QrPlacement {
            @JsonProperty("position")
            private Position position;

            @JsonProperty("sizeRatio")
            private Double sizeRatio;

            @Data
            public static class Position {
                @JsonProperty("x")
                private Double x;

                @JsonProperty("y")
                private Double y;
            }
        }
    }

    @Data
    public static class A4Size {
        @JsonProperty("width")
        private Integer width;

        @JsonProperty("height")
        private Integer height;
    }

    @Data
    public static class QrLayout {
        @JsonProperty("positionX")
        private Integer positionX;

        @JsonProperty("positionY")
        private Integer positionY;

        @JsonProperty("width")
        private Integer width;

        @JsonProperty("height")
        private Integer height;

        @JsonProperty("unit")
        private String unit;
    }
}
