package kr.wayplus.wayplus_qr.exception;

import lombok.Getter;

@Getter
public class CustomLandingPageException extends RuntimeException {

    private final ErrorCode errorCode;

    public CustomLandingPageException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public CustomLandingPageException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}
