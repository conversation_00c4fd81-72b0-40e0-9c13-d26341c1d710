package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.entity.AttendeeBenefitRedemption;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Optional;

/**
 * MyBatis Mapper for attendee_benefit_redemption.
 */
@Mapper
public interface AttendeeBenefitRedemptionMapper {
    /**
     * 특정 참가자의 혜택 사용 내역 조회
     */
    List<AttendeeBenefitRedemption> selectRedemptionsByAttendeeId(@Param("attendeeId") Long attendeeId);

    /**
     * 참가자-혜택 단일 사용 기록 조회
     */
    Optional<AttendeeBenefitRedemption> selectByAttendeeAndBenefit(@Param("attendeeId") Long attendeeId,
                                                                   @Param("benefitId") Long benefitId);

    /**
     * 사용 기록 삽입
     */
    int insertRedemption(AttendeeBenefitRedemption redemption);

    /**
     * 혜택 사용 취소 (레코드 삭제)
     */
    int deleteRedemption(@Param("attendeeId") Long attendeeId, @Param("benefitId") Long benefitId);
}
