package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import kr.wayplus.wayplus_qr.entity.InquiryType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 문의 생성 요청 DTO.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryCreateRequestDto {

    @NotNull
    private Long projectId;

    @NotBlank
    private String inquiryTitle;

    @NotBlank
    private String inquiryContent;

    @NotNull
    private InquiryType inquiryType;
}
