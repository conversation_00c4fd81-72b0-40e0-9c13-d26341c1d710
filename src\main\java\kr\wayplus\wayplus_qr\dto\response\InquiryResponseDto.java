package kr.wayplus.wayplus_qr.dto.response;

import kr.wayplus.wayplus_qr.entity.Inquiry;
import kr.wayplus.wayplus_qr.entity.InquiryStatus;
import kr.wayplus.wayplus_qr.entity.InquiryType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 문의 상세 응답 DTO.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryResponseDto {
    private Long inquiryId;
    private Long projectId;
    private String projectName;
    private String userEmail;
    private String inquiryTitle;
    private String inquiryContent;
    private InquiryType inquiryType;
    private InquiryStatus inquiryStatus;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    private List<InquiryCommentResponseDto> comments;
    private List<AttachmentResponseDto> attachments;

    public static InquiryResponseDto fromEntity(Inquiry entity) {
        return InquiryResponseDto.builder()
                .inquiryId(entity.getInquiryId())
                .projectId(entity.getProjectId())
                .projectName(entity.getProjectName())
                .userEmail(entity.getUserEmail())
                .inquiryTitle(entity.getInquiryTitle())
                .inquiryContent(entity.getInquiryContent())
                .inquiryType(entity.getInquiryType())
                .inquiryStatus(entity.getInquiryStatus())
                .createdAt(entity.getCreatedAt())
                .updatedAt(entity.getUpdatedAt())
                .build();
    }
}
