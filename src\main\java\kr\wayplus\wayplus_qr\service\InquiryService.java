package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.request.InquiryCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.InquiryStatusUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.InquiryUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.AttachmentResponseDto;
import kr.wayplus.wayplus_qr.dto.response.InquiryResponseDto;
import kr.wayplus.wayplus_qr.entity.Inquiry;
import kr.wayplus.wayplus_qr.entity.InquiryAttachment;
import kr.wayplus.wayplus_qr.entity.InquiryComment;
import kr.wayplus.wayplus_qr.entity.InquiryStatus;
import kr.wayplus.wayplus_qr.entity.InquiryType;
import kr.wayplus.wayplus_qr.entity.NotificationType;
import kr.wayplus.wayplus_qr.entity.enums.UserRole;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import kr.wayplus.wayplus_qr.mapper.InquiryAttachmentMapper;
import kr.wayplus.wayplus_qr.mapper.InquiryCommentMapper;
import kr.wayplus.wayplus_qr.mapper.InquiryMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class InquiryService {

    private final InquiryMapper inquiryMapper;
    private final InquiryCommentMapper inquiryCommentMapper;
    private final InquiryAttachmentMapper inquiryAttachmentMapper;
    private final UserMapper userMapper;
    private final FileStorageService fileStorageService;
    private final NotificationService notificationService;

    /**
     * 문의 생성
     * 
     * @param requestDto    문의 생성 요청 DTO
     * @param userEmail     사용자 이메일
     * @param attachments   첨부파일 목록 (선택)
     * @return              생성된 문의 정보
     * @throws IOException 
     */
    @Transactional
    public InquiryResponseDto createInquiry(
            Long projectId,
            String inquiryTitle,
            String inquiryContent,
            String inquiryType,
            String userEmail,
            List<MultipartFile> attachments) throws IOException {
        
        // 사용자 권한 확인 (PROJECT_ADMIN, SUB_ADMIN만 문의 생성 가능)
        if (!checkUserProjectRoles(projectId, userEmail, UserRole.PROJECT_ADMIN, UserRole.SUB_ADMIN)) {
            throw new QRcodeException(ErrorCode.ACCESS_DENIED, "프로젝트에 문의를 생성할 권한이 없습니다.");
        }

        // Inquiry 엔티티 생성
        Inquiry inquiry = Inquiry.builder()
                .projectId(projectId)
                .userEmail(userEmail)
                .inquiryTitle(inquiryTitle)
                .inquiryContent(inquiryContent)
                .inquiryType(InquiryType.valueOf(inquiryType))
                .inquiryStatus(InquiryStatus.PENDING) // 초기 상태: 접수됨
                .build();

        // 문의 저장
        int insertedCount = inquiryMapper.insertInquiry(inquiry);
        if (insertedCount == 0 || inquiry.getInquiryId() == null) {
            log.error("Failed to insert inquiry data or retrieve generated ID");
            throw new QRcodeException(ErrorCode.DATABASE_ERROR, "문의 데이터 저장 또는 ID 생성에 실패했습니다.");
        }

        // 첨부파일 처리
        List<AttachmentResponseDto> savedAttachments = processAttachments(inquiry.getInquiryId(), null, attachments);

        // SUPER_ADMIN에게 알림 생성
        notificationService.createNotificationForSuperAdmins(
                inquiry.getInquiryId(),
                "새로운 문의가 등록되었습니다. 문의 제목 : " + inquiry.getInquiryTitle(),
                NotificationType.NEW_INQUIRY_COMMENT
        );

        // 응답 DTO 생성
        InquiryResponseDto responseDto = InquiryResponseDto.fromEntity(inquiry);
        responseDto.setAttachments(savedAttachments);
        
        return responseDto;
    }

    /**
     * 문의 목록 조회 (페이징, 필터링) - 모든 프로젝트의 문의를 반환합니다.
     * 
     * @param projectId      프로젝트 ID (무시됨, 항상 null로 처리함)
     * @param status         문의 상태 (선택)
     * @param inquiryType    문의 유형 (선택)
     * @param searchKeyword  검색 키워드 (선택)
     * @param userEmail      사용자 이메일
     * @param pageable       페이징 정보
     * @return               문의 목록 (페이징)
     */
    @Transactional(readOnly = true)
    public Page<InquiryResponseDto> getInquiries(
            Long projectId,
            String inquiryType,
            String inquiryStatus,
            String searchType,
            String searchKeyword,
            String userEmail,
            Pageable pageable) {
        
        // 사용자 존재 여부만 확인합니다.
        userMapper.selectUserRoleByEmail(userEmail)
                .orElseThrow(() -> new QRcodeException(ErrorCode.USER_NOT_FOUND, "사용자를 찾을 수 없습니다: " + userEmail));
        
        // 프로젝트 ID는 항상 무시하고 모든 문의를 조회합니다.
        // projectId 파라미터는 API 호환성을 위해 유지하지만 실제로는 사용하지 않습니다.

        String searchColumn = null;
        if (searchKeyword != null && !searchKeyword.isBlank()) {
            switch (searchType) {
                case "inquiryTitle": searchColumn = "inquiry_title"; break;
                case "inquiryType": searchColumn = "inquiry_type"; break;
                case "inquiryStatus": searchColumn = "inquiry_status"; break;
                case "userEmail": searchColumn = "user_email"; break;
                case "createDate": searchColumn = "created_at"; break;
            }
        }
        // 문의 목록 조회 - projectId는 항상 null로 전달하여 모든 프로젝트의 문의를 조회합니다.
        List<Inquiry> inquiries = inquiryMapper.selectInquiriesByCondition(
                null, inquiryType, inquiryStatus, searchColumn, searchKeyword, pageable);
        long total = inquiryMapper.countInquiriesByCondition(
                null, inquiryType, inquiryStatus, searchColumn, searchKeyword);

        // DTO 변환
        List<InquiryResponseDto> dtos = inquiries.stream()
                .map(InquiryResponseDto::fromEntity)
                .collect(Collectors.toList());

        return new PageImpl<>(dtos, pageable, total);
    }

    /**
     * 문의 상세 조회
     * 
     * @param inquiryId  문의 ID
     * @param userEmail  사용자 이메일
     * @return           문의 상세 정보
     */
    @Transactional(readOnly = true)
    public InquiryResponseDto getInquiryById(Long inquiryId, String userEmail) {
        // 문의 조회
        Inquiry inquiry = inquiryMapper.selectInquiryById(inquiryId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_NOT_FOUND, "문의를 찾을 수 없습니다."));

        // 사용자 권한 확인 (SUPER_ADMIN, 문의 작성자, 또는 프로젝트 관리자/부관리자만 조회 가능)
        boolean isSuperAdmin = isSuperAdmin(userEmail);
        boolean isAuthor = inquiry.getUserEmail().equals(userEmail);
        boolean isProjectMember = checkUserProjectRoles(
                inquiry.getProjectId(), userEmail, UserRole.PROJECT_ADMIN, UserRole.SUB_ADMIN);

        if (!(isSuperAdmin || isAuthor || isProjectMember)) {
            throw new QRcodeException(ErrorCode.INQUIRY_ACCESS_DENIED, "문의를 조회할 권한이 없습니다.");
        }

        // 댓글 목록 조회
        List<InquiryComment> comments = inquiryCommentMapper.selectCommentsByInquiryId(inquiryId);

        // 첨부파일 목록 조회
        List<InquiryAttachment> inquiryAttachments = inquiryAttachmentMapper.selectAttachmentsByInquiryId(inquiryId);

        // 댓글별 첨부파일 조회
        List<Long> commentIds = comments.stream()
                .map(InquiryComment::getCommentId)
                .collect(Collectors.toList());

        List<InquiryAttachment> commentAttachments = commentIds.isEmpty() ? 
                List.of() : 
                commentIds.stream()
                    .flatMap(commentId -> inquiryAttachmentMapper.selectAttachmentsByCommentId(commentId).stream())
                    .collect(Collectors.toList());

        // 응답 DTO 생성
        InquiryResponseDto responseDto = InquiryResponseDto.fromEntity(inquiry);
        
        // 댓글 DTO 변환
        responseDto.setComments(comments.stream()
                .map(comment -> {
                    var commentDto = kr.wayplus.wayplus_qr.dto.response.InquiryCommentResponseDto.fromEntity(comment);
                    
                    // 댓글별 첨부파일 설정
                    List<AttachmentResponseDto> attachmentDtos = commentAttachments.stream()
                            .filter(attachment -> comment.getCommentId().equals(attachment.getCommentId()))
                            .map(AttachmentResponseDto::fromEntity)
                            .collect(Collectors.toList());
                    
                    // 댓글별 첨부파일 설정
                    commentDto.setAttachments(attachmentDtos);
                    
                    return commentDto;
                })
                .collect(Collectors.toList()));
        
        // 문의 첨부파일 DTO 변환
        responseDto.setAttachments(inquiryAttachments.stream()
                .map(AttachmentResponseDto::fromEntity)
                .collect(Collectors.toList()));
        
        return responseDto;
    }

    /**
     * 문의 내용 수정 (제목은 수정 불가)
     * 
     * @param inquiryId   문의 ID
     * @param requestDto  수정 요청 DTO
     * @param attachments 첨부파일 목록 (선택)
     * @param userEmail   사용자 이메일
     * @return            수정된 문의 정보
     */
    @Transactional
    public InquiryResponseDto updateInquiry(
            Long inquiryId,
            String inquiryContent,
            String inquiryType,
            List<MultipartFile> attachments,
            String userEmail) {
        
        // 문의 조회
        Inquiry inquiry = inquiryMapper.selectInquiryById(inquiryId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_NOT_FOUND, "문의를 찾을 수 없습니다."));

        // 작성자 확인 (작성자만 수정 가능)
        if (!inquiry.getUserEmail().equals(userEmail)) {
            throw new QRcodeException(ErrorCode.INQUIRY_ACCESS_DENIED, "문의를 수정할 권한이 없습니다.");
        }

        // 내용 수정
        int updatedCount = inquiryMapper.updateInquiryContent(inquiryId, inquiryContent, inquiryType, userEmail);
        if (updatedCount == 0) {
            throw new QRcodeException(ErrorCode.DATABASE_ERROR, "문의 내용 수정에 실패했습니다.");
        }
        
        // 첨부파일 처리
        if (attachments != null && !attachments.isEmpty()) {
            try {
                // 첨부파일 추가
                processAttachments(inquiryId, null, attachments);
            } catch (Exception e) {
                log.error("Error processing attachments: {}", e.getMessage(), e);
                throw new QRcodeException(ErrorCode.FILE_PROCESSING_ERROR, "첨부파일 처리 중 오류가 발생했습니다.");
            }
        }

        // 업데이트된 문의 정보 조회
        return getInquiryById(inquiryId, userEmail);
    }

    /**
     * 문의 내용 수정 (첨부파일 없이 내용만 수정 - 배용성 유지를 위한 오버로딩)
     * 
     * @param inquiryId   문의 ID
     * @param inquiryContent  수정 요청 DTO
     * @param attachments 새로운 첨부파일 목록 (선택)
     * @param userEmail   사용자 이메일
     * @return            수정된 문의 정보
     */
    @Transactional
    public InquiryResponseDto updateInquiry(
            Long inquiryId,
            String inquiryContent,
            String inquiryType,
            String userEmail) {
        // 첨부파일 없이 호출하는 오버로딩 버전
        return updateInquiry(inquiryId, inquiryContent, inquiryType, null, userEmail);
    }

    /**
     * 문의 상태 변경 (SUPER_ADMIN만 가능)
     * 
     * @param inquiryId   문의 ID
     * @param requestDto  상태 변경 요청 DTO
     * @param userEmail   사용자 이메일
     * @return            변경된 문의 정보
     */
    @Transactional
    public InquiryResponseDto updateInquiryStatus(
            Long inquiryId,
            InquiryStatusUpdateRequestDto requestDto,
            String userEmail) {
        
        // SUPER_ADMIN 권한 확인
        if (!isSuperAdmin(userEmail)) {
            throw new QRcodeException(ErrorCode.ACCESS_DENIED, "문의 상태를 변경할 권한이 없습니다.");
        }

        // 문의 조회
        Inquiry inquiry = inquiryMapper.selectInquiryById(inquiryId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_NOT_FOUND, "문의를 찾을 수 없습니다."));

        // 상태 변경
        int updatedCount = inquiryMapper.updateInquiryStatus(inquiryId, requestDto.getNewStatus(), userEmail);
        if (updatedCount == 0) {
            throw new QRcodeException(ErrorCode.DATABASE_ERROR, "문의 상태 변경에 실패했습니다.");
        }

        InquiryStatus newStatus = requestDto.getNewStatus();
        String newStatusKor = "";
        switch (newStatus) {
            case PENDING:
                newStatusKor = "접수됨";
                break;
            case PROCESSING:
                newStatusKor = "처리중";
                break;
            case COMPLETED:
                newStatusKor = "답변완료";
                break;
            default:
                break;
        }
        // 상태 변경 알림 생성 (문의 작성자에게)
        notificationService.createNotification(
                inquiry.getUserEmail(),
                inquiryId,
                "문의 상태가 " + newStatusKor + "로 변경되었습니다.",
                NotificationType.INQUIRY_STATUS_UPDATED
        );

        // 업데이트된 문의 정보 조회
        return getInquiryById(inquiryId, userEmail);
    }

    /**
     * 문의 삭제 (SUPER_ADMIN 또는 작성자만 가능)
     * 
     * @param inquiryId  문의 ID
     * @param userEmail  사용자 이메일
     */
    @Transactional
    public void deleteInquiry(Long inquiryId, String userEmail) {
        // 문의 조회
        Inquiry inquiry = inquiryMapper.selectInquiryById(inquiryId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_NOT_FOUND, "문의를 찾을 수 없습니다."));

        // 권한 확인 (SUPER_ADMIN 또는 작성자만 삭제 가능)
        boolean isSuperAdmin = isSuperAdmin(userEmail);
        boolean isAuthor = inquiry.getUserEmail().equals(userEmail);

        if (!(isSuperAdmin || isAuthor)) {
            throw new QRcodeException(ErrorCode.INQUIRY_ACCESS_DENIED, "문의를 삭제할 권한이 없습니다.");
        }

        // 댓글 삭제 (CASCADE 확인)
        inquiryCommentMapper.deleteCommentsByInquiryId(inquiryId);

        // 첨부파일 삭제 (파일 시스템과 DB)
        List<InquiryAttachment> attachments = inquiryAttachmentMapper.selectAttachmentsByInquiryId(inquiryId);
        for (InquiryAttachment attachment : attachments) {
            try {
                fileStorageService.deleteFile(attachment.getStoredFilePath());
            } catch (Exception e) {
                log.warn("Failed to delete attachment file: {}", attachment.getStoredFilePath(), e);
            }
        }
        inquiryAttachmentMapper.deleteAttachmentsByInquiryId(inquiryId);

        // 알림 삭제
        notificationService.deleteNotificationsByInquiryId(inquiryId);

        // 문의 논리적 삭제
        int deletedCount = inquiryMapper.deleteInquiry(inquiryId, userEmail);
        if (deletedCount == 0) {
            throw new QRcodeException(ErrorCode.DATABASE_ERROR, "문의 삭제에 실패했습니다.");
        }
    }

    // ...

    /**
     * 첨부파일 다운로드 (인증된 사용자용)
     * 
     * @param attachmentId  첨부파일 ID
     * @param userEmail     사용자 이메일
     * @return              첨부파일 Resource
     */
    @Transactional(readOnly = true)
    public Resource downloadAttachment(Long attachmentId, String userEmail) {
        // 첨부파일 조회
        InquiryAttachment attachment = inquiryAttachmentMapper.selectAttachmentById(attachmentId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.ENTITY_NOT_FOUND, "첨부파일을 찾을 수 없습니다."));

        // 권한 확인 (첨부파일이 연결된, 문의 또는 댓글에 접근할 권한이 있는지)
        if (attachment.getInquiryId() != null) {
            // 문의 첨부파일인 경우
            Inquiry inquiry = inquiryMapper.selectInquiryById(attachment.getInquiryId())
                    .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_NOT_FOUND, "문의를 찾을 수 없습니다."));
            
            boolean isSuperAdmin = isSuperAdmin(userEmail);
            boolean isAuthor = inquiry.getUserEmail().equals(userEmail);
            boolean isProjectMember = checkUserProjectRoles(
                    inquiry.getProjectId(), userEmail, UserRole.PROJECT_ADMIN, UserRole.SUB_ADMIN);

            if (!(isSuperAdmin || isAuthor || isProjectMember)) {
                throw new QRcodeException(ErrorCode.INQUIRY_ACCESS_DENIED, "첨부파일을 다운로드할 권한이 없습니다.");
            }
        } else if (attachment.getCommentId() != null) {
            // 댓글 첨부파일인 경우
            InquiryComment comment = inquiryCommentMapper.selectCommentById(attachment.getCommentId())
                    .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_COMMENT_NOT_FOUND, "댓글을 찾을 수 없습니다."));
            
            Inquiry inquiry = inquiryMapper.selectInquiryById(comment.getInquiryId())
                    .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_NOT_FOUND, "문의를 찾을 수 없습니다."));
            
            boolean isSuperAdmin = isSuperAdmin(userEmail);
            boolean isAuthor = inquiry.getUserEmail().equals(userEmail);
            boolean isProjectMember = checkUserProjectRoles(
                    inquiry.getProjectId(), userEmail, UserRole.PROJECT_ADMIN, UserRole.SUB_ADMIN);

            if (!(isSuperAdmin || isAuthor || isProjectMember)) {
                throw new QRcodeException(ErrorCode.INQUIRY_ACCESS_DENIED, "첨부파일을 다운로드할 권한이 없습니다.");
            }
        }

        // 파일 로드
        try {
            return fileStorageService.loadFileAsResource(attachment.getStoredFilePath());
        } catch (Exception e) {
            throw new QRcodeException(ErrorCode.FILE_PROCESSING_ERROR, "파일 다운로드 중 오류가 발생했습니다.");
        }
    }

    /**
     * 첨부파일 삭제
     * 
     * @param attachmentId  첨부파일 ID
     * @param userEmail     사용자 이메일
     * @throws QRcodeException 첨부파일을 찾을 수 없거나 권한이 없는 경우
     */
    @Transactional
    public void deleteAttachment(Long attachmentId, String userEmail) {
        // 첨부파일 조회
        InquiryAttachment attachment = inquiryAttachmentMapper.selectAttachmentById(attachmentId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.ENTITY_NOT_FOUND, "첨부파일을 찾을 수 없습니다."));

        // 권한 확인 (첨부파일이 연결된, 문의 또는 댓글에 접근할 권한이 있는지)
        if (attachment.getInquiryId() != null) {
            // 문의 첨부파일인 경우
            Inquiry inquiry = inquiryMapper.selectInquiryById(attachment.getInquiryId())
                    .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_NOT_FOUND, "문의를 찾을 수 없습니다."));
            
            boolean isSuperAdmin = isSuperAdmin(userEmail);
            boolean isAuthor = inquiry.getUserEmail().equals(userEmail);
            boolean isProjectMember = checkUserProjectRoles(
                    inquiry.getProjectId(), userEmail, UserRole.PROJECT_ADMIN, UserRole.SUB_ADMIN);

            if (!(isSuperAdmin || isAuthor || isProjectMember)) {
                throw new QRcodeException(ErrorCode.INQUIRY_ACCESS_DENIED, "첨부파일을 삭제할 권한이 없습니다.");
            }
        } else if (attachment.getCommentId() != null) {
            // 댓글 첨부파일인 경우
            InquiryComment comment = inquiryCommentMapper.selectCommentById(attachment.getCommentId())
                    .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_COMMENT_NOT_FOUND, "댓글을 찾을 수 없습니다."));
            
            Inquiry inquiry = inquiryMapper.selectInquiryById(comment.getInquiryId())
                    .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_NOT_FOUND, "문의를 찾을 수 없습니다."));
            
            boolean isSuperAdmin = isSuperAdmin(userEmail);
            boolean isAuthor = inquiry.getUserEmail().equals(userEmail);
            boolean isProjectMember = checkUserProjectRoles(
                    inquiry.getProjectId(), userEmail, UserRole.PROJECT_ADMIN, UserRole.SUB_ADMIN);

            if (!(isSuperAdmin || isAuthor || isProjectMember)) {
                throw new QRcodeException(ErrorCode.INQUIRY_ACCESS_DENIED, "첨부파일을 삭제할 권한이 없습니다.");
            }
        }
        
        try {
            // 실제 파일 삭제
            String storedFilePath = attachment.getStoredFilePath();
            if (storedFilePath != null && !storedFilePath.isEmpty()) {
                // 파일 시스템에서 삭제
                java.nio.file.Path filePath = java.nio.file.Paths.get(storedFilePath);
                java.nio.file.Files.deleteIfExists(filePath);
            }
            
            // DB에서 첨부파일 정보 삭제
            int deletedCount = inquiryAttachmentMapper.deleteAttachment(attachmentId);
            
            if (deletedCount == 0) {
                throw new QRcodeException(ErrorCode.DATABASE_ERROR, "첨부파일 삭제에 실패했습니다.");
            }
            
            log.info("Attachment deleted successfully: {}, by user: {}", attachmentId, userEmail);
        } catch (IOException e) {
            log.error("Error deleting attachment file: {}", e.getMessage(), e);
            throw new QRcodeException(ErrorCode.FILE_PROCESSING_ERROR, "첨부파일 삭제 중 오류가 발생했습니다.");
        } catch (Exception e) {
            log.error("Error deleting attachment: {}", e.getMessage(), e);
            throw new QRcodeException(ErrorCode.DATABASE_ERROR, "첨부파일 삭제 중 오류가 발생했습니다.");
        }
    }

    /**
     * 첨부파일 처리 (저장)
     */
    private List<AttachmentResponseDto> processAttachments(
            Long inquiryId,
            Long commentId,
            List<MultipartFile> attachments) throws IOException {
        
        if (attachments == null || attachments.isEmpty()) {
            return List.of();
        }

        return attachments.stream()
                .filter(file -> !file.isEmpty())
                .map(file -> {
                    // 파일 저장
                    String originalFileName = file.getOriginalFilename();
                    
                    String storedFilePath = fileStorageService.storeFilePath(file, "inquiry");

                    // DB에 첨부파일 정보 저장
                    InquiryAttachment attachment = InquiryAttachment.builder()
                            .inquiryId(inquiryId)
                            .commentId(commentId)
                            .originalFileName(originalFileName)
                            .storedFilePath(storedFilePath)
                            .fileSize(file.getSize())
                            .mimeType(file.getContentType())
                            .build();

                    inquiryAttachmentMapper.insertAttachment(attachment);

                    return AttachmentResponseDto.fromEntity(attachment);
                })
                .collect(Collectors.toList());
    }

    /**
     * 사용자가 SUPER_ADMIN 역할인지 확인
     */
    private boolean isSuperAdmin(String userEmail) {
        return userMapper.selectUserRoleByEmail(userEmail)
                .map(role -> role.equals(UserRole.SUPER_ADMIN.name()))
                .orElse(false);
    }

    /**
     * 사용자가 프로젝트에 대해 특정 역할을 가지고 있는지 확인
     * (QrCodeService에서 가져온 메소드 재활용)
     */
    private boolean checkUserProjectRoles(Long projectId, String userEmail, UserRole... requiredRoles) {
        if (projectId == null || userEmail == null || requiredRoles == null || requiredRoles.length == 0) {
            return false;
        }

        String userRole = userMapper.selectUserRoleByEmail(userEmail)
                .orElseThrow(() -> new QRcodeException(ErrorCode.USER_NOT_FOUND, "사용자를 찾을 수 없습니다: " + userEmail));
        
        if (userRole.equals(UserRole.SUPER_ADMIN.name())) {
            return true;
        }

        List<String> requiredRoleNames = java.util.Arrays.stream(requiredRoles)
                .map(UserRole::name)
                .collect(Collectors.toList());

        int count = userMapper.countUserProjectMembershipWithRoles(userEmail, projectId, requiredRoleNames);
        return count > 0;
    }

    /**
     * 첨부파일 다운로드 (익명 사용자용 - 권한 검사 없음)
     * 
     * @param attachmentId  첨부파일 ID
     * @return              첨부파일 Resource
     */
    @Transactional(readOnly = true)
    public Resource downloadAttachmentWithoutAuth(Long attachmentId) {
        // 첨부파일 조회
        InquiryAttachment attachment = inquiryAttachmentMapper.selectAttachmentById(attachmentId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.ENTITY_NOT_FOUND, "첨부파일을 찾을 수 없습니다."));

        // 파일 로드 (권한 검사 없음)
        try {
            return fileStorageService.loadFileAsResource(attachment.getStoredFilePath());
        } catch (Exception e) {
            throw new QRcodeException(ErrorCode.FILE_PROCESSING_ERROR, "파일 다운로드 중 오류가 발생했습니다.");
        }
    }
}
