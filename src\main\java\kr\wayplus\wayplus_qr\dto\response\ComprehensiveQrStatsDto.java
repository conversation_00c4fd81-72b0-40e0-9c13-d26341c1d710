package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 통합 QR 코드 통계 DTO
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ComprehensiveQrStatsDto {
    
    // 기본 통계
    private BasicStats basic;
    
    // 일별 QR 코드 생성 및 스캔 통계
    private DailyQrStats dailyQrStats;
    
    // 타입별 분포
    private TypeDistribution typeDistribution;
    
    // 상태별 분포
    private StatusDistribution statusDistribution;
    
    // 기기별 통계
    private DeviceStats deviceStats;
    
    // 시간별 통계
    private TimeStats timeStats;
    
    // 교환권 통계
    private ExchangeStats exchangeStats;
    
    // 스캔된 QR 코드 목록 (최대 5개)
    private ScannedQrCodes scannedQrCodes;
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BasicStats {
        private Long totalQrCodes;
        private Long totalScans;
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DailyQrStats {
        private List<String> dates;
        private List<Long> createdCounts;
        private List<Long> scannedCounts;
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TypeDistribution {
        private Map<String, Long> distribution;
        private Map<String, Double> percentage;
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class StatusDistribution {
        private Map<String, Long> distribution;
        private Map<String, Double> percentage;
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DeviceStats {
        private DeviceTypeStats device;
        private DeviceTypeStats browser;
        private DeviceTypeStats os;
        
        @Getter
        @Setter
        @NoArgsConstructor
        @AllArgsConstructor
        public static class DeviceTypeStats {
            private Map<String, Long> counts;
            private Map<String, Double> percentage;
        }
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TimeStats {
        private Map<Integer, Long> hourly;
        private Map<String, Long> weekday;
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExchangeStats {
        private ExchangeTotal total;
        private ExchangeDaily daily;
        private ExchangeApprover approver;
        private Map<String, Long> hourly;
        
        @Getter
        @Setter
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ExchangeTotal {
            private Long totalExchangeQrCodes;
            private Long totalExchangeCount;
            private Long totalUsedCount;
            private Double usageRate;
        }
        
        @Getter
        @Setter
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ExchangeDaily {
            private List<String> dates;
            private List<Long> counts;
        }
        
        @Getter
        @Setter
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ExchangeApprover {
            private Map<String, Long> counts;
            private Map<String, Double> percentage;
        }
    }
    
    @Getter
    @Setter
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ScannedQrCodes {
        private List<ScannedQrInfo> qrCodes;
        
        @Getter
        @Setter
        @NoArgsConstructor
        @AllArgsConstructor
        public static class ScannedQrInfo {
            private Long qrCodeId;
            private String qrName;
            private String qrType;
            private String location;
            private Float installationLocationLat;
            private Float installationLocationLng;
            private Long scanCount;
            private String createdAt;
        }
    }
}
