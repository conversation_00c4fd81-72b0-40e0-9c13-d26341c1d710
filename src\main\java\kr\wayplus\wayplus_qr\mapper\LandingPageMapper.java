package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.entity.LandingPage;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Mapper
public interface LandingPageMapper {
    int insertLandingPage(LandingPage landingPage);
    Optional<LandingPage> selectLandingPageById(@Param("landingPageId") Long landingPageId);
    Optional<LandingPage> selectLandingPageByProjectIdAndPageTitle(@Param("projectId") Long projectId, @Param("pageTitle") String pageTitle);
    List<LandingPage> selectLandingPagesByProjectId(@Param("projectId") Long projectId,
                                                 @Param("status") String status,
                                                 @Param("searchColumn") String searchColumn,
                                                 @Param("searchKeyword") String searchKeyword,
                                                 @Param("pageable") Pageable pageable);
    long countLandingPagesByProjectId(@Param("projectId") Long projectId,
                                       @Param("status") String status,
                                       @Param("searchColumn") String searchColumn,
                                       @Param("searchKeyword") String searchKeyword);
    int updateLandingPage(LandingPage landingPage);
    int logicalDeleteLandingPage(@Param("landingPageId") Long landingPageId, @Param("userEmail") String userEmail);

    /**
     * SUPER_ADMIN: 모든 랜딩 페이지 목록 조회 (페이징 및 동적 정렬)
     * @param params Map containing offset, pageSize, sortColumn, sortDirection
     * @return 랜딩 페이지 목록
     */
    List<LandingPage> selectAllLandingPagesForSuperAdmin(
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword,
            @Param("pageable") Pageable pageable);

    /**
     * SUPER_ADMIN: 모든 랜딩 페이지 개수 조회
     * @return 랜딩 페이지 총 개수
     */
    long countAllLandingPagesForSuperAdmin(
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword);

    /**
     * SUPER_ADMIN: 특정 프로젝트의 랜딩 페이지 목록 조회 (페이징 및 동적 정렬)
     * @param params Map containing projectId, offset, pageSize, sortColumn, sortDirection
     * @return 랜딩 페이지 목록
     */
    List<LandingPage> selectLandingPagesByProjectIdForSuperAdmin(
            @Param("projectId") Long projectId,
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword,
            @Param("pageable") Pageable pageable);

    /**
     * SUPER_ADMIN: 특정 프로젝트의 랜딩 페이지 개수 조회
     * @param projectId 프로젝트 ID
     * @return 랜딩 페이지 총 개수
     */
    long countLandingPagesByProjectIdForSuperAdmin(
            @Param("projectId") Long projectId,
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword);
}
