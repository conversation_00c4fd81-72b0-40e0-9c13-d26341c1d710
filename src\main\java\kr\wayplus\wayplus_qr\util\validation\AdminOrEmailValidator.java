package kr.wayplus.wayplus_qr.util.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;
import java.util.regex.Pattern;

public class AdminOrEmailValidator implements ConstraintValidator<AdminOrEmail, String> {
    
    private static final String EMAIL_PATTERN = 
        "^[_A-Za-z0-9-\\+]+(\\.[_A-Za-z0-9-]+)*@" +
        "[A-Za-z0-9-]+(\\.[A-Za-z0-9]+)*(\\.[A-Za-z]{2,})$";
    private static final Pattern pattern = Pattern.compile(EMAIL_PATTERN);
    
    @Override
    public void initialize(AdminOrEmail constraintAnnotation) {
        // 초기화 로직이 필요한 경우 여기에 구현
    }
    
    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return false;
        }
        
        // "admin"인 경우 유효성 검사 통과
        if ("admin".equals(value)) {
            return true;
        }
        
        // 그 외의 경우 이메일 형식 검사
        return pattern.matcher(value).matches();
    }
}
