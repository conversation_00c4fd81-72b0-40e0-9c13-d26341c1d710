package kr.wayplus.wayplus_qr.controller;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import kr.wayplus.wayplus_qr.config.SearchTypeRegistry;
import kr.wayplus.wayplus_qr.dto.request.TeamRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.TeamResponseDto;
import kr.wayplus.wayplus_qr.dto.response.SearchTypeInfo;
import kr.wayplus.wayplus_qr.dto.response.AnswerTypeInfo;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.service.TeamService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/way/teams")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Teams", description = "팀 관리 API")
public class TeamController {
    
    private final TeamService teamService;
    private final SearchTypeRegistry searchTypeRegistry;
    
    /**
     * 팀 등록
     * 
     * @param requestDto 팀 등록 요청 데이터
     * @param userDetails 인증된 사용자 정보
     * @return 등록된 팀 정보
     */
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "팀 등록", description = "새로운 팀을 등록합니다.")
    public ResponseEntity<ApiResponseDto<TeamResponseDto>> createTeam(
            @Valid @ModelAttribute TeamRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {
        
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }
        
        String createUserEmail = userDetails.getUserEmail();
        TeamResponseDto response = teamService.createTeam(requestDto, createUserEmail);
        
        log.info("[TeamController] Successfully created team with ID: {}", response.getTeamId());
        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success(response));
    }
    
    /**
     * 팀 수정
     * 
     * @param teamId 수정할 팀 ID
     * @param requestDto 팀 수정 요청 데이터
     * @param updateUserEmail 수정자 이메일 (헤더에서 추출)
     * @return 수정된 팀 정보
     */
    @PutMapping(value = "/{teamId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    @Operation(summary = "팀 수정", description = "기존 팀 정보를 수정합니다.")
    public ResponseEntity<ApiResponseDto<TeamResponseDto>> updateTeam(
            @PathVariable Long teamId,
            @Valid @ModelAttribute TeamRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {
        
        log.info("[TeamController] PUT /api/teams/{} - Updating team: {}", teamId, requestDto.getTeamName());
        
        String updateUserEmail = userDetails.getUserEmail();
        TeamResponseDto response = teamService.updateTeam(teamId, requestDto, updateUserEmail);
        
        log.info("[TeamController] Successfully updated team ID: {}", teamId);
        return ResponseEntity.ok(ApiResponseDto.success(response));
    }
    
    /**
     * 팀 삭제 (논리 삭제)
     * 
     * @param teamId 삭제할 팀 ID
     * @param deleteUserEmail 삭제자 이메일 (헤더에서 추출)
     * @return 삭제 완료 응답
     */
    @DeleteMapping("/{teamId}")
    @Operation(summary = "팀 삭제", description = "팀을 논리적 삭제합니다.")
    public ResponseEntity<ApiResponseDto<Void>> deleteTeam(
            @PathVariable Long teamId,
            @AuthenticationPrincipal User userDetails) {
        log.info("[TeamController] DELETE /api/teams/{} - Deleting team", teamId);
        
        teamService.deleteTeam(teamId, userDetails.getUserEmail());
        
        log.info("[TeamController] Successfully deleted team ID: {}", teamId);
        return ResponseEntity.ok(ApiResponseDto.success(null));
    }
    
    /**
     * 팀 ID로 팀 조회
     * 
     * @param teamId 조회할 팀 ID
     * @return 팀 정보
     */
    @GetMapping("/{teamId}")
    @Operation(summary = "팀 조회", description = "팀 ID로 팀 정보를 조회합니다.")
    public ResponseEntity<ApiResponseDto<TeamResponseDto>> getTeamById(@PathVariable Long teamId,
            @RequestParam(value = "projectId", required = false) Long projectId,
            @AuthenticationPrincipal User userDetails) {

        log.info("[TeamController] GET /api/teams/{} - Getting team by ID", teamId);
        
        if (userDetails == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error("UNAUTHORIZED", "로그인이 필요합니다."));
        }

        TeamResponseDto response = teamService.getTeamById(teamId, userDetails.getUserEmail(), projectId);
        return ResponseEntity.ok(ApiResponseDto.success(response));
    }
    
    /**
     * 이벤트별 팀 목록 조회
     * 
     * @param eventId 이벤트 ID
     * @return 팀 목록
     */
    @GetMapping("/list")
    @Operation(summary = "이벤트별 팀 목록 조회", description = "특정 이벤트의 팀 목록을 조회합니다.")
    public ResponseEntity<ApiResponseDto<ListResponseDto<TeamResponseDto>>> getTeamsByEventId(
            @RequestParam(value = "projectId", required = false) Long projectId,
            @RequestParam(value = "eventId", required = false) Long eventId,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "createDate,desc") Pageable pageable,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        
        // 검색 타입 유효성 검사
        if (searchType != null && !searchTypeRegistry.isValidSearchType("team", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }
        
        // 사용 가능한 검색 타입 목록 조회
        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("team");

        // 서비스 호출하여 팀 목록 조회
        Page<TeamResponseDto> teamPage = teamService.getTeamsByProjectId(
                projectId, eventId, searchType, searchKeyword, userEmail, pageable);

        // 응답 생성
        ListResponseDto<TeamResponseDto> responseDto = new ListResponseDto<>(teamPage, availableSearchTypes);
        
        // answerSearchTypes 목록 추가
        List<AnswerTypeInfo> teamStatusList = List.of(
            new AnswerTypeInfo("ALL", "전체"),
            new AnswerTypeInfo("ACTIVE", "활성"),
            new AnswerTypeInfo("INACTIVE", "비활성"),
            new AnswerTypeInfo("PENDING", "대기중")
        );
        responseDto.setAnswerSearchTypes(teamStatusList);

        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }
}