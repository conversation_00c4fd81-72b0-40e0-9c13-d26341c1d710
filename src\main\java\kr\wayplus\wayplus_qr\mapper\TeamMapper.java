package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.entity.Team;
import kr.wayplus.wayplus_qr.dto.response.TeamResponseDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;


import java.util.List;
import java.util.Optional;

/**
 * Team Mapper
 * 
 * 팀 관련 데이터베이스 접근을 위한 매퍼 인터페이스
 * 
 * <AUTHOR> Name]
 */
@Mapper
public interface TeamMapper {
    
    /**
     * 팀 등록
     * 
     * @param team 등록할 팀 정보
     * @return 등록된 행 수
     */
    int insertTeam(Team team);
    
    /**
     * 팀 수정
     * 
     * @param team 수정할 팀 정보
     * @return 수정된 행 수
     */
    int updateTeam(Team team);
    
    /**
     * 팀 삭제 (논리 삭제)
     * 
     * @param teamId 삭제할 팀 ID
     * @param deleteUserEmail 삭제자 이메일
     * @return 삭제된 행 수
     */
    int deleteTeam(@Param("teamId") Long teamId, @Param("deleteUserEmail") String deleteUserEmail);
    
    /**
     * 팀 ID로 팀 조회
     * 
     * @param teamId 조회할 팀 ID
     * @return 팀 정보
     */
    Optional<Team> selectTeamById(@Param("teamId") Long teamId);
    
    /**
     * 이벤트별 팀 목록 조회
     * 
     * @param eventId 이벤트 ID
     * @return 팀 목록
     */
    List<TeamResponseDto> selectTeamsByEventId(@Param("eventId") Long eventId);
    
    /**
     * 모든 팀 목록 조회
     * 
     * @return 팀 목록
     */
    List<TeamResponseDto> selectAllTeams();
    
    /**
     * 이벤트 내에서 팀명으로 팀 검색
     * 
     * @param eventId 이벤트 ID
     * @param teamName 검색할 팀명 (부분 일치)
     * @return 검색된 팀 목록
     */
    List<TeamResponseDto> selectTeamsByEventIdAndName(@Param("eventId") Long eventId, @Param("teamName") String teamName);
    
    /**
     * 팀 리더 참가자 ID로 팀 검색
     * 
     * @param leaderAttendeeId 팀 리더 참가자 ID
     * @return 검색된 팀 목록
     */
    List<TeamResponseDto> selectTeamsByLeaderAttendeeId(@Param("leaderAttendeeId") Long leaderAttendeeId);
    
    /**
     * 이벤트 내에서 팀명 중복 체크
     * 
     * @param eventId 이벤트 ID
     * @param teamName 체크할 팀명
     * @param excludeTeamId 제외할 팀 ID (수정 시 자기 자신 제외)
     * @return 중복 개수
     */
    int countTeamsByEventIdAndName(@Param("eventId") Long eventId, @Param("teamName") String teamName, @Param("excludeTeamId") Long excludeTeamId);
    
    /**
     * 이벤트 내에서 팀 코드 중복 체크
     * 
     * @param eventId 이벤트 ID
     * @param teamCode 체크할 팀 코드
     * @param excludeTeamId 제외할 팀 ID (수정 시 자기 자신 제외)
     * @return 중복 개수
     */
    int countTeamsByEventIdAndCode(@Param("teamCode") String teamCode, @Param("excludeTeamId") Long excludeTeamId);
    
    /**
     * 필터링 조건에 따른 팀 개수 조회
     * 
     * @param eventId 이벤트 ID (선택)
     * @param teamType 팀 유형 (선택)
     * @param teamStatus 팀 상태 (선택)
     * @param searchType 검색 타입 (선택)
     * @param searchKeyword 검색 키워드 (선택)
     * @return 조건에 맞는 팀 개수
     */
    int countTeamsWithFilters(
            @Param("projectId") Long projectId,
            @Param("eventId") Long eventId,
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword);
    
    /**
     * 필터링 조건에 따른 팀 목록 조회 (페이징)
     * 
     * @param eventId 이벤트 ID (선택)
     * @param teamType 팀 유형 (선택)
     * @param teamStatus 팀 상태 (선택)
     * @param searchType 검색 타입 (선택)
     * @param searchKeyword 검색 키워드 (선택)
     * @param offset 페이징 오프셋
     * @param limit 페이징 제한
     * @return 조건에 맞는 팀 목록
     */
    List<TeamResponseDto> selectTeamsWithFilters(
            @Param("projectId") Long projectId,
            @Param("eventId") Long eventId,
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword,
            @Param("pageable") Pageable pageable);

}