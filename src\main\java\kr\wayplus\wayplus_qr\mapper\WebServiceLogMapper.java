package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.dto.response.UsageStatisticsResponseDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface WebServiceLogMapper {

    long selectTotalRequestsInPeriod(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("projectId") Long projectId);

    long selectAdminRequestsInPeriod(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("projectId") Long projectId);

    long selectGeneralUserRequestsInPeriod(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("projectId") Long projectId);

    long selectUniqueAdminUsersInPeriod(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("projectId") Long projectId);

    long selectUniqueAnonymousSessionsInPeriod(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("projectId") Long projectId);

    List<UsageStatisticsResponseDto.HourlyUsageDto> selectHourlyUsageInPeriod(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("projectId") Long projectId);

    List<UsageStatisticsResponseDto.TopUrlDto> selectTopUrlsInPeriod(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("limit") int limit, @Param("projectId") Long projectId);

    List<UsageStatisticsResponseDto.TopUserActivityDto> selectTopAdminUserActivityInPeriod(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("limit") int limit, @Param("projectId") Long projectId);

    List<UsageStatisticsResponseDto.TopUserActivityDto> selectTopAnonymousUserActivityInPeriod(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("limit") int limit, @Param("projectId") Long projectId);

    List<UsageStatisticsResponseDto.TopRefererDto> selectTopReferersInPeriod(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("limit") int limit, @Param("projectId") Long projectId);

    List<UsageStatisticsResponseDto.TopUserAgentDto> selectTopUserAgentsInPeriod(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate, @Param("limit") int limit, @Param("projectId") Long projectId);

}
