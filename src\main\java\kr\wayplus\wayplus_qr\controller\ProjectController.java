package kr.wayplus.wayplus_qr.controller;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import kr.wayplus.wayplus_qr.config.SearchTypeRegistry;
import kr.wayplus.wayplus_qr.dto.request.ProjectCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.ProjectUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ListResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ProjectResponseDto;
import kr.wayplus.wayplus_qr.dto.response.SearchTypeInfo;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.service.ProjectService;
import lombok.RequiredArgsConstructor;

@RestController
@RequestMapping("/api/way/projects")
@RequiredArgsConstructor
@Tag(name = "Project API", description = "프로젝트 관련 API")
@SecurityRequirement(name = "bearerAuth") // JWT 인증 필요 명시
public class ProjectController {

    private final ProjectService projectService;
    private final SearchTypeRegistry searchTypeRegistry;

    /**
     * 모든 프로젝트 목록 조회
     * @return ResponseEntity<ApiResponseDto<Page<ProjectResponseDto>>>
     */
    @GetMapping("/list")
    public ResponseEntity<ApiResponseDto<ListResponseDto<ProjectResponseDto>>> getAllProjects(
            @RequestParam(value = "status", required = false) String status,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "createDate,desc") Pageable pageable
    ) {
        if (searchType != null && !searchTypeRegistry.isValidSearchType("project", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }
        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("project");
        Page<ProjectResponseDto> projectPage = projectService.getAllProjects(status, searchType, searchKeyword, pageable);
        ListResponseDto<ProjectResponseDto> responseDto = new ListResponseDto<>(projectPage, availableSearchTypes);
        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    @PostMapping
    public ResponseEntity<ApiResponseDto<ProjectResponseDto>> createProject(
            @Valid @RequestBody ProjectCreateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails
    ) {
        String creatorEmail = userDetails.getUserEmail();
        ApiResponseDto<ProjectResponseDto> response = projectService.createProject(requestDto, creatorEmail);
        return ResponseEntity.status(HttpStatus.CREATED).body(response);
    }

    @GetMapping("/{projectId}")
    public ResponseEntity<ApiResponseDto<ProjectResponseDto>> getProjectById(@PathVariable("projectId") Long projectId) {
        ApiResponseDto<ProjectResponseDto> response = projectService.getProjectById(projectId);
        return ResponseEntity.ok(response);
    }

    /**
     * 프로젝트 정보 수정
     * @param projectId 수정할 프로젝트 ID
     * @param requestDto 수정할 정보 DTO
     * @param userDetails 요청 사용자 정보
     * @return ResponseEntity<ApiResponseDto<ProjectResponseDto>>
     */
    @PutMapping("/{projectId}")
    public ResponseEntity<ApiResponseDto<ProjectResponseDto>> updateProject(
            @PathVariable("projectId") Long projectId,
            @Valid @RequestBody ProjectUpdateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails
    ) {
        String updateUserEmail = userDetails.getUserEmail();
        ApiResponseDto<ProjectResponseDto> response = projectService.updateProject(projectId, requestDto, updateUserEmail);
        return ResponseEntity.ok(response);
    }

    // 프로젝트 삭제 (Soft Delete)
    @DeleteMapping("/{projectId}")
    public ResponseEntity<ApiResponseDto<Void>> deleteProject(
            @PathVariable("projectId") Long projectId,
            @AuthenticationPrincipal User userDetails
    ) {
        String updateUserEmail = userDetails.getUserEmail();
        ApiResponseDto<Void> response = projectService.deleteProject(projectId, updateUserEmail);
        return ResponseEntity.ok(response);
    }
}
