package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.dto.response.ProjectResponseDto;
import kr.wayplus.wayplus_qr.entity.Project;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@Mapper
public interface ProjectMapper {

    /**
     * 프로젝트 ID로 프로젝트 정보 조회 (삭제되지 않은 프로젝트만)
     * @param projectId 프로젝트 ID
     * @return Project Optional
     */
    Optional<Project> selectProjectById(@Param("projectId") Long projectId);

    /**
     * 모든 프로젝트 목록 조회 (삭제되지 않은 프로젝트만)
     * @return List<Project> // 반환 타입 변경: ProjectResponseDto -> Project
     */
    List<Project> selectAllProjects();

    /**
     * 페이징 처리된 프로젝트 목록 조회
     * @param status 프로젝트 상태
     * @param searchColumn 검색 컬럼
     * @param searchKeyword 검색 키워드
     * @param pageable 페이징 정보
     * @return List<Project>
     */
    List<Project> selectProjectListWithPaging(@Param("status") String status,
                                               @Param("searchColumn") String searchColumn,
                                               @Param("searchKeyword") String searchKeyword,
                                               @Param("pageable") Pageable pageable);

    /**
     * 전체 프로젝트 개수 조회 (페이징용)
     * @param status 프로젝트 상태
     * @param searchColumn 검색 컬럼
     * @param searchKeyword 검색 키워드
     * @return 전체 프로젝트 개수
     */
    int countAllProjects(@Param("status") String status,
                          @Param("searchColumn") String searchColumn,
                          @Param("searchKeyword") String searchKeyword);

    /**
     * 새로운 프로젝트 정보를 DB에 삽입
     * @param project Project 엔티티 객체
     * @return 삽입된 행 수
     */
    int insertProject(Project project);

    /**
     * 프로젝트 정보를 DB에 업데이트
     * @param project Project 엔티티 객체
     * @return 업데이트 된 행 수
     */
    int updateProject(Project project);

    /**
     * 프로젝트 ID로 프로젝트 정보 삭제 (Soft delete)
     * @param projectId 프로젝트 ID
     * @param updateUserEmail 삭제자 이메일
     * @return 삭제된 행 수
     */
    int deleteProjectById(@Param("projectId") Long projectId, @Param("updateUserEmail") String updateUserEmail);

    // 필요에 따라 추가 메소드 정의 (예: 사용자 이메일로 프로젝트 목록 조회 등)
    // List<ProjectResponseDto> selectProjectsByUserEmail(@Param("userEmail") String userEmail);

    /**
     * 주어진 이메일을 관리자로 가진 활성 프로젝트 수를 조회 (특정 프로젝트 제외 가능)
     * @param adminEmail 관리자 이메일
     * @param excludeProjectId 제외할 프로젝트 ID (수정 시 사용, 생성 시에는 null 또는 0L)
     * @return 해당 관리자가 담당하는 활성 프로젝트 수
     */
    int countActiveProjectsByAdminEmail(@Param("adminEmail") String adminEmail, @Param("excludeProjectId") Long excludeProjectId);

    ProjectResponseDto selectProjectByIdMap(@Param("projectId") Long projectId);

    // 프로젝트 이름으로 조회 (중복 검사용, 활성 프로젝트 대상)
    Optional<Project> selectProjectByName(@Param("projectName") String projectName);

    // 주어진 이메일을 관리자로 가진 활성 프로젝트 수를 조회 (특정 프로젝트 제외 가능)

    /**
     * 사용자가 특정 프로젝트의 관리자인지 확인합니다.
     * @param userEmail 사용자 이메일
     * @param projectId 프로젝트 ID
     * @param adminRoleIds 관리자 권한을 가진 역할 ID 목록
     * @return 관리자 여부 (true: 관리자, false: 관리자 아님)
     */
    boolean isUserAdminOfProject(@Param("userEmail") String userEmail, @Param("projectId") Long projectId, @Param("adminRoleIds") List<String> adminRoleIds);

    // SUPER_ADMIN: 모든 프로젝트 목록 조회 (페이징 및 동적 정렬)
    List<Project> selectAllProjectsForSuperAdmin(
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword,
            @Param("pageable") Pageable pageable);

    // SUPER_ADMIN: 모든 프로젝트 총 개수 조회
    long countAllProjectsForSuperAdmin(
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword);
}
