package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * Team Response DTO
 * 
 * 팀 응답 데이터 전송 객체
 * 
 * <AUTHOR> Name]
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeamResponseDto {
    
    private Long projectId;
    private Long teamId;
    private Long eventId;
    private String eventName;
    private String teamName;
    private String teamCode;
    private String description;
    private Integer maxMembers;
    private String teamStatus;
    private String profileImagePath;
    private Long joinQrCodeId;
    private Long leaderAttendeeId;
    private String leaderName;
    private String leaderPhone;
    private String useYn;
    private String deleteYn;
    private String createUserEmail;
    private LocalDateTime createDate;
    private String updateUserEmail;
    private LocalDateTime lastUpdateDate;
    private String deleteUserEmail;
    private LocalDateTime deleteDate;
}