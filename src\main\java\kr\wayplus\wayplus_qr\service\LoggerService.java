package kr.wayplus.wayplus_qr.service;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import kr.wayplus.wayplus_qr.entity.LogMailSend;
import kr.wayplus.wayplus_qr.mapper.LoggerMapper;

@Slf4j
@Service
@RequiredArgsConstructor
public class LoggerService {

    private final LoggerMapper loggerMapper;

    public int writeMailingLog(LogMailSend mailSend) {
        loggerMapper.insertLogMailSend(mailSend);
        return mailSend.getId();
    }

    public void updateMailingLogResult(LogMailSend mailSend){
        loggerMapper.updateMailingLogResult(mailSend);
    }
}
