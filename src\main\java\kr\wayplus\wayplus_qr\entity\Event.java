package kr.wayplus.wayplus_qr.entity;

import lombok.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Event {
    private Long eventId;
    private Long teamId;
    private Long projectId;
    private String eventName;
    private String teamName;
    private String teamCode;
    private String description;
    private LocalDateTime startDate;
    private LocalDateTime endDate;
    private String location;
    private Integer participantLimit;
    private Long preRegistrationFormId;
    private String preRegistrationFormName;
    private Long linkedQrCodeId;
    private String qrName;
    private Integer linkedQrCodeCount;
    private EventStatus status;
    private String eventImagePath;
    private String createUserEmail;
    private LocalDateTime createDate;
    private String updateUserEmail;
    private LocalDateTime lastUpdateDate;
    private String deleteUserEmail;
    private LocalDateTime deleteDate;
    private String useYn;
    private String deleteYn;
}
