package kr.wayplus.wayplus_qr.external;

import kr.wayplus.wayplus_qr.config.external.QrQuizMappingApiProperties;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
@RequiredArgsConstructor
public class quizServerConnecter {

    private final RestTemplate restTemplate;
    private final QrQuizMappingApiProperties properties;

    public Map<String, Object> requestQrQuizMapping(Long qrCodeId, Long quizId, String createId) {
        String url = properties.getBaseUrl() + properties.getEndpoints().getMapping();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set(properties.getHeaderName(), properties.getSecretKey());

        Map<String, Object> body = Map.of("qrCodeId", qrCodeId, "quizId", quizId,"createId",createId);

        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        try {
            @SuppressWarnings("unchecked")
            Map<String, Object> response = restTemplate.postForObject(url, requestEntity, Map.class);
            return response;
        } catch (RestClientException e) {
            e.printStackTrace();
            throw new QRcodeException(ErrorCode.THYMELEAF_SERVER_COMM_FAILED, e.getMessage());
        }
    }

    /**
     * 퀴즈와 QR 코드의 연결을 해제합니다.
     *
     * @param qrCodeId QR 코드 ID
     * @param quizId   퀴즈 ID
     * @return
     */
    public Map<String, Object> deleteQrQuizMapping(Long qrCodeId, Long quizId, String createId) {
        String url = properties.getBaseUrl() + properties.getEndpoints().getUnMapping();
        log.info("Request URL: {}", url);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(java.util.Collections.singletonList(MediaType.APPLICATION_JSON));
        headers.set(properties.getHeaderName(), properties.getSecretKey());
        log.info("Request Headers: {}", headers);

        Map<String, Object> body = new HashMap<>();
        body.put("qrCodeId", qrCodeId);
        body.put("quizId", quizId);
        body.put("createId", createId);
        log.info("Request Body: {}", body);

        HttpEntity<Map<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        try {
            ResponseEntity<Map<String, Object>> responseEntity = restTemplate.exchange(
                    url, HttpMethod.POST, requestEntity, new ParameterizedTypeReference<>() {}
            );

            log.info("Response Status: {}, Body: {}", responseEntity.getStatusCode(), responseEntity.getBody());

            if (responseEntity.getStatusCode().isError()) {
                log.error("Thymeleaf server returned an error. Status: {}, Body: {}", responseEntity.getStatusCode(), responseEntity.getBody());
                return responseEntity.getBody();
            }

            return responseEntity.getBody();
        } catch (RestClientException e) {
            log.error("Error during Thymeleaf qrQuizUnMapping request: {}", e.getMessage(), e);
            throw new QRcodeException(ErrorCode.THYMELEAF_SERVER_COMM_FAILED, e.getMessage());
        }
    }
}
