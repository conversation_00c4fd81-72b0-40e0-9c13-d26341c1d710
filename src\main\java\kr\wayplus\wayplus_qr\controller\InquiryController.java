package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.config.SearchTypeRegistry;
import kr.wayplus.wayplus_qr.dto.request.InquiryStatusUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.InquiryCommentResponseDto;
import kr.wayplus.wayplus_qr.dto.response.InquiryResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ListResponseDto;

import kr.wayplus.wayplus_qr.dto.response.SearchTypeInfo;
import kr.wayplus.wayplus_qr.dto.response.AnswerTypeInfo;
import kr.wayplus.wayplus_qr.entity.User;
import kr.wayplus.wayplus_qr.service.InquiryCommentService;
import kr.wayplus.wayplus_qr.service.InquiryService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

@RestController
@RequestMapping("/api/way/inquiries")
@RequiredArgsConstructor
@Slf4j
public class InquiryController {

    private final InquiryService inquiryService;
    private final InquiryCommentService inquiryCommentService;
    private final SearchTypeRegistry searchTypeRegistry;

    /**
     * 문의 생성 (첨부파일 포함)
     * @param requestDto 문의 생성 요청 DTO
     * @param attachments 첨부파일 목록 (선택)
     * @param userDetails 인증된 사용자 정보
     * @return 생성된 문의 정보
     * @throws IOException 
     */
    @PostMapping(value = "/save", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponseDto<InquiryResponseDto>> createInquiry(
            @RequestParam(value = "projectId") Long projectId,
            @RequestParam(value = "inquiryTitle") String inquiryTitle,
            @RequestParam(value = "inquiryContent") String inquiryContent,
            @RequestParam(value = "inquiryType") String inquiryType,
            @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments,
            @AuthenticationPrincipal User userDetails) throws IOException {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        log.info("Creating inquiry request received from user: {}", userEmail);

        InquiryResponseDto createdInquiry = inquiryService.createInquiry(projectId, inquiryTitle, inquiryContent
        , inquiryType, userEmail, attachments);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success(createdInquiry));
    }

    /**
     * 문의 목록 조회 (페이징, 필터링) - 모든 프로젝트의 문의를 반환합니다.
     * @param status 문의 상태 (선택)
     * @param inquiryType 문의 유형 (선택)
     * @param searchType 검색 타입 (선택)
     * @param searchKeyword 검색 키워드 (선택)
     * @param pageable 페이징 정보
     * @param userDetails 인증된 사용자 정보
     * @return 문의 목록 (페이징)
     */
    @GetMapping("/list")
    public ResponseEntity<ApiResponseDto<ListResponseDto<InquiryResponseDto>>> getInquiries(
            @RequestParam(value = "inquiryType", required = false) String inquiryType,
            @RequestParam(value = "inquiryStatus", required = false) String inquiryStatus,
            @RequestParam(value = "searchType", required = false) String searchType,
            @RequestParam(value = "searchKeyword", required = false) String searchKeyword,
            @PageableDefault(size = 10, sort = "createdAt,desc") Pageable pageable,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";
        
        // 검색 타입 유효성 검사
        if (searchType != null && !searchTypeRegistry.isValidSearchType("inquiry", searchType)) {
            return ResponseEntity.status(HttpStatus.BAD_REQUEST)
                    .body(ApiResponseDto.error("INVALID_SEARCH_TYPE", "지원하지 않는 검색 타입입니다: " + searchType));
        }
        
        // 사용 가능한 검색 타입 목록 조회
        List<SearchTypeInfo> availableSearchTypes = searchTypeRegistry.getSearchTypes("inquiry");

        // 서비스 호출하여 문의 목록 조회 - projectId는 null로 전달하여 모든 문의를 조회
        Page<InquiryResponseDto> inquiryPage = inquiryService.getInquiries(
                null, inquiryType, inquiryStatus, searchType, searchKeyword, userEmail, pageable);

        // 응답 생성
        ListResponseDto<InquiryResponseDto> responseDto = new ListResponseDto<>(inquiryPage, availableSearchTypes);
        
        // answerSearchTypes 목록 추가
        List<AnswerTypeInfo> inquiryStatusList = List.of(
            new AnswerTypeInfo("ALL", "전체"),
            new AnswerTypeInfo("PENDING", "접수됨"),
            new AnswerTypeInfo("PROCESSING", "처리중"),
            new AnswerTypeInfo("COMPLETED", "답변 완료")
        );
        responseDto.setAnswerSearchTypes(inquiryStatusList);

        return ResponseEntity.ok(ApiResponseDto.success(responseDto));
    }

    /**
     * 문의 상세 조회
     * @param inquiryId 문의 ID
     * @param userDetails 인증된 사용자 정보
     * @return 문의 상세 정보
     */
    @GetMapping("/{inquiryId}")
    public ResponseEntity<ApiResponseDto<InquiryResponseDto>> getInquiryById(
            @PathVariable("inquiryId") Long inquiryId,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        InquiryResponseDto inquiry = inquiryService.getInquiryById(inquiryId, userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(inquiry));
    }

    /**
     * 문의 내용 수정 (제목은 수정 불가)
     * @param inquiryId 문의 ID
     * @param requestDto 수정 요청 DTO
     * @param attachments 새로운 첨부파일 목록 (선택)
     * @param userDetails 인증된 사용자 정보
     * @return 수정된 문의 정보
     */
    @PutMapping(value = "/{inquiryId}/modify", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponseDto<InquiryResponseDto>> updateInquiry(
            @PathVariable("inquiryId") Long inquiryId,
            @RequestParam("inquiryContent") String inquiryContent,
            @RequestParam(value = "inquiryType", required = false) String inquiryType,
            @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        InquiryResponseDto updatedInquiry = inquiryService.updateInquiry(inquiryId, inquiryContent, inquiryType, attachments, userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(updatedInquiry));
    }

    /**
     * 문의 상태 변경 (SUPER_ADMIN만 가능)
     * @param inquiryId 문의 ID
     * @param requestDto 상태 변경 요청 DTO
     * @param userDetails 인증된 사용자 정보
     * @return 변경된 문의 정보
     */
    @PutMapping("/{inquiryId}/status")
    public ResponseEntity<ApiResponseDto<InquiryResponseDto>> updateInquiryStatus(
            @PathVariable("inquiryId") Long inquiryId,
            @RequestBody InquiryStatusUpdateRequestDto requestDto,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        InquiryResponseDto updatedInquiry = inquiryService.updateInquiryStatus(inquiryId, requestDto, userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(updatedInquiry));
    }

    /**
     * 문의 삭제 (SUPER_ADMIN 또는 작성자만 가능)
     * @param inquiryId 문의 ID
     * @param userDetails 인증된 사용자 정보
     * @return 성공 응답 (데이터 없음)
     */
    @DeleteMapping("/{inquiryId}")
    public ResponseEntity<ApiResponseDto<Void>> deleteInquiry(
            @PathVariable("inquiryId") Long inquiryId,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        inquiryService.deleteInquiry(inquiryId, userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(null));
    }

    /**
     * 문의 댓글 생성 (첨부파일 포함)
     * @param inquiryId 문의 ID
     * @param requestDto 댓글 생성 요청 DTO
     * @param attachments 첨부파일 목록 (선택)
     * @param userDetails 인증된 사용자 정보
     * @return 생성된 댓글 정보
     */
    @PostMapping(value = "/{inquiryId}/comments", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponseDto<InquiryCommentResponseDto>> createComment(
            @PathVariable("inquiryId") Long inquiryId,
            @RequestParam("commentContent") String commentContent,
            @RequestPart(value = "attachments", required = false) List<MultipartFile> attachments,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        InquiryCommentResponseDto createdComment = inquiryCommentService.createComment(
                inquiryId, commentContent, userEmail, attachments);

        return ResponseEntity.status(HttpStatus.CREATED)
                .body(ApiResponseDto.success(createdComment));
    }

    /**
     * 문의 댓글 수정 (첨부파일 포함)
     * @param inquiryId 문의 ID
     * @param commentId 댓글 ID
     * @param requestDto 댓글 수정 요청 DTO
     * @param attachments 새로 추가할 첨부파일 목록 (선택)
     * @param userDetails 인증된 사용자 정보
     * @return 수정된 댓글 정보
     */
    @PutMapping(value = "/{inquiryId}/comments/{commentId}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public ResponseEntity<ApiResponseDto<InquiryCommentResponseDto>> updateComment(
            @PathVariable("inquiryId") Long inquiryId,
            @PathVariable("commentId") Long commentId,
            @RequestParam("commentContent") String commentContent,
            @RequestParam(value = "deletedAttachmentIds", required = false) List<Long> deletedAttachmentIds,
            @RequestPart(value = "newAttachments", required = false) List<MultipartFile> newAttachments,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        InquiryCommentResponseDto updatedComment = inquiryCommentService.updateComment(
                commentId, commentContent, deletedAttachmentIds, userEmail, newAttachments);

        return ResponseEntity.ok(ApiResponseDto.success(updatedComment));
    }

    /**
     * 문의 댓글 삭제
     * @param inquiryId 문의 ID
     * @param commentId 댓글 ID
     * @param userDetails 인증된 사용자 정보
     * @return 성공 응답 (데이터 없음)
     */
    @DeleteMapping("/{inquiryId}/comments/{commentId}")
    public ResponseEntity<ApiResponseDto<Void>> deleteComment(
            @PathVariable("inquiryId") Long inquiryId,
            @PathVariable("commentId") Long commentId,
            @AuthenticationPrincipal User userDetails) {

        String userEmail = (userDetails != null) ? userDetails.getUserEmail() : "anonymousUser";

        inquiryCommentService.deleteComment(commentId, userEmail);

        return ResponseEntity.ok(ApiResponseDto.success(null));
    }
}
