package kr.wayplus.wayplus_qr.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 메뉴 기반 권한 체크를 위한 어노테이션
 * 
 * 사용 예시:
 * @RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = "READ")
 * @RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = "WRITE")
 * @RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = "UPDATE")
 * @RequireMenuPermission(menuCode = "QR_MANAGEMENT", permission = "DELETE")
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RequireMenuPermission {
    
    /**
     * 메뉴 코드 (manage_menus.menu_code)
     * 예: "QR_MANAGEMENT", "USER_MANAGEMENT", "MENU_MANAGEMENT"
     */
    String menuCode();
    
    /**
     * 필요한 권한 타입
     * READ: 읽기 권한 (can_read = 'Y')
     * WRITE: 쓰기(생성) 권한 (can_write = 'Y')
     * UPDATE: 수정 권한 (can_update = 'Y')
     * DELETE: 삭제 권한 (can_delete = 'Y')
     */
    PermissionType permission();
    
    /**
     * 권한 타입 열거형
     */
    enum PermissionType {
        READ("READ"),
        WRITE("WRITE"),
        UPDATE("UPDATE"),
        DELETE("DELETE");
        
        private final String value;
        
        PermissionType(String value) {
            this.value = value;
        }
        
        public String getValue() {
            return value;
        }
    }
}
