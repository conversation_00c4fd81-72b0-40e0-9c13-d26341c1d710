package kr.wayplus.wayplus_qr.exception;

import lombok.Getter;

@Getter
public class CustomPreRegistrationFormException extends RuntimeException {
    private final ErrorCode errorCode;

    public CustomPreRegistrationFormException(ErrorCode errorCode) {
        super(errorCode.getMessage());
        this.errorCode = errorCode;
    }

    public CustomPreRegistrationFormException(ErrorCode errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
    }
}
