package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.entity.Inquiry;
import kr.wayplus.wayplus_qr.entity.InquiryStatus;
import kr.wayplus.wayplus_qr.entity.enums.UserRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

@Mapper
public interface InquiryMapper {

    /**
     * 문의 생성
     * 
     * @param inquiry 생성할 문의 정보
     * @return 생성된 행 수
     */
    int insertInquiry(Inquiry inquiry);

    /**
     * ID로 문의 조회
     * 
     * @param inquiryId 문의 ID
     * @return 문의 정보 (Optional)
     */
    Optional<Inquiry> selectInquiryById(Long inquiryId);

    /**
     * 조건별 문의 목록 조회 (페이징)
     * 
     * @param projectId     프로젝트 ID (SUPER_ADMIN이 아닌 경우 필수)
     * @param status        문의 상태 (선택)
     * @param inquiryType   문의 유형 (선택)
     * @param searchColumn  검색 컬럼 (선택)
     * @param searchKeyword 검색 키워드 (선택)
     * @param pageable      페이징 정보
     * @return 문의 목록
     */
    List<Inquiry> selectInquiriesByCondition(
            @Param("projectId") Long projectId,
            @Param("inquiryType") String inquiryType,
            @Param("inquiryStatus") String inquiryStatus,
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword,
            @Param("pageable") Pageable pageable);

    /**
     * 조건별 문의 수 조회
     * 
     * @param projectId     프로젝트 ID (SUPER_ADMIN이 아닌 경우 필수)
     * @param status        문의 상태 (선택)
     * @param inquiryType   문의 유형 (선택)
     * @param searchColumn  검색 컬럼 (선택)
     * @param searchKeyword 검색 키워드 (선택)
     * @return 문의 수
     */
    long countInquiriesByCondition(
            @Param("projectId") Long projectId,
            @Param("inquiryType") String inquiryType,
            @Param("inquiryStatus") String inquiryStatus,
            @Param("searchColumn") String searchColumn,
            @Param("searchKeyword") String searchKeyword);

    /**
     * 문의 내용 수정
     * 
     * @param inquiryId      문의 ID
     * @param inquiryContent 수정할 문의 내용
     * @param inquiryType    수정할 문의 유형
     * @param userEmail      수정자 이메일
     * @return 수정된 행 수
     */
    int updateInquiryContent(
            @Param("inquiryId") Long inquiryId,
            @Param("inquiryContent") String inquiryContent,
            @Param("inquiryType") String inquiryType,
            @Param("userEmail") String userEmail);

    /**
     * 문의 상태 변경
     * 
     * @param inquiryId   문의 ID
     * @param status      변경할 상태
     * @param userEmail   변경자 이메일
     * @return 변경된 행 수
     */
    int updateInquiryStatus(
            @Param("inquiryId") Long inquiryId,
            @Param("inquiryStatus") InquiryStatus inquiryStatus,
            @Param("userEmail") String userEmail);

    /**
     * 문의 삭제 (논리적 삭제)
     * 
     * @param inquiryId 삭제할 문의 ID
     * @param deleteUserEmail 삭제한 사용자 이메일
     * @return 삭제된 행 수
     */
    int deleteInquiry(Long inquiryId, String deleteUserEmail);
}
