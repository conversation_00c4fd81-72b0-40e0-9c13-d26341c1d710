package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.request.InquiryCommentCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.InquiryCommentUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.AttachmentResponseDto;
import kr.wayplus.wayplus_qr.dto.response.InquiryCommentResponseDto;
import kr.wayplus.wayplus_qr.entity.Inquiry;
import kr.wayplus.wayplus_qr.entity.InquiryAttachment;
import kr.wayplus.wayplus_qr.entity.InquiryComment;
import kr.wayplus.wayplus_qr.entity.InquiryStatus;
import kr.wayplus.wayplus_qr.entity.NotificationType;
import kr.wayplus.wayplus_qr.entity.enums.UserRole;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import kr.wayplus.wayplus_qr.mapper.InquiryAttachmentMapper;
import kr.wayplus.wayplus_qr.mapper.InquiryCommentMapper;
import kr.wayplus.wayplus_qr.mapper.InquiryMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class InquiryCommentService {

    private final InquiryCommentMapper inquiryCommentMapper;
    private final InquiryMapper inquiryMapper;
    private final InquiryAttachmentMapper inquiryAttachmentMapper;
    private final UserMapper userMapper;
    private final FileStorageService fileStorageService;
    private final NotificationService notificationService;

    /**
     * 문의 댓글 생성
     * 
     * @param inquiryId    문의 ID
     * @param requestDto   댓글 생성 요청 DTO
     * @param userEmail    사용자 이메일
     * @param attachments  첨부파일 목록 (선택)
     * @return             생성된 댓글 정보
     */
    @Transactional
    public InquiryCommentResponseDto createComment(
            Long inquiryId,
            String commentContent,
            String userEmail,
            List<MultipartFile> attachments) {
        
        // 문의 조회
        Inquiry inquiry = inquiryMapper.selectInquiryById(inquiryId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_NOT_FOUND, "문의를 찾을 수 없습니다."));

        // 권한 확인 (SUPER_ADMIN 또는 문의 작성자만 댓글 작성 가능)
        boolean isSuperAdmin = isSuperAdmin(userEmail);
        boolean isAuthor = inquiry.getUserEmail().equals(userEmail);

        if (!(isSuperAdmin || isAuthor)) {
            throw new QRcodeException(ErrorCode.INQUIRY_ACCESS_DENIED, "댓글을 작성할 권한이 없습니다.");
        }

        // InquiryComment 엔티티 생성
        InquiryComment comment = InquiryComment.builder()
                .inquiryId(inquiryId)
                .userEmail(userEmail)
                .commentContent(commentContent)
                .build();

        // 댓글 저장
        int insertedCount = inquiryCommentMapper.insertComment(comment);
        if (insertedCount == 0 || comment.getCommentId() == null) {
            log.error("Failed to insert comment data or retrieve generated ID");
            throw new QRcodeException(ErrorCode.DATABASE_ERROR, "댓글 데이터 저장 또는 ID 생성에 실패했습니다.");
        }

        // SUPER_ADMIN이 댓글을 달면 상태를 PROCESSING으로 변경
        if (isSuperAdmin && inquiry.getInquiryStatus() == InquiryStatus.PENDING) {
            inquiryMapper.updateInquiryStatus(inquiryId, InquiryStatus.PROCESSING, userEmail);
        }

        // 첨부파일 처리
        List<AttachmentResponseDto> savedAttachments = processAttachments(null, comment.getCommentId(), attachments);

        // 알림 생성
        if (isSuperAdmin) {
            // SUPER_ADMIN이 댓글을 달면 문의 작성자에게 알림
            notificationService.createNotification(
                    inquiry.getUserEmail(),
                    inquiryId,
                    "문의에 답변이 등록되었습니다. 문의 제목 : " + inquiry.getInquiryTitle(),
                    NotificationType.NEW_INQUIRY_COMMENT
            );
        } else {
            // 문의 작성자가 댓글을 달면 SUPER_ADMIN에게 알림
            notificationService.createNotificationForSuperAdmins(
                    inquiryId,
                    "문의에 새 댓글이 등록되었습니다. 문의 제목 : " + inquiry.getInquiryTitle(),
                    NotificationType.NEW_INQUIRY_COMMENT
            );
        }

        // 생성된 댓글 정보와 첨부파일 정보를 포함하여 DTO 반환
        InquiryComment createdCommentWithDetails = inquiryCommentMapper.selectCommentById(comment.getCommentId())
                .orElseThrow(() -> new QRcodeException(ErrorCode.DATABASE_ERROR, "생성된 댓글 정보를 가져올 수 없습니다."));

        return InquiryCommentResponseDto.builder()
                .commentId(createdCommentWithDetails.getCommentId())
                .inquiryId(createdCommentWithDetails.getInquiryId())
                .userEmail(createdCommentWithDetails.getUserEmail())
                .commentContent(createdCommentWithDetails.getCommentContent())
                .createdAt(createdCommentWithDetails.getCreatedAt())
                // updatedAt 필드는 InquiryComment 엔티티에 존재하지 않으므로 제거
                .attachments(savedAttachments)
                .build();
    }

    /**
     * 댓글 삭제
     * 
     * @param commentId  댓글 ID
     * @param userEmail  사용자 이메일
     */
    @Transactional
    public void deleteComment(Long commentId, String userEmail) {
        // 댓글 조회
        InquiryComment comment = inquiryCommentMapper.selectCommentById(commentId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_COMMENT_NOT_FOUND, "댓글을 찾을 수 없습니다."));

        // 문의 조회
        Inquiry inquiry = inquiryMapper.selectInquiryById(comment.getInquiryId())
                .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_NOT_FOUND, "문의를 찾을 수 없습니다."));

        // 권한 확인 (SUPER_ADMIN, 댓글 작성자, 또는 문의 작성자만 삭제 가능)
        boolean isSuperAdmin = isSuperAdmin(userEmail);
        boolean isCommentAuthor = comment.getUserEmail().equals(userEmail);
        boolean isInquiryAuthor = inquiry.getUserEmail().equals(userEmail);

        if (!(isSuperAdmin || isCommentAuthor || isInquiryAuthor)) {
            throw new QRcodeException(ErrorCode.INQUIRY_ACCESS_DENIED, "댓글을 삭제할 권한이 없습니다.");
        }

        // 첨부파일 삭제 (파일 시스템과 DB)
        List<InquiryAttachment> attachments = inquiryAttachmentMapper.selectAttachmentsByCommentId(commentId);
        for (InquiryAttachment attachment : attachments) {
            try {
                fileStorageService.deleteFile(attachment.getStoredFilePath());
            } catch (Exception e) {
                log.warn("Failed to delete attachment file: {}", attachment.getStoredFilePath(), e);
            }
        }
        inquiryAttachmentMapper.deleteAttachmentsByCommentId(commentId);

        // 댓글 삭제
        int deletedCount = inquiryCommentMapper.deleteComment(commentId);
        if (deletedCount == 0) {
            throw new QRcodeException(ErrorCode.DATABASE_ERROR, "댓글 삭제에 실패했습니다.");
        }
    }

    /**
     * 첨부파일 처리 (저장)
     */
    private List<AttachmentResponseDto> processAttachments(
            Long inquiryId,
            Long commentId,
            List<MultipartFile> attachments) {
        
        if (attachments == null || attachments.isEmpty()) {
            return List.of();
        }

        return attachments.stream()
                .filter(file -> !file.isEmpty())
                .map(file -> {
                    // 파일 저장
                    String originalFileName = file.getOriginalFilename();
                    
                    String storedFilePath = fileStorageService.storeFilePath(file, "inquiry");

                    // DB에 첨부파일 정보 저장
                    InquiryAttachment attachment = InquiryAttachment.builder()
                            .inquiryId(inquiryId)
                            .commentId(commentId)
                            .originalFileName(originalFileName)
                            .storedFilePath(storedFilePath)
                            .fileSize(file.getSize())
                            .mimeType(file.getContentType())
                            .build();

                    inquiryAttachmentMapper.insertAttachment(attachment);

                    return AttachmentResponseDto.fromEntity(attachment);
                })
                .collect(Collectors.toList());
    }

    /**
     * 문의 댓글 수정
     * 
     * @param commentId       댓글 ID
     * @param requestDto      댓글 수정 요청 DTO
     * @param userEmail       사용자 이메일
     * @param newAttachments  새로 추가할 첨부파일 목록 (선택)
     * @return                수정된 댓글 정보
     */
    @Transactional
    public InquiryCommentResponseDto updateComment(
            Long commentId,
            String commentContent,
            List<Long> deletedAttachmentIds,
            String userEmail,
            List<MultipartFile> newAttachments) {
        
        // 댓글 조회
        InquiryComment comment = inquiryCommentMapper.selectCommentById(commentId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_COMMENT_NOT_FOUND, "댓글을 찾을 수 없습니다."));

        // 문의 조회
        Inquiry inquiry = inquiryMapper.selectInquiryById(comment.getInquiryId())
                .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_NOT_FOUND, "문의를 찾을 수 없습니다."));

        // 권한 확인 (SUPER_ADMIN 또는 댓글 작성자만 수정 가능)
        boolean isSuperAdmin = isSuperAdmin(userEmail);
        boolean isCommentAuthor = comment.getUserEmail().equals(userEmail);

        if (!(isSuperAdmin || isCommentAuthor)) {
            throw new QRcodeException(ErrorCode.INQUIRY_ACCESS_DENIED, "댓글을 수정할 권한이 없습니다.");
        }

        // 댓글 내용 업데이트
        comment.setCommentContent(commentContent);
        int updatedCount = inquiryCommentMapper.updateComment(comment);
        if (updatedCount == 0) {
            throw new QRcodeException(ErrorCode.DATABASE_ERROR, "댓글 내용 수정에 실패했습니다.");
        }

        // 기존 첨부파일 삭제
        if (deletedAttachmentIds != null && !deletedAttachmentIds.isEmpty()) {
            for (Long attachmentId : deletedAttachmentIds) {
                // 해당 첨부파일이 현재 댓글에 속하는지 확인
                InquiryAttachment attachment = inquiryAttachmentMapper.selectAttachmentByIdAndCommentId(attachmentId, commentId)
                        .orElse(null);
                        
                if (attachment != null) {
                    try {
                        // 파일 시스템에서 파일 삭제
                        fileStorageService.deleteFile(attachment.getStoredFilePath());
                    } catch (Exception e) {
                        log.warn("Failed to delete attachment file: {}", attachment.getStoredFilePath(), e);
                        // 파일 삭제 실패 시에도 DB 정보는 삭제 진행
                    }
                    // DB에서 첨부파일 정보 삭제
                    inquiryAttachmentMapper.deleteAttachmentById(attachmentId);
                }
            }
        }

        // 새 첨부파일 추가 (기존 processAttachments 메소드 사용)
        if (newAttachments != null && !newAttachments.isEmpty()) {
            processAttachments(null, commentId, newAttachments);
        }

        // 수정된 댓글 및 첨부파일 정보 조회
        InquiryComment updatedComment = inquiryCommentMapper.selectCommentById(commentId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.INQUIRY_COMMENT_NOT_FOUND, "수정된 댓글 정보를 가져올 수 없습니다."));

        List<InquiryAttachment> attachments = inquiryAttachmentMapper.selectAttachmentsByCommentId(commentId);
        List<AttachmentResponseDto> attachmentDtos = attachments.stream()
                .map(AttachmentResponseDto::fromEntity)
                .collect(Collectors.toList());

        // 응답 DTO 생성 및 반환
        return InquiryCommentResponseDto.builder()
                .commentId(updatedComment.getCommentId())
                .inquiryId(updatedComment.getInquiryId())
                .userEmail(updatedComment.getUserEmail())
                .commentContent(updatedComment.getCommentContent())
                .createdAt(updatedComment.getCreatedAt())
                .attachments(attachmentDtos)
                .build();
    }

    /**
     * 사용자가 SUPER_ADMIN 역할인지 확인
     */
    private boolean isSuperAdmin(String userEmail) {
        return userMapper.selectUserRoleByEmail(userEmail)
                .map(role -> role.equals(UserRole.SUPER_ADMIN.name()))
                .orElse(false);
    }
}
