package kr.wayplus.wayplus_qr.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * attendee_benefit_redemption 테이블 VO.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AttendeeBenefitRedemption {
    private Long redemptionId;
    private Long attendeeId;
    private Long benefitId;
    private Long eventId;
    private String eventName;
    private LocalDateTime redeemedAt;
    private String redeemedByUserEmail;
    private String memo;
}
