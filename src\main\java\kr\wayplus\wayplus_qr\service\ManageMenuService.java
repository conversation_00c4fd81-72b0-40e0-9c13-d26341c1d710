package kr.wayplus.wayplus_qr.service;

import kr.wayplus.wayplus_qr.dto.MenuDto;
import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.entity.MenuEntity;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.exception.QRcodeException;
import kr.wayplus.wayplus_qr.mapper.ManageMenuMapper;
import kr.wayplus.wayplus_qr.mapper.UserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ManageMenuService {

    private final ManageMenuMapper manageMenuMapper;
    private final UserMapper userMapper;

    /**
     * 메뉴를 생성합니다.
     */
    @Transactional
    public ApiResponseDto<Long> createMenu(MenuDto.CreateRequest requestDto, String createUserEmail) {
        log.info("Creating menu: {} by user: {}", requestDto.getMenuCode(), createUserEmail);

        // 메뉴 코드 중복 확인
        if (manageMenuMapper.existsByMenuCode(requestDto.getMenuCode(), null)) {
            throw new QRcodeException(ErrorCode.DUPLICATE_MENU_CODE);
        }

        // 메뉴 이름 중복 확인
        if (manageMenuMapper.existsByMenuName(requestDto.getMenuName(), null)) {
            throw new QRcodeException(ErrorCode.DUPLICATE_MENU_NAME);
        }

        // 메뉴 URL 중복 확인 (URL이 있는 경우에만)
        if (requestDto.getMenuUrl() != null && !requestDto.getMenuUrl().trim().isEmpty()) {
            if (manageMenuMapper.existsByMenuUrl(requestDto.getMenuUrl(), null)) {
                throw new QRcodeException(ErrorCode.DUPLICATE_MENU_URL);
            }
        }

        // 상위 메뉴 존재 확인
        if (requestDto.getParentMenuId() != null) {
            MenuEntity.Menu parentMenu = manageMenuMapper.selectMenuById(requestDto.getParentMenuId())
                    .orElseThrow(() -> new QRcodeException(ErrorCode.MENU_NOT_FOUND));
            if (!"ACTIVE".equals(parentMenu.getStatus())) {
                throw new QRcodeException(ErrorCode.PARENT_MENU_NOT_ACTIVE);
            }
        }

        // 표시 순서 자동 설정
        if (requestDto.getDisplayOrder() == null || requestDto.getDisplayOrder() == 0) {
            Integer maxOrder = manageMenuMapper.selectMaxDisplayOrder(requestDto.getParentMenuId(), requestDto.getMenuLevel());
            requestDto = MenuDto.CreateRequest.builder()
                    .parentMenuId(requestDto.getParentMenuId())
                    .menuCode(requestDto.getMenuCode())
                    .menuName(requestDto.getMenuName())
                    .menuUrl(requestDto.getMenuUrl())
                    .menuIcon(requestDto.getMenuIcon())
                    .menuLevel(requestDto.getMenuLevel())
                    .displayOrder(maxOrder + 1)
                    .status(requestDto.getStatus())
                    .build();
        }

        MenuEntity.Menu menu = requestDto.toEntity(createUserEmail);
        int insertedCount = manageMenuMapper.insertMenu(menu);

        if (insertedCount == 0 || menu.getMenuId() == null) {
            throw new QRcodeException(ErrorCode.MENU_CREATION_FAILED);
        }

        return ApiResponseDto.success(menu.getMenuId());
    }

    /**
     * 메뉴를 수정합니다.
     */
    @Transactional
    public ApiResponseDto<Void> updateMenu(Long menuId, MenuDto.UpdateRequest requestDto, String updateUserEmail) {
        log.info("Updating menu ID: {} by user: {}", menuId, updateUserEmail);

        MenuEntity.Menu existingMenu = manageMenuMapper.selectMenuById(menuId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.MENU_NOT_FOUND));

        // 메뉴 이름 중복 확인 (현재 메뉴 제외)
        if (manageMenuMapper.existsByMenuName(requestDto.getMenuName(), menuId)) {
            throw new QRcodeException(ErrorCode.DUPLICATE_MENU_NAME);
        }

        // 메뉴 URL 중복 확인 (URL이 있는 경우에만, 현재 메뉴 제외)
        if (requestDto.getMenuUrl() != null && !requestDto.getMenuUrl().trim().isEmpty()) {
            if (manageMenuMapper.existsByMenuUrl(requestDto.getMenuUrl(), menuId)) {
                throw new QRcodeException(ErrorCode.DUPLICATE_MENU_URL);
            }
        }

        // 상위 메뉴 변경 시 순환 참조 확인
        if (requestDto.getParentMenuId() != null && !requestDto.getParentMenuId().equals(existingMenu.getParentMenuId())) {
            if (requestDto.getParentMenuId().equals(menuId)) {
                throw new QRcodeException(ErrorCode.INVALID_PARENT_MENU);
            }
            if (isDescendantMenu(menuId, requestDto.getParentMenuId())) {
                throw new QRcodeException(ErrorCode.CIRCULAR_REFERENCE);
            }
        }

        MenuEntity.Menu updateMenu = MenuEntity.Menu.builder()
                .menuId(menuId)
                .parentMenuId(requestDto.getParentMenuId())
                .menuName(requestDto.getMenuName())
                .menuUrl(requestDto.getMenuUrl())
                .menuIcon(requestDto.getMenuIcon())
                .menuLevel(requestDto.getMenuLevel())
                .displayOrder(requestDto.getDisplayOrder())
                .status(requestDto.getStatus())
                .updateUserEmail(updateUserEmail)
                .lastUpdateDate(LocalDateTime.now())
                .build();

        int updatedCount = manageMenuMapper.updateMenu(updateMenu);
        if (updatedCount == 0) {
            throw new QRcodeException(ErrorCode.MENU_UPDATE_FAILED);
        }

        return ApiResponseDto.success(null);
    }

    /**
     * 메뉴를 삭제합니다.
     */
    @Transactional
    public ApiResponseDto<Void> deleteMenu(Long menuId, String deleteUserEmail) {
        log.info("Deleting menu ID: {} by user: {}", menuId, deleteUserEmail);

        manageMenuMapper.selectMenuById(menuId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.MENU_NOT_FOUND));

        if (manageMenuMapper.hasChildMenus(menuId)) {
            throw new QRcodeException(ErrorCode.MENU_HAS_CHILDREN);
        }

        int deletedCount = manageMenuMapper.deleteMenu(menuId, deleteUserEmail);
        if (deletedCount == 0) {
            throw new QRcodeException(ErrorCode.MENU_DELETE_FAILED);
        }

        // 관련 권한 정보도 삭제
        manageMenuMapper.deleteAllRolePermissionsByMenuId(menuId, deleteUserEmail);
        manageMenuMapper.deleteAllUserPermissionsByMenuId(menuId, deleteUserEmail);

        return ApiResponseDto.success(null);
    }

    /**
     * 메뉴 트리를 조회합니다.
     */
    @Transactional(readOnly = true)
    public ApiResponseDto<List<MenuDto.Response>> getMenuTree() {
        log.info("Getting menu tree");
        List<MenuDto.Response> allMenus = manageMenuMapper.selectAllMenus();
        List<MenuDto.Response> menuTree = buildMenuTree(allMenus);
        return ApiResponseDto.success(menuTree);
    }

    /**
     * 사용자가 접근 가능한 메뉴 트리를 조회합니다.
     */
    @Transactional(readOnly = true)
    public ApiResponseDto<List<MenuDto.Response>> getAccessibleMenuTree(String userEmail, String roleId) {
        log.info("Getting accessible menu tree for user: {} with role: {}", userEmail, roleId);
        List<MenuDto.Response> accessibleMenus = manageMenuMapper.selectAccessibleMenuTree(userEmail, roleId);
        List<MenuDto.Response> menuTree = buildMenuTree(accessibleMenus);
        return ApiResponseDto.success(menuTree);
    }

    /**
     * 역할별 메뉴 권한을 설정합니다.
     */
    @Transactional
    public ApiResponseDto<Void> setMenuRolePermission(Long menuId, String roleId, String isAccessible, String createUserEmail) {
        log.info("Setting menu role permission - Menu: {}, Role: {}, Accessible: {}", menuId, roleId, isAccessible);

        manageMenuMapper.selectMenuById(menuId)
                .orElseThrow(() -> new QRcodeException(ErrorCode.MENU_NOT_FOUND));

        MenuEntity.MenuRolePermission existingPermission = manageMenuMapper
                .selectRolePermissionByMenuIdAndRoleId(menuId, roleId).orElse(null);

        if (existingPermission != null) {
            // 기존 권한 수정
            MenuEntity.MenuRolePermission updatePermission = MenuEntity.MenuRolePermission.builder()
                    .permissionId(existingPermission.getPermissionId())
                    .isAccessible(isAccessible)
                    .updateUserEmail(createUserEmail)
                    .lastUpdateDate(LocalDateTime.now())
                    .build();
            manageMenuMapper.updateMenuRolePermission(updatePermission);
        } else {
            // 새 권한 생성
            MenuEntity.MenuRolePermission newPermission = MenuEntity.MenuRolePermission.builder()
                    .menuId(menuId)
                    .roleId(roleId)
                    .isAccessible(isAccessible)
                    .createUserEmail(createUserEmail)
                    .updateUserEmail(createUserEmail)
                    .createDate(LocalDateTime.now())
                    .lastUpdateDate(LocalDateTime.now())
                    .useYn("Y")
                    .deleteYn("N")
                    .build();
            manageMenuMapper.insertMenuRolePermission(newPermission);
        }

        return ApiResponseDto.success(null);
    }

    /**
     * 사용자별 메뉴 권한을 설정합니다.
     */
    @Transactional
    public ApiResponseDto<Void> setMenuUserPermission(MenuDto.UserPermissionRequest requestDto, String createUserEmail) {
        log.info("Setting menu user permission - Menu: {}, User: {}", requestDto.getMenuId(), requestDto.getUserEmail());

        manageMenuMapper.selectMenuById(requestDto.getMenuId())
                .orElseThrow(() -> new QRcodeException(ErrorCode.MENU_NOT_FOUND));

        userMapper.selectUserByEmail(requestDto.getUserEmail())
                .orElseThrow(() -> new QRcodeException(ErrorCode.USER_NOT_FOUND));

        MenuEntity.MenuUserPermission existingPermission = manageMenuMapper
                .selectUserPermissionByMenuIdAndUserEmail(requestDto.getMenuId(), requestDto.getUserEmail()).orElse(null);

        if (existingPermission != null) {
            // 기존 권한 수정
            MenuEntity.MenuUserPermission updatePermission = MenuEntity.MenuUserPermission.builder()
                    .userPermissionId(existingPermission.getUserPermissionId())
                    .isAccessible(requestDto.getIsAccessible())
                    .canRead(requestDto.getCanRead() != null ? requestDto.getCanRead() : "Y")
                    .canWrite(requestDto.getCanWrite() != null ? requestDto.getCanWrite() : "N")
                    .canUpdate(requestDto.getCanUpdate() != null ? requestDto.getCanUpdate() : "N")
                    .canDelete(requestDto.getCanDelete() != null ? requestDto.getCanDelete() : "N")
                    .permissionNote(requestDto.getPermissionNote())
                    .updateUserEmail(createUserEmail)
                    .lastUpdateDate(LocalDateTime.now())
                    .build();
            manageMenuMapper.updateMenuUserPermission(updatePermission);
        } else {
            // 새 권한 생성
            MenuEntity.MenuUserPermission newPermission = requestDto.toEntity(createUserEmail);
            manageMenuMapper.insertMenuUserPermission(newPermission);
        }

        return ApiResponseDto.success(null);
    }

    /**
     * 메뉴 접근 권한을 확인합니다.
     */
    @Transactional(readOnly = true)
    public boolean hasMenuAccess(Long menuId, String userEmail, String roleId) {
        // 개별 사용자 권한 확인 (우선순위 높음)
        if (manageMenuMapper.isAccessibleByUser(menuId, userEmail)) {
            return true;
        }
        // 역할별 권한 확인
        return manageMenuMapper.isAccessibleByRole(menuId, roleId);
    }

    /**
     * 하위 메뉴인지 확인 (순환 참조 방지용)
     */
    private boolean isDescendantMenu(Long parentMenuId, Long childMenuId) {
        List<MenuDto.Response> childMenus = manageMenuMapper.selectMenusByParentId(parentMenuId);
        for (MenuDto.Response childMenu : childMenus) {
            if (childMenu.getMenuId().equals(childMenuId)) {
                return true;
            }
            if (isDescendantMenu(childMenu.getMenuId(), childMenuId)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 평면 메뉴 목록을 계층형 트리 구조로 변환
     */
    private List<MenuDto.Response> buildMenuTree(List<MenuDto.Response> allMenus) {
        Map<Long, MenuDto.Response> menuMap = allMenus.stream()
                .collect(Collectors.toMap(MenuDto.Response::getMenuId, menu -> {
                    return MenuDto.Response.builder()
                            .menuId(menu.getMenuId())
                            .parentMenuId(menu.getParentMenuId())
                            .menuCode(menu.getMenuCode())
                            .menuName(menu.getMenuName())
                            .menuUrl(menu.getMenuUrl())
                            .menuIcon(menu.getMenuIcon())
                            .menuLevel(menu.getMenuLevel())
                            .displayOrder(menu.getDisplayOrder())
                            .status(menu.getStatus())
                            .createDate(menu.getCreateDate())
                            .accessible(menu.getAccessible())
                            .children(new ArrayList<>())
                            .build();
                }));

        List<MenuDto.Response> rootMenus = new ArrayList<>();

        for (MenuDto.Response menu : allMenus) {
            MenuDto.Response menuWithChildren = menuMap.get(menu.getMenuId());
            if (menu.getParentMenuId() == null) {
                rootMenus.add(menuWithChildren);
            } else {
                MenuDto.Response parent = menuMap.get(menu.getParentMenuId());
                if (parent != null) {
                    parent.getChildren().add(menuWithChildren);
                }
            }
        }

        return rootMenus;
    }

    // ========== 권한 관리 메서드 ==========

    /**
     * 특정 메뉴의 모든 사용자 권한을 조회합니다.
     */
    @Transactional(readOnly = true)
    public ApiResponseDto<List<MenuDto.PermissionResponse.UserPermission>> getMenuUserPermissions(Long menuId) {
        log.info("Getting user permissions for menu: {}", menuId);

        // 메뉴 존재 여부 확인
        java.util.Optional<MenuEntity.Menu> menu = manageMenuMapper.selectMenuById(menuId);
        if (menu.isEmpty()) {
            return ApiResponseDto.error("MENU_NOT_FOUND", "메뉴를 찾을 수 없습니다.");
        }

        List<MenuDto.PermissionResponse.UserPermission> userPermissions =
            manageMenuMapper.selectDetailedUserPermissionsByMenuId(menuId);

        return ApiResponseDto.success(userPermissions);
    }

    /**
     * 메뉴별 사용자 권한을 삭제합니다.
     */
    @Transactional
    public ApiResponseDto<String> deleteMenuUserPermission(Long menuId, String userEmail, String deleteUserEmail) {
        log.info("Deleting user permission for menu: {} and user: {}", menuId, userEmail);

        try {
            // 기존 권한 확인
            java.util.Optional<MenuEntity.MenuUserPermission> existingPermission =
                manageMenuMapper.selectUserPermissionByMenuIdAndUserEmail(menuId, userEmail);

            if (existingPermission.isEmpty()) {
                return ApiResponseDto.error("PERMISSION_NOT_FOUND", "삭제할 권한을 찾을 수 없습니다.");
            }

            manageMenuMapper.deleteMenuUserPermission(existingPermission.get().getUserPermissionId(), deleteUserEmail);
            log.info("Deleted user permission for menu: {} and user: {}", menuId, userEmail);

            return ApiResponseDto.success("권한이 성공적으로 삭제되었습니다.");
        } catch (Exception e) {
            log.error("Error deleting user permission for menu: {} and user: {}", menuId, userEmail, e);
            return ApiResponseDto.error("DELETE_ERROR", "권한 삭제 중 오류가 발생했습니다: " + e.getMessage());
        }
    }

    /**
     * 사용자의 특정 메뉴에 대한 권한을 체크합니다.
     */
    @Transactional(readOnly = true)
    public boolean hasMenuPermission(String menuCode, String userEmail, String permissionType) {
        log.debug("Checking permission for menu: {}, user: {}, permission: {}", menuCode, userEmail, permissionType);
        return manageMenuMapper.hasUserPermission(menuCode, userEmail, permissionType.toLowerCase());
    }
}
