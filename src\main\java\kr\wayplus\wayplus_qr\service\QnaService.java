package kr.wayplus.wayplus_qr.service;

import java.util.List;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import kr.wayplus.wayplus_qr.dto.request.QnaAnswerCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.QnaAnswerUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.QnaQuestionCreateRequestDto;
import kr.wayplus.wayplus_qr.dto.request.QnaQuestionUpdateRequestDto;
import kr.wayplus.wayplus_qr.dto.response.QnaAnswerResponseDto;
import kr.wayplus.wayplus_qr.dto.response.QnaQuestionResponseDto;
import kr.wayplus.wayplus_qr.exception.CustomException;
import kr.wayplus.wayplus_qr.exception.ErrorCode;
import kr.wayplus.wayplus_qr.mapper.QnaAnswerMapper;
import kr.wayplus.wayplus_qr.mapper.QnaQuestionMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Service
@RequiredArgsConstructor
@Slf4j
public class QnaService {

    private final QnaQuestionMapper qnaQuestionMapper;
    private final QnaAnswerMapper qnaAnswerMapper;

    /**
     * 질문 생성
     * 
     * @param requestDto 질문 생성 요청 DTO
     * @param userEmail 사용자 이메일
     * @return 생성된 질문 정보
     */
    @Transactional
    public QnaQuestionResponseDto createQuestion(QnaQuestionCreateRequestDto requestDto, String userEmail) {
        log.info("질문 생성 요청: {}, 사용자: {}", requestDto, userEmail);
        
        // 질문 등록
        qnaQuestionMapper.insertQuestion(
            requestDto.getProjectId(),
            requestDto.getTitle(),
            requestDto.getContent(),
            userEmail
        );
        
        // 등록된 질문 ID 조회
        Long questionId = qnaQuestionMapper.selectLastInsertId();
        
        // 등록된 질문 정보 조회 및 반환
        return qnaQuestionMapper.selectQuestionById(questionId);
    }
    
    /**
     * 질문 목록 조회 (페이징, 검색)
     * 
     * @param projectId 프로젝트 ID (필터링)
     * @param searchType 검색 유형 (title, content, createdBy)
     * @param searchKeyword 검색 키워드
     * @param pageable 페이징 정보
     * @return 질문 목록 (페이징)
     */
    @Transactional(readOnly = true)
    public Page<QnaQuestionResponseDto> getQuestions(
            String answerType,
            String searchType,
            String searchKeyword,
            Pageable pageable) {
        
        int offset = pageable.getPageNumber() * pageable.getPageSize();
        int limit = pageable.getPageSize();
        
        // 질문 목록 조회 (답변 정보 포함됨)
        List<QnaQuestionResponseDto> questions = qnaQuestionMapper.selectQuestionsList(
            answerType, searchKeyword, searchType, offset, limit);
                
        // 전체 질문 수 조회 (페이징용)
        int totalCount = qnaQuestionMapper.selectQuestionsCount(answerType, searchKeyword, searchType);
        
        return new PageImpl<>(questions, pageable, totalCount);
    }
    
    /**
     * 질문 상세 조회
     * 
     * @param questionId 질문 ID
     * @param userEmail 사용자 이메일
     * @return 질문 상세 정보 (답변 포함)
     */
    @Transactional
    public QnaQuestionResponseDto getQuestionById(Long questionId, String userEmail) {
        log.info("질문 상세 조회 요청: {}, 사용자: {}", questionId, userEmail);
        
        // 조회수 증가
        qnaQuestionMapper.updateViewCount(questionId);
        
        // 질문 조회 (답변 포함됨)
        return qnaQuestionMapper.selectQuestionById(questionId);
    }
    
    /**
     * 질문 수정
     * 
     * @param questionId 질문 ID
     * @param requestDto 질문 수정 요청 DTO
     * @param userEmail 사용자 이메일
     * @return 수정된 질문 정보
     */
    @Transactional
    public QnaQuestionResponseDto updateQuestion(
            Long questionId, 
            QnaQuestionUpdateRequestDto requestDto, 
            String userEmail) throws CustomException {
        log.info("질문 수정 요청: {}, 질문 ID: {}, 사용자: {}", requestDto, questionId, userEmail);
        
        // 기존 질문 정보 조회 (수정 권한 체크)
        QnaQuestionResponseDto question = qnaQuestionMapper.selectQuestionById(questionId);
        if (question == null) {
            throw new CustomException(ErrorCode.ENTITY_NOT_FOUND, "존재하지 않는 질문입니다: " + questionId);
        }
        
        // 작성자 체크 (본인 질문만 수정 가능)
        if (!question.getCreateUserEmail().equals(userEmail)) {
            throw new CustomException(ErrorCode.ACCESS_DENIED, "질문 수정 권한이 없습니다");
        }
        
        // 질문 수정
        qnaQuestionMapper.updateQuestion(
            questionId,
            requestDto.getTitle(),
            requestDto.getContent(),
            userEmail
        );
        
        // 수정된 질문 정보 조회 및 반환
        return qnaQuestionMapper.selectQuestionById(questionId);
    }
    
    /**
     * 질문 삭제
     * 
     * @param questionId 질문 ID
     * @param userEmail 사용자 이메일
     */
    @Transactional
    public void deleteQuestion(Long questionId, String userEmail) throws CustomException {
        log.info("질문 삭제 요청: {}, 사용자: {}", questionId, userEmail);
        
        // 기존 질문 정보 조회 (삭제 권한 체크)
        QnaQuestionResponseDto question = qnaQuestionMapper.selectQuestionById(questionId);
        if (question == null) {
            throw new CustomException(ErrorCode.ENTITY_NOT_FOUND, "존재하지 않는 질문입니다: " + questionId);
        }
        
        // 작성자 체크 (본인 질문만 삭제 가능)
        if (!question.getCreateUserEmail().equals(userEmail)) {
            throw new CustomException(ErrorCode.ACCESS_DENIED, "질문 삭제 권한이 없습니다");
        }
        
        // 질문 삭제
        qnaQuestionMapper.deleteQuestion(questionId, userEmail);
    }
    
    /**
     * 답변 생성
     * 
     * @param requestDto 답변 생성 요청 DTO
     * @param userEmail 사용자 이메일
     * @return 생성된 답변 정보
     */
    @Transactional
    public QnaAnswerResponseDto createAnswer(QnaAnswerCreateRequestDto requestDto, String userEmail) {
        log.info("답변 생성 요청: {}, 사용자: {}", requestDto, userEmail);
        
        // 질문 존재 여부 체크
        QnaQuestionResponseDto question = qnaQuestionMapper.selectQuestionById(requestDto.getQuestionId());
        if (question == null) {
            throw new IllegalArgumentException("존재하지 않는 질문입니다: " + requestDto.getQuestionId());
        }
        
        // 해당 질문에 이미 답변이 있는지 체크
        int answerCount = qnaAnswerMapper.countAnswerByQuestionId(requestDto.getQuestionId());
        if (answerCount > 0) {
            throw new IllegalArgumentException("이미 답변이 존재하는 질문입니다");
        }
        
        // 답변 등록
        qnaAnswerMapper.insertAnswer(
            requestDto.getQuestionId(),
            requestDto.getAnswerContent(),
            userEmail
        );
        
        // 등록된 답변 ID 조회
        Long answerId = qnaAnswerMapper.selectLastInsertId();
        
        // 등록된 답변 정보 조회 및 반환
        return qnaAnswerMapper.selectAnswerById(answerId);
    }
    
    /**
     * 답변 수정
     * 
     * @param answerId 답변 ID
     * @param requestDto 답변 수정 요청 DTO
     * @param userEmail 사용자 이메일
     * @return 수정된 답변 정보
     */
    @Transactional
    public QnaAnswerResponseDto updateAnswer(
            Long answerId, 
            QnaAnswerUpdateRequestDto requestDto, 
            String userEmail) {
        log.info("답변 수정 요청: {}, 답변 ID: {}, 사용자: {}", requestDto, answerId, userEmail);
        
        // 기존 답변 정보 조회 (수정 권한 체크)
        QnaAnswerResponseDto answer = qnaAnswerMapper.selectAnswerById(answerId);
        if (answer == null) {
            throw new IllegalArgumentException("존재하지 않는 답변입니다: " + answerId);
        }
        
        // 작성자 체크 (본인 답변만 수정 가능)
        if (!answer.getCreateUserEmail().equals(userEmail)) {
            throw new IllegalArgumentException("답변 수정 권한이 없습니다");
        }
        
        // 답변 수정
        qnaAnswerMapper.updateAnswer(
            answerId,
            requestDto.getAnswerContent(),
            userEmail
        );
        
        // 수정된 답변 정보 조회 및 반환
        return qnaAnswerMapper.selectAnswerById(answerId);
    }
    
    /**
     * 답변 삭제
     * 
     * @param answerId 답변 ID
     * @param userEmail 사용자 이메일
     */
    @Transactional
    public void deleteAnswer(Long answerId, String userEmail) {
        log.info("답변 삭제 요청: {}, 사용자: {}", answerId, userEmail);
        
        // 기존 답변 정보 조회 (삭제 권한 체크)
        QnaAnswerResponseDto answer = qnaAnswerMapper.selectAnswerById(answerId);
        if (answer == null) {
            throw new IllegalArgumentException("존재하지 않는 답변입니다: " + answerId);
        }
        
        // 작성자 체크 (본인 답변만 삭제 가능)
        if (!answer.getCreateUserEmail().equals(userEmail)) {
            throw new IllegalArgumentException("답변 삭제 권한이 없습니다");
        }
        
        // 답변 삭제
        qnaAnswerMapper.deleteAnswer(answerId, userEmail);
    }
}
