package kr.wayplus.wayplus_qr.controller;

import kr.wayplus.wayplus_qr.dto.response.ApiResponseDto;
import kr.wayplus.wayplus_qr.dto.response.ErrorDetail;
import kr.wayplus.wayplus_qr.dto.response.TokenResponseDto;
import kr.wayplus.wayplus_qr.dto.request.LoginRequestDto;
import kr.wayplus.wayplus_qr.dto.request.RefreshTokenRequestDto;
import kr.wayplus.wayplus_qr.dto.request.ChangePasswordRequestDto;
import kr.wayplus.wayplus_qr.service.AuthService;
import kr.wayplus.wayplus_qr.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseCookie;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/api/way/auth") // 기본 경로 설정
@RequiredArgsConstructor
public class AuthController {

    private final AuthService authService;
    private final UserService userService;

    @Value("${jwt.refresh-token-expiration-ms}")
    private Long refreshTokenExpirationMs;

    /**
     * 로그인 엔드포인트
     * @param loginRequestDto 로그인 요청 DTO (이메일, 비밀번호)
     * @return 성공 시 토큰 정보, 실패 시 GlobalExceptionHandler가 처리
     */
    @PostMapping("/login")
    public ResponseEntity<ApiResponseDto<Map<String, Object>>> login(@Valid @RequestBody LoginRequestDto loginRequestDto) {
        log.info("Login attempt for user: {}", loginRequestDto.getUserEmail());
        TokenResponseDto tokenResponseDto = authService.login(loginRequestDto);

        // 1. Refresh Token을 HttpOnly 쿠키로 설정
        ResponseCookie refreshTokenCookie = ResponseCookie.from("refreshToken", tokenResponseDto.getRefreshToken())
                .httpOnly(true)
                .secure(false) // HTTPS 환경에서만 전송 (배포 시 true, 로컬 개발 시 false 고려)
                .path("/") // 쿠키가 유효한 경로 설정 (예: /api/way/auth/refresh)
                .maxAge(TimeUnit.MILLISECONDS.toSeconds(refreshTokenExpirationMs)) // 쿠키 만료 시간 (초 단위)
                // .domain("example.com") // 필요한 경우 도메인 설정
                .sameSite("Strict") // CSRF 방지를 위해 SameSite 설정 (None, Lax, Strict)
                                 // SameSite=None 사용 시 Secure=true 필수
                .build();

        // 2. 응답 본문에는 Access Token 정보만 포함
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("accessToken", tokenResponseDto.getAccessToken());
        responseData.put("tokenType", tokenResponseDto.getTokenType());
        responseData.put("expiresIn", tokenResponseDto.getExpiresIn());

        log.info("Login successful for user: {}, setting refresh token cookie.", loginRequestDto.getUserEmail());

        // 3. 응답 헤더에 Set-Cookie 추가하여 ResponseEntity 반환
        return ResponseEntity.ok()
                .header(HttpHeaders.SET_COOKIE, refreshTokenCookie.toString())
                .body(ApiResponseDto.success(responseData));
    }

    /**
     * 토큰 갱신 엔드포인트
     * @param request HttpServletRequest - 쿠키에서 리프레시 토큰을 추출하기 위해 사용
     * @return 성공 시 새로운 토큰 정보, 실패 시 GlobalExceptionHandler가 처리
     */
    @PostMapping("/refresh")
    public ResponseEntity<ApiResponseDto<?>> refreshToken(HttpServletRequest request) {
        // 쿠키에서 리프레시 토큰 추출
        String refreshToken = null;
        jakarta.servlet.http.Cookie[] cookies = request.getCookies();
        
        if (cookies != null) {
            for (jakarta.servlet.http.Cookie cookie : cookies) {
                if ("refreshToken".equals(cookie.getName())) {
                    refreshToken = cookie.getValue();
                    break;
                }
            }
        }
        
        if (refreshToken == null) {
            log.warn("Refresh token not found in cookies");
            ErrorDetail errorDetail = ErrorDetail.builder()
                    .code("AUTHENTICATION_FAILED")
                    .message("로그인 정보가 없습니다. 다시 로그인해주세요.")
                    .build();
            return ResponseEntity.status(org.springframework.http.HttpStatus.UNAUTHORIZED)
                    .body(ApiResponseDto.error(errorDetail));
        }
        
        // 리프레시 토큰 DTO 생성
        RefreshTokenRequestDto refreshTokenRequestDto = new RefreshTokenRequestDto(refreshToken);
        
        // 토큰 갱신 서비스 호출
        TokenResponseDto tokenResponse = authService.refreshToken(refreshTokenRequestDto);
        
        // 새로운 리프레시 토큰을 쿠키에 저장
        ResponseCookie refreshTokenCookie = ResponseCookie.from("refreshToken", tokenResponse.getRefreshToken())
                .httpOnly(true)
                .secure(false) // HTTPS 환경에서만 전송 (배포 시 true, 로컬 개발 시 false 고려)
                .path("/") // 쿠키가 유효한 경로 설정
                .maxAge(TimeUnit.MILLISECONDS.toSeconds(refreshTokenExpirationMs)) // 쿠키 만료 시간 (초 단위)
                .sameSite("Strict") // CSRF 방지를 위해 SameSite 설정
                .build();
        
        // 응답 본문에는 Access Token 정보만 포함
        Map<String, Object> responseData = new HashMap<>();
        responseData.put("accessToken", tokenResponse.getAccessToken());
        responseData.put("tokenType", tokenResponse.getTokenType());
        responseData.put("expiresIn", tokenResponse.getExpiresIn());
        
        log.info("Token refresh successful");
        
        return ResponseEntity.ok()
                .header(HttpHeaders.SET_COOKIE, refreshTokenCookie.toString())
                .body(ApiResponseDto.success(responseData));
    }

    /**
     * 로그아웃 엔드포인트
     * @param authentication 현재 인증된 사용자 정보
     * @param request HttpServletRequest (AuthService.logout에서 필요 시 사용)
     * @return 성공 시 200 OK (데이터 없음), 실패 시 GlobalExceptionHandler가 처리
     */
    @PostMapping("/logout")
    public ResponseEntity<ApiResponseDto<?>> logout(Authentication authentication, HttpServletRequest request) {
        Object principal = authentication.getPrincipal();
        String userEmail = null;
        if (principal instanceof UserDetails) {
            userEmail = ((UserDetails) principal).getUsername();
        } else if (principal instanceof String) {
            userEmail = (String) principal;
        }

        if (userEmail != null) {
            authService.logout(userEmail);
            log.info("Logout successful for user: {}", userEmail);

            // 쿠키 삭제를 위한 ResponseCookie 생성
            ResponseCookie deleteCookie = ResponseCookie.from("refreshToken", "") // 값은 비워도 무방
                    .maxAge(0) // 만료 시간을 0으로 설정하여 즉시 삭제
                    .path("/") // 삭제할 쿠키와 동일한 경로 설정
                    .httpOnly(true)
                    .secure(false) // login/refresh와 동일하게 설정 (배포 시 true)
                    .sameSite("Strict") // login/refresh와 동일하게 설정
                    .build();

            // 응답 헤더에 Set-Cookie 추가하여 반환
            return ResponseEntity.ok()
                    .header(HttpHeaders.SET_COOKIE, deleteCookie.toString())
                    .body(ApiResponseDto.success(null));
        } else {
            log.error("Could not extract user email from principal for logout: {}", principal);
            ErrorDetail errorDetail = ErrorDetail.builder()
                .code("INTERNAL_SERVER_ERROR")
                .message("사용자 정보를 가져올 수 없습니다.")
                .build();
            return ResponseEntity.internalServerError().body(ApiResponseDto.error(errorDetail));
        }
    }

    /**
     * 최초 로그인 사용자의 비밀번호를 변경합니다.
     *
     * @param requestDto 비밀번호 변경 요청 데이터 (userEmail, currentPassword, newPassword)
     * @return 성공 응답 (데이터 없음)
     */
    @PostMapping("/change-initial-password")
    public ResponseEntity<ApiResponseDto<Void>> changeInitialPassword(@Valid @RequestBody ChangePasswordRequestDto requestDto) {
        log.info("Received request to change initial password for user: {}", requestDto.getUserEmail());
        // 서비스 호출하여 비밀번호 변경 로직 수행
        userService.changeInitialPassword(requestDto);

        // 비밀번호 변경 성공 시 표준 응답 반환
        return ResponseEntity.ok(ApiResponseDto.success(null));
    }
}
