package kr.wayplus.wayplus_qr.entity;

import lombok.*;

import java.time.LocalDateTime;

@Getter
@Setter // Mapper에서 결과를 매핑하거나, Service에서 값을 설정할 때 필요할 수 있음
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Project {

    private Long projectId;             // project_id (PK, auto_increment)
    private String projectName;         // project_name
    private String description;         // description
    private String projectAdminUserEmail; // project_admin_user_email
    private String status;              // status (e.g., ACTIVE, INACTIVE) - Enum 사용 고려
    private String createUserEmail;     // create_user_email
    private LocalDateTime createDate;   // create_date
    private String updateUserEmail;     // update_user_email
    private LocalDateTime lastUpdateDate; // last_update_date
    private String deleteYn;            // delete_yn ('Y' or 'N')

    // == 편의 메소드 == //
    // 필요에 따라 엔티티 관련 로직 추가 가능
    // 예: 상태 변경 메소드 등
    // public void changeStatus(String newStatus) {
    //     this.status = newStatus;
    //     this.lastUpdateDate = LocalDateTime.now();
    //     // this.updateUserEmail = ... (변경 사용자 정보 설정 필요)
    // }
}
