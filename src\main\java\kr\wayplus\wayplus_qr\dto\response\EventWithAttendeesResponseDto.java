package kr.wayplus.wayplus_qr.dto.response;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.springframework.data.domain.Page;
import kr.wayplus.wayplus_qr.entity.Event;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Getter
@Setter
@Builder
public class EventWithAttendeesResponseDto {
    private EventResponseDto eventDetails; // 기존 이벤트 응답 DTO 재사용
    private Page<AttendeeSummaryDto> attendees; // 참석자 목록 (페이지 정보 포함)
}
