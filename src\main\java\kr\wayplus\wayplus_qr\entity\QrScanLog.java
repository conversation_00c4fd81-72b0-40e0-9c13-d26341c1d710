package kr.wayplus.wayplus_qr.entity;

import lombok.*;
import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QrScanLog {
    private Long logId;             // 로그 고유 번호 (PK)
    private Long qrCodeId;          // 스캔된 QR 코드 ID (FK)
    private LocalDateTime scanTime;    // 스캔 시각
    private String ipAddress;       // 스캐너 IP 주소
    private String userAgent;       // 스캐너 사용자 에이전트
    private String locationData;    // 위치 정보 (JSON)
    private String scannerUserEmail;// 스캔한 사용자 email (로그인 시)
    private Boolean isUniqueScan;   // 유니크 스캔 여부 (현재는 기본값 true, 추후 로직 구현)
}
