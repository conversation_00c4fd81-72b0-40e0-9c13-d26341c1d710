<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
  PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.PreRegistrationFormFieldMapper">

  <resultMap id="FieldResultMap" type="kr.wayplus.wayplus_qr.entity.PreRegistrationFormField">
    <id property="fieldId" column="field_id"/>
    <result property="formId" column="form_id"/>
    <result property="fieldLabel" column="field_label"/>
    <result property="fieldName" column="field_name"/>
    <result property="fieldType" column="field_type"/>
    <result property="isRequiredYn" column="is_required_yn"/>
    <result property="helpText" column="help_text"/>
    <result property="options" column="options"/>
    <result property="displayOrder" column="display_order"/>
    <result property="createUserEmail" column="create_user_email"/>
    <result property="createDate" column="create_date"/>
    <result property="updateUserEmail" column="update_user_email"/>
    <result property="lastUpdateDate" column="last_update_date"/>
    <result property="deleteUserEmail" column="delete_user_email"/>
    <result property="deleteDate" column="delete_date"/>
    <result property="useYn" column="use_yn"/>
    <result property="deleteYn" column="delete_yn"/>
  </resultMap>

  <insert id="insertField" useGeneratedKeys="true" keyProperty="fieldId">
    INSERT INTO form_fields
      (form_id, field_label, field_name, field_type, is_required_yn, help_text, options, display_order, create_user_email, create_date, use_yn, delete_yn)
    VALUES
      (#{formId}, #{fieldLabel}, #{fieldName}, #{fieldType}, #{isRequiredYn}, #{helpText}, #{options}, #{displayOrder}, #{createUserEmail}, NOW(), 'Y', 'N')
  </insert>

  <select id="selectFieldsByFormId" resultMap="FieldResultMap">
    SELECT * FROM form_fields
    WHERE form_id = #{formId}
      AND delete_yn = 'N'
    ORDER BY display_order ASC
  </select>

  <select id="selectAllFieldsByFormId" resultMap="FieldResultMap">
    SELECT *
    FROM form_fields
    WHERE form_id = #{formId}
    ORDER BY display_order ASC
  </select>

  <!-- submissionData 변환을 위해 삭제된 필드 포함하여 모든 필드의 이름과 라벨 조회 -->
  <select id="selectFieldNameAndLabelByFormIdIncludingDeleted" resultType="java.util.Map">
    SELECT
      field_name AS fieldName,
      field_label AS fieldLabel
    FROM form_fields
    WHERE form_id = #{formId}
    ORDER BY display_order ASC
  </select>

  <!-- Update: mark existing fields as deleted on update -->
  <update id="deleteFieldsByFormId">
    UPDATE form_fields
    SET delete_yn = 'Y',
        use_yn = 'N',
        delete_user_email = #{userEmail},
        delete_date = NOW()
    WHERE form_id = #{formId}
      AND delete_yn = 'N'
  </update>

</mapper>
