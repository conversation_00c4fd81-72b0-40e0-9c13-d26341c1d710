package kr.wayplus.wayplus_qr.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "프로젝트 정보 응답 DTO")
public class ProjectResponseDto {

    @Schema(description = "프로젝트 ID", example = "1")
    private Long projectId;

    @Schema(description = "프로젝트 이름", example = "웨이플러스 QR코드")
    private String projectName;

    @Schema(description = "프로젝트 설명", example = "웨이플러스 QR코드 관리용 프로젝트")
    private String description;

    @Schema(description = "프로젝트 관리자 이메일", example = "<EMAIL>")
    private String projectAdminUserEmail;

    @Schema(description = "프로젝트 상태 (ACTIVE, INACTIVE)", example = "ACTIVE")
    private String status; // Enum은 문자열로 처리

    @Schema(description = "생성자 이메일", example = "<EMAIL>")
    private String createUserEmail;

    @Schema(description = "생성 일시 (YYYY-MM-DD HH:mm:ss)", example = "2025-04-01 10:00:00")
    private String createDate; // 'YYYY-MM-DD HH:mm:ss' 형식

    @Schema(description = "수정자 이메일", example = "<EMAIL>")
    private String updateUserEmail;

    @Schema(description = "최종 수정 일시 (YYYY-MM-DD HH:mm:ss)", example = "2025-04-08 14:30:00")
    private String lastUpdateDate; // 'YYYY-MM-DD HH:mm:ss' 형식

    // 필요하다면 엔티티 -> DTO 변환 메소드 추가
    // public static ProjectResponseDto fromEntity(Project project) { ... }
}
