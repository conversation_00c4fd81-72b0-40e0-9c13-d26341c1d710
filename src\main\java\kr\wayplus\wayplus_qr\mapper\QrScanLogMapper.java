package kr.wayplus.wayplus_qr.mapper;

import kr.wayplus.wayplus_qr.entity.QrScanLog;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

@Mapper
public interface QrScanLogMapper {
    /**
     * QR 코드 스캔 로그를 저장합니다.
     * @param qrScanLog 저장할 스캔 로그 정보
     * @return 삽입된 행 수
     */
    int insertQrScanLog(QrScanLog qrScanLog);
    
    /**
     * 특정 QR 코드의 주어진 기간 내 스캔 횟수를 조회합니다.
     * @param qrCodeId 조회할 QR 코드 ID
     * @param startDateTime 시작 일시
     * @param endDateTime 종료 일시
     * @return 스캔 횟수
     */
    long countScansByQrIdAndDateRange(
            @Param("qrCodeId") Long qrCodeId,
            @Param("startDateTime") LocalDateTime startDateTime,
            @Param("endDateTime") LocalDateTime endDateTime);

    /**
     * 특정 QR 코드의 주어진 기간 내 시간대별 Top 5 스캔 횟수를 조회합니다.
     * @param qrCodeId 조회할 QR 코드 ID
     * @param startDateTime 시작 일시
     * @param endDateTime 종료 일시
     * @return 시간대별 스캔 횟수 정보
     */
    List<Map<String, Object>> selectTopScanHoursByQrIdAndDateRange(
            @Param("qrCodeId") Long qrCodeId, 
            @Param("startDateTime") LocalDateTime startDateTime,
            @Param("endDateTime") LocalDateTime endDateTime);

    /**
     * 특정 QR 코드의 주어진 기간 내 스캔한 기기 OS 통계를 조회합니다.
     * @param qrCodeId 조회할 QR 코드 ID
     * @param startDateTime 시작 일시
     * @param endDateTime 종료 일시
     * @return OS별 스캔 횟수 정보
     */
    List<Map<String, Object>> selectDeviceOsStatsByQrIdAndDateRange(
            @Param("qrCodeId") Long qrCodeId,
            @Param("startDateTime") LocalDateTime startDateTime,
            @Param("endDateTime") LocalDateTime endDateTime);
            
    /**
     * 특정 QR 코드의 주어진 기간 내 일자별 스캔 횟수를 조회합니다.
     * @param qrCodeId 조회할 QR 코드 ID
     * @param startDateTime 시작 일시
     * @param endDateTime 종료 일시
     * @return 일자별 스캔 횟수 정보
     */
    List<Map<String, Object>> selectDailyScanCountsByQrIdAndDateRange(
            @Param("qrCodeId") Long qrCodeId,
            @Param("startDateTime") LocalDateTime startDateTime,
            @Param("endDateTime") LocalDateTime endDateTime);
}
