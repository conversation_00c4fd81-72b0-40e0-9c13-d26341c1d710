<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR 코드 관리자 시스템 요구사항 인포그래픽</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f4f7f9;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: auto;
            background-color: #fff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 20px;
        }
        h1 {
             border-bottom: 3px solid #3498db;
             padding-bottom: 10px;
             margin-bottom: 30px;
        }
        h2 {
            color: #3498db;
            margin-top: 40px;
            border-bottom: 1px dashed #bdc3c7;
            padding-bottom: 8px;
        }
        h3 {
            color: #16a085;
            margin-top: 25px;
            margin-bottom: 15px;
            text-align: left;
            font-size: 1.2em;
        }
        .section {
            margin-bottom: 40px;
            padding: 20px;
            background-color: #ecf0f1;
            border-radius: 5px;
        }
        .role-list, .feature-list {
            list-style: none;
            padding: 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        .role-list li, .feature-list li {
            background-color: #fff;
            padding: 15px;
            border-radius: 5px;
            border-left: 5px solid #3498db;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
            transition: transform 0.2s ease;
        }
        .role-list li:hover, .feature-list li:hover {
             transform: translateY(-3px);
        }
        .role-list strong, .feature-list strong {
            color: #2980b9;
            display: block;
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        .role-list span { /* Icon placeholder */
            display: inline-block;
            margin-right: 8px;
            font-size: 1.2em;
            color: #3498db;
        }
        .feature-list strong span { /* Icon placeholder */
             display: inline-block;
            margin-right: 8px;
            font-size: 1.1em;
        }

        .common-features ul {
            list-style: none;
            padding: 0;
        }
         .common-features li {
            background-color: #e8f6fd;
            padding: 10px 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            border-left: 3px solid #1abc9c;
        }
         .common-features li::before {
            content: "✓"; /* Checkmark */
            color: #1abc9c;
            font-weight: bold;
            margin-right: 10px;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .role-list, .feature-list {
                grid-template-columns: 1fr; /* Stack vertically on smaller screens */
            }
            h1 { font-size: 1.8em; }
            h2 { font-size: 1.5em; }
        }

    </style>
</head>
<body>
    <div class="container">
        <h1>📊 QR 코드 관리자 시스템 요구사항: 한눈에 보기</h1>

        <!-- <p style="text-align: center; font-size: 1.1em; color: #555; margin-bottom: 30px;">
            QR 코드의 생성부터 관리, 통계 분석, 이벤트 연동까지! <br>
            다양한 기능을 통합적으로 제공하는 시스템의 핵심 요구사항을 요약했습니다.
        </p> -->

        <!-- 역할 정의 -->
        <div class="section">
            <h2>👥 시스템 역할 (Actors & Roles)</h2>
            <ul class="role-list">
                <li><span>👑</span><strong>수퍼 관리자 (Super Admin):</strong> 시스템 전체 총괄 관리 (모든 기능 접근)</li>
                <li><span> M </span><strong>프로젝트 관리자 (Project Admin):</strong> 할당된 프로젝트(업체) 및 팀 멤버 관리</li>
                <li><span>⚙️</span><strong>팀 멤버 - 서브 관리자:</strong> 부여된 권한 내에서 프로젝트 기능 수행</li>
                <li><span>👁️</span><strong>팀 멤버 - 뷰어:</strong> 할당된 프로젝트 정보 조회만 가능</li>
                <li><span>👤</span><strong>등록된 사용자 (Registered User):</strong> 본인 활동 관리 (스캔 기록 등) + 교환권 승인 가능</li>
                <li><span>📱</span><strong>최종 사용자 (End-User / Scanner):</strong> QR 스캔 및 상호작용 (비로그인)</li>
            </ul>
        </div>

        <!-- 주요 기능 -->
        <h2>🚀 주요 기능 (Core Features)</h2>

        <div class="section">
            <h3>🔑 관리자 관리 (Admin Management)</h3>
            <ul class="feature-list">
                <li><strong><span>👤</span>목록 관리:</strong> 역할 기반 조회, 검색, 필터링, 상태 관리</li>
                <li><strong><span>➕</span>생성 & 초대:</strong> 개별 생성 (역할/권한 지정), 이메일 초대, 엑셀 일괄 등록 (수퍼)</li>
                <li><strong><span>✏️</span>수정:</strong> 정보, 역할, 권한 변경 (일부 필드 제한)</li>
                <li><strong><span>🗑️</span>삭제 (논리적):</strong> 미사용 계정 비활성화 (데이터 이관 옵션)</li>
                <li><strong><span>🔁</span>권한 이관:</strong> 프로젝트 담당 관리자 변경 (수퍼 전용)</li>
            </ul>
        </div>

        <div class="section">
            <h3>🏢 프로젝트 관리 (Project Management - 업체 단위)</h3>
            <ul class="feature-list">
                 <li><strong><span>📊</span>목록 관리 (수퍼):</strong> 전체 프로젝트 조회, 검색, 상태 관리</li>
                 <li><strong><span>✨</span>생성 (수퍼):</strong> 신규 프로젝트 등록 및 담당자 지정</li>
                 <li><strong><span>🔧</span>수정 (수퍼):</strong> 프로젝트 정보 및 담당자 변경</li>
                 <li><strong><span>❌</span>삭제 (논리적, 수퍼):</strong> 미사용 프로젝트 비활성화 (조건 확인 필요)</li>
            </ul>
        </div>

        <div class="section">
            <h3>📈 통계 관리 (Statistics Management)</h3>
             <ul class="feature-list">
                 <li><strong><span>👁️‍🗨️</span>통계 조회:</strong> QR 생성/스캔, 랜딩페이지, 이벤트 등 다양한 지표 분석</li>
                 <li><strong><span>⚙️</span>필터링 & 분석:</strong> 기간, 프로젝트, 타입별 데이터 필터링 및 시각화 (차트, 테이블)</li>
                 <li><strong><span>💾</span>다운로드:</strong> 분석된 통계 데이터 엑셀 파일로 다운로드</li>
            </ul>
        </div>

         <div class="section">
            <h3>🤳 QR 코드 관리 (QR Code Management)</h3>
            <ul class="feature-list">
                 <li><strong><span>📜</span>목록 관리:</strong> 생성된 QR 조회, 검색, 필터링, 상태 관리 (미리보기 제공)</li>
                 <li><strong><span>💡</span>생성 (동적 QR):</strong> 다양한 타입(URL, WiFi, 랜딩페이지, 이벤트, 결제 등) 생성, 디자인 커스터마이징</li>
                 <li><strong><span>✍️</span>수정:</strong> 연결 정보, 기본 정보, 디자인 수정 (타입 변경 불가)</li>
                 <li><strong><span>🗑️</span>삭제 (논리적):</strong> 미사용 QR 비활성화 (휴지통 기능, 복구 가능)</li>
            </ul>
        </div>

         <div class="section">
            <h3>📄 랜딩 페이지 관리 (Landing Page Management)</h3>
            <ul class="feature-list">
                 <li><strong><span>📑</span>목록 관리:</strong> 생성된 페이지 조회, 검색, 상태(게시/임시) 관리</li>
                 <li><strong><span>🎨</span>생성 & 편집:</strong> 코딩 없이 WYSIWYG/블록 에디터로 제작 및 수정 (템플릿 제공)</li>
                 <li><strong><span>🔄</span>버전 관리:</strong> 변경 이력 확인 및 이전 버전으로 롤백 기능 (선택)</li>
                 <li><strong><span>🗑️</span>삭제 (논리적):</strong> 미사용 페이지 삭제 (복구 불가, 연결 QR은 유지)</li>
            </ul>
        </div>

        <div class="section">
            <h3>🎉 참석자/이벤트 관리 (Attendee/Event Management)</h3>
            <ul class="feature-list">
                 <li><strong><span>📅</span>이벤트 관리:</strong> 이벤트 생성, 수정, 삭제, 상태 관리 (예정/진행/종료)</li>
                 <li><strong><span>📝</span>사전 신청 양식:</strong> 커스텀 가능한 온라인 신청 양식 제작</li>
                 <li><strong><span>🧑‍🤝‍🧑</span>참석자 관리:</strong> 신청자 목록 확인, 정보 수정, 참석 여부 관리, 엑셀 연동</li>
            </ul>
        </div>

        <div class="section">
            <h3>✅ 교환용 QR 코드 승인 관리 (Approval Management)</h3>
            <ul class="feature-list">
                <li><strong><span>📱</span>모바일 승인:</strong> 권한 사용자가 모바일 친화적 페이지에서 처리</li>
                <li><strong><span>🔎</span>QR 확인:</strong> 스캔 또는 ID 입력으로 교환권 정보 확인 (유효기간, 재고 등)</li>
                <li><strong><span>✔️</span>사용 처리:</strong> 상태/재고/횟수 차감 및 로그 기록 (부정 사용 방지)</li>
            </ul>
        </div>

        <!-- 공통 적용 항목 -->
        <div class="section common-features">
             <h2>🔗 공통 적용 항목 (Common Features)</h2>
             <ul>
                <li><strong>역할 기반 접근 제어 (RBAC):</strong> 권한에 따른 메뉴/기능/데이터 접근 차등 적용</li>
                <li><strong>감사 로그 (Audit Log):</strong> 주요 데이터 변경 이력 (생성, 수정, 삭제) 자동 기록</li>
                <li><strong>논리적 삭제 (Soft Delete):</strong> 데이터 복구 가능성 및 추적 위한 삭제 처리 (일부 제외)</li>
                <li><strong>표준화된 UI/UX:</strong> 리스트(검색/페이징), 생성/수정 폼 등 일관된 인터페이스 제공</li>
                <li><strong>동시 수정 방지:</strong> 데이터 충돌 방지 메커니즘 적용</li>
                <li><strong>보안 강화:</strong> 비밀번호 정책, 데이터 보호, 입력값 검증 등</li>
                <li><strong>성능 최적화:</strong> 대량 데이터 처리 효율성 고려</li>
            </ul>
        </div>

    </div>
</body>
</html>