<!DOCTYPE html>
<html lang="ko">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JWT 인증 시스템 인포그래픽</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+KR:wght@300;400;500;700&display=swap');

        body {
            font-family: 'Noto Sans KR', sans-serif;
            line-height: 1.6;
            background-color: #f8f9fa;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 900px;
            margin: 20px auto;
            background-color: #ffffff;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
            overflow: hidden;
        }
        header {
            background-color: #4a69bd; /* Modern Blue */
            color: #ffffff;
            padding: 30px;
            text-align: center;
            border-bottom: 5px solid #3b5bb0;
        }
        header h1 {
            margin: 0;
            font-size: 2.2em;
            font-weight: 700;
        }
        section {
            padding: 25px 35px;
            border-bottom: 1px solid #e9ecef;
        }
        section:last-child {
            border-bottom: none;
        }
        h2 {
            color: #4a69bd;
            font-size: 1.8em;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #e0e0e0;
            font-weight: 500;
        }
        h3 {
            color: #5a8dee; /* Lighter Blue */
            font-size: 1.4em;
            margin-top: 25px;
            margin-bottom: 15px;
            font-weight: 500;
        }
        ul, ol {
            padding-left: 25px;
            margin-bottom: 15px;
        }
        li {
            margin-bottom: 12px;
        }
        strong {
            color: #3b5bb0; /* Darker emphasis */
            font-weight: 500;
        }
        code {
            background-color: #e9ecef;
            color: #d63384; /* Pinkish for code */
            padding: 0.2em 0.4em;
            border-radius: 4px;
            font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, Courier, monospace;
            font-size: 0.9em;
        }
        .component-list li, .config-list li {
            background-color: #f8f9fa;
            padding: 10px 15px;
            border-radius: 5px;
            border-left: 4px solid #5a8dee;
        }
        .flow-step {
            counter-increment: step-counter;
            margin-bottom: 15px;
            padding: 10px;
            position: relative;
        }
        .flow-step::before {
            content: counter(step-counter);
            position: absolute;
            left: -35px;
            top: 5px;
            background-color: #5a8dee;
            color: white;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 0.9em;
        }
        .flow ol {
            list-style: none;
            padding-left: 40px; /* Space for counter */
            counter-reset: step-counter;
        }
        .token-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .token-card {
            background-color: #f1f3f5;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
        }
        .token-card h3 {
            margin-top: 0;
            color: #495057;
            border-bottom: 1px solid #ced4da;
            padding-bottom: 8px;
        }
        .exception-list li {
            border-left: 4px solid #dc3545; /* Red for exceptions */
            background-color: #fdf2f2;
            padding: 10px 15px;
            border-radius: 5px;
        }
        .important-note {
            background-color: #fff3cd;
            color: #664d03;
            padding: 10px 15px;
            border-radius: 5px;
            border-left: 4px solid #ffc107;
            margin-top: 10px;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            .container {
                margin: 10px auto;
            }
            header {
                padding: 20px;
            }
            header h1 {
                font-size: 1.8em;
            }
            section {
                padding: 20px;
            }
            h2 {
                font-size: 1.5em;
            }
            h3 {
                font-size: 1.2em;
            }
            .flow ol {
                padding-left: 30px;
            }
             .flow-step::before {
                left: -30px;
                width: 22px;
                height: 22px;
                font-size: 0.8em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1>JWT 기반 인증 시스템</h1>
            <p>사용자 인증 및 API 접근 제어를 위한 JWT 시스템 상세 안내</p>
        </header>

        <section id="overview">
            <h2>1. 개요</h2>
            <p>본 시스템은 사용자 인증 및 API 접근 제어를 위해 <strong>JWT(JSON Web Token)</strong>를 활용합니다. 사용자는 로그인을 통해 <strong>Access Token</strong>과 <strong>Refresh Token</strong>을 발급받습니다. API 요청 시에는 Access Token을 사용하며, 만료 시 Refresh Token으로 새로운 Access Token을 재발급 받을 수 있습니다.</p>
        </section>

        <section id="components">
            <h2>2. 주요 컴포넌트</h2>
            <ul class="component-list">
                <li><strong>AuthController:</strong> 로그인, 토큰 갱신, 로그아웃 등 사용자 인증 관련 HTTP 요청 처리</li>
                <li><strong>AuthService:</strong> 실제 인증 로직 (로그인, 토큰 발급/갱신, 로그아웃) 수행</li>
                <li><strong>JwtUtil:</strong> JWT 생성, 검증, 정보 추출 등 유틸리티 기능 제공</li>
                <li><strong>UserMapper:</strong> MyBatis를 통해 DB에서 사용자 정보(이메일, 비밀번호 등) 조회</li>
                <li><strong>UserMapper.xml:</strong> 사용자 관련 SQL 쿼리 정의</li>
                <li><strong>RefreshTokenMapper:</strong> MyBatis를 통해 DB에서 Refresh Token 저장, 조회, 수정, 삭제</li>
                <li><strong>RefreshTokenMapper.xml:</strong> Refresh Token 관련 SQL 쿼리 정의</li>
                <li><strong>GlobalExceptionHandler:</strong> 인증 예외 포함, 전역 예외 처리 및 표준 오류 응답 반환</li>
            </ul>
        </section>

        <section id="flow" class="flow">
            <h2>3. 인증 흐름</h2>

            <div>
                <h3>3.1. 로그인 (<code>POST /api/way/auth/login</code>)</h3>
                <ol>
                    <li class="flow-step">사용자가 이메일과 비밀번호를 요청 본문에 담아 전송합니다.</li>
                    <li class="flow-step"><code>AuthController</code>가 요청을 받아 <code>AuthService.login</code>을 호출합니다.</li>
                    <li class="flow-step"><code>AuthService</code>는 <code>UserMapper</code>로 사용자 정보를 조회하고 비밀번호를 검증합니다.</li>
                    <li class="flow-step">인증 성공 시, <code>JwtUtil</code>을 사용하여 Access Token과 Refresh Token을 생성합니다.</li>
                    <li class="flow-step">생성된 Refresh Token 정보 (사용자 이메일, 토큰 값, 만료일)를 <code>RefreshTokenMapper</code>를 통해 DB에 저장/업데이트 합니다 (기존 토큰은 <code>tokenId</code> 기준 업데이트).</li>
                    <li class="flow-step"><code>AuthController</code>는 Access Token, Refresh Token, 만료 시간 등을 포함한 응답을 반환합니다.</li>
                </ol>
            </div>

            <div>
                <h3>3.2. 토큰 갱신 (<code>POST /api/way/auth/refresh</code>)</h3>
                <ol>
                    <li class="flow-step">클라이언트가 만료되지 않은 유효한 Refresh Token을 요청 본문에 담아 전송합니다.</li>
                    <li class="flow-step"><code>AuthController</code>가 요청을 받아 <code>AuthService.refreshToken</code>을 호출합니다.</li>
                    <li class="flow-step"><code>AuthService</code>는 다음을 검증합니다:
                        <ul>
                            <li><code>JwtUtil</code>: Refresh Token 자체의 유효성(서명, 형식 등) 검증 (<code>InvalidTokenException</code> 발생 가능).</li>
                            <li><code>RefreshTokenMapper</code>: 해당 Refresh Token의 DB 존재 여부 및 만료 여부 확인 (<code>InvalidTokenException</code>, <code>ExpiredTokenException</code> 발생 가능).</li>
                        </ul>
                    </li>
                    <li class="flow-step">검증 성공 시, <code>JwtUtil</code>로 새로운 Access Token을 생성합니다 (현재 로직은 Access Token만 재발급).</li>
                    <li class="flow-step"><code>AuthController</code>는 새로운 Access Token 정보 (및 필요시 Refresh Token 정보)를 응답으로 반환합니다.</li>
                </ol>
            </div>

             <div>
                <h3>3.3. 로그아웃 (<code>POST /api/way/auth/logout</code>)</h3>
                <ol>
                    <li class="flow-step">인증된 사용자(헤더에 Access Token 포함)가 로그아웃을 요청합니다.</li>
                    <li class="flow-step"><code>AuthController</code>가 요청을 받아 (인증 필터를 통해 사용자 식별 후) <code>AuthService.logout</code>을 호출합니다.</li>
                    <li class="flow-step"><code>AuthService</code>는 요청 사용자의 이메일을 기반으로 <code>RefreshTokenMapper.deleteByUserEmail</code>을 호출하여 DB에서 Refresh Token을 삭제합니다.</li>
                    <li class="flow-step"><code>AuthController</code>는 성공 메시지를 반환합니다.</li>
                </ol>
            </div>

            <div>
                <h3>3.4. API 접근</h3>
                <ol>
                    <li class="flow-step">클라이언트는 <code>Authorization: Bearer &lt;AccessToken&gt;</code> 헤더와 함께 API를 요청합니다.</li>
                    <li class="flow-step"><code>JwtAuthenticationFilter</code>(가정)가 헤더에서 Access Token을 추출하고 <code>JwtUtil</code>로 유효성을 검증합니다.</li>
                    <li class="flow-step">토큰이 유효하면, 사용자 정보를 추출하여 Spring Security 컨텍스트에 인증 정보를 설정합니다.</li>
                    <li class="flow-step">요청이 해당 API 엔드포인트로 전달되어 처리됩니다.</li>
                    <li class="flow-step">토큰이 유효하지 않거나 만료된 경우, 필터에서 <code>401 Unauthorized</code> 또는 <code>403 Forbidden</code> 응답을 반환합니다.</li>
                </ol>
            </div>
        </section>

        <section id="tokens">
            <h2>4. 토큰 정보</h2>
            <div class="token-info">
                <div class="token-card">
                    <h3>Access Token</h3>
                    <p><strong>용도:</strong> API 접근 권한 증명</p>
                    <p><strong>만료 시간:</strong> 비교적 짧음 (예: 30분, <code>jwt.access-token-expiration-ms</code> 설정)</p>
                    <p><strong>저장 위치:</strong> 클라이언트 메모리 (권장)</p>
                </div>
                <div class="token-card">
                    <h3>Refresh Token</h3>
                    <p><strong>용도:</strong> 새로운 Access Token 발급 요청</p>
                    <p><strong>만료 시간:</strong> 비교적 긺 (예: 7일, <code>jwt.refresh-token-expiration-ms</code> 설정)</p>
                    <p><strong>저장 위치:</strong> 서버 DB (<code>refresh_token</code> 테이블) 및 클라이언트 안전한 저장소 (예: HttpOnly 쿠키)</p>
                </div>
            </div>
        </section>

        <section id="exceptions">
            <h2>5. 예외 처리</h2>
            <p><code>GlobalExceptionHandler</code>에서 주요 인증 관련 예외를 처리하고 표준 에러 응답(<code>ApiResponseDto</code>)을 반환합니다.</p>
            <ul class="exception-list">
                <li><strong><code>InvalidTokenException</code>:</strong> 토큰 형식이 잘못되었거나, 서명이 유효하지 않거나, DB에 존재하지 않는 Refresh Token일 경우 발생.</li>
                <li><strong><code>ExpiredTokenException</code>:</strong> 토큰 유효 기간 만료 시 발생 (Access Token은 필터, Refresh Token은 갱신 서비스 로직에서 주로 처리).</li>
                <li><strong><code>BadCredentialsException</code>:</strong> 로그인 시 아이디 또는 비밀번호 불일치 시 발생.</li>
            </ul>
             <p>표준 에러 코드 예시: <code>INVALID_TOKEN</code>, <code>EXPIRED_TOKEN</code>, <code>AUTHENTICATION_FAILED</code></p>
        </section>

        <section id="config">
            <h2>6. 설정 (<code>application.yml</code>)</h2>
             <ul class="config-list">
                <li><code>jwt.secret</code>: JWT 서명 비밀 키. <strong class="important-note">절대 하드코딩 금지! 환경 변수 등으로 안전하게 관리하세요.</strong></li>
                <li><code>jwt.access-token-expiration-ms</code>: Access Token 만료 시간 (밀리초).</li>
                <li><code>jwt.refresh-token-expiration-ms</code>: Refresh Token 만료 시간 (밀리초).</li>
            </ul>
        </section>
    </div>
</body>
</html>