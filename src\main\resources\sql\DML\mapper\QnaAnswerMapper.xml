<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="kr.wayplus.wayplus_qr.mapper.QnaAnswerMapper">

    <!-- 답변 DTO를 위한 결과 맵 -->
    <resultMap id="qnaAnswerMap" type="kr.wayplus.wayplus_qr.dto.response.QnaAnswerResponseDto">
        <id property="qnaAnswerId" column="qna_answer_id"/>
        <result property="qnaQuestionId" column="qna_question_id"/>
        <result property="answerContent" column="answer_content"/>
        <result property="createUserEmail" column="create_user_email"/>
        <result property="createDate" column="create_date"/>
        <result property="updateUserEmail" column="update_user_email"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteUserEmail" column="delete_user_email"/>
        <result property="deleteDate" column="delete_date"/>
        <result property="useYn" column="use_yn"/>
        <result property="deleteYn" column="delete_yn"/>
    </resultMap>

    <!-- 답변 등록 -->
    <insert id="insertAnswer">
        INSERT INTO qna_answers (
            qna_question_id,
            answer_content,
            create_user_email,
            use_yn,
            delete_yn
        ) VALUES (
            #{qnaQuestionId},
            #{answerContent},
            #{createUserEmail},
            'Y',
            'N'
        )
    </insert>
    
    <!-- 최근 등록된 ID 조회 -->
    <select id="selectLastInsertId" resultType="java.lang.Long">
        SELECT LAST_INSERT_ID()
    </select>
    
    <!-- 특정 ID의 답변 조회 -->
    <select id="selectAnswerById" resultMap="qnaAnswerMap">
        SELECT *
        FROM qna_answers
        WHERE qna_answer_id = #{qnaAnswerId}
          AND delete_yn = 'N'
    </select>
    
    <!-- 특정 질문에 대한 답변 조회 -->
    <select id="selectAnswerByQuestionId" resultMap="qnaAnswerMap">
        SELECT *
        FROM qna_answers
        WHERE qna_question_id = #{qnaQuestionId}
          AND delete_yn = 'N'
    </select>
    
    <!-- 특정 질문에 대한 답변 존재 여부 체크 -->
    <select id="countAnswerByQuestionId" resultType="int">
        SELECT COUNT(*)
        FROM qna_answers
        WHERE qna_question_id = #{qnaQuestionId}
          AND delete_yn = 'N'
    </select>
    
    <!-- 답변 수정 -->
    <update id="updateAnswer">
        UPDATE qna_answers
        SET 
            answer_content = #{answerContent},
            update_user_email = #{updateUserEmail},
            last_update_date = CURRENT_TIMESTAMP
        WHERE 
            qna_answer_id = #{qnaAnswerId}
            AND delete_yn = 'N'
    </update>
    
    <!-- 답변 삭제 (논리적 삭제) -->
    <update id="deleteAnswer">
        UPDATE qna_answers
        SET 
            delete_yn = 'Y',
            delete_user_email = #{deleteUserEmail},
            delete_date = CURRENT_TIMESTAMP
        WHERE 
            qna_answer_id = #{qnaAnswerId}
            AND delete_yn = 'N'
    </update>
    
</mapper>
