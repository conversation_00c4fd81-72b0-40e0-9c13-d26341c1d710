package kr.wayplus.wayplus_qr.dto.request;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@JsonIgnoreProperties(ignoreUnknown = true)
public class PreRegistrationFormFieldRequestDto {

    @JsonProperty("fieldLabel")
    @NotBlank(message = "필드 레이블은 필수입니다.")
    private String fieldLabel;

    @JsonProperty("fieldName")
    @NotBlank(message = "필드 이름은 필수입니다.")
    private String fieldName;

    @JsonProperty("fieldType")
    @NotBlank(message = "필드 타입은 필수입니다.")
    private String fieldType;

    @JsonProperty("isRequiredYn")
    private String isRequiredYn;

    @JsonProperty("helpText")
    private String helpText;

    @JsonProperty("options")
    private List<Object> options;

    @JsonProperty("displayOrder")
    private Integer displayOrder;
}
