package kr.wayplus.wayplus_qr.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.*;
import kr.wayplus.wayplus_qr.entity.MenuEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

public class MenuDto {

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "메뉴 생성 요청 DTO")
    public static class CreateRequest {
        @Schema(description = "상위 메뉴 ID", example = "1")
        private Long parentMenuId;

        @NotBlank(message = "메뉴 코드는 필수입니다.")
        @Size(max = 100, message = "메뉴 코드는 100자를 초과할 수 없습니다.")
        @Pattern(regexp = "^[A-Z0-9_]+$", message = "메뉴 코드는 영문 대문자, 숫자, 언더스코어만 사용 가능합니다.")
        @Schema(description = "메뉴 코드", example = "QR_MANAGEMENT")
        private String menuCode;

        @NotBlank(message = "메뉴 이름은 필수입니다.")
        @Size(max = 255, message = "메뉴 이름은 255자를 초과할 수 없습니다.")
        @Schema(description = "메뉴 이름", example = "QR 관리")
        private String menuName;

        @Size(max = 500, message = "메뉴 URL은 500자를 초과할 수 없습니다.")
        @Schema(description = "메뉴 URL", example = "/admin/qr")
        private String menuUrl;

        @Size(max = 100, message = "메뉴 아이콘은 100자를 초과할 수 없습니다.")
        @Schema(description = "메뉴 아이콘", example = "fas fa-qrcode")
        private String menuIcon;

        @NotNull(message = "메뉴 레벨은 필수입니다.")
        @Schema(description = "메뉴 레벨", example = "1")
        private Integer menuLevel;

        @Schema(description = "표시 순서", example = "1")
        private Integer displayOrder;

        @Pattern(regexp = "^(ACTIVE|INACTIVE)$", message = "메뉴 상태는 ACTIVE 또는 INACTIVE만 가능합니다.")
        @Schema(description = "메뉴 상태", example = "ACTIVE")
        private String status;

        public MenuEntity.Menu toEntity(String createUserEmail) {
            return MenuEntity.Menu.builder()
                    .parentMenuId(this.parentMenuId)
                    .menuCode(this.menuCode)
                    .menuName(this.menuName)
                    .menuUrl(this.menuUrl)
                    .menuIcon(this.menuIcon)
                    .menuLevel(this.menuLevel)
                    .displayOrder(this.displayOrder != null ? this.displayOrder : 0)
                    .status(this.status != null ? this.status : "ACTIVE")
                    .createUserEmail(createUserEmail)
                    .updateUserEmail(createUserEmail)
                    .createDate(LocalDateTime.now())
                    .lastUpdateDate(LocalDateTime.now())
                    .useYn("Y")
                    .deleteYn("N")
                    .build();
        }
    }

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "메뉴 수정 요청 DTO")
    public static class UpdateRequest {
        @Schema(description = "상위 메뉴 ID", example = "1")
        private Long parentMenuId;

        @NotBlank(message = "메뉴 이름은 필수입니다.")
        @Size(max = 255, message = "메뉴 이름은 255자를 초과할 수 없습니다.")
        @Schema(description = "메뉴 이름", example = "QR 관리")
        private String menuName;

        @Size(max = 500, message = "메뉴 URL은 500자를 초과할 수 없습니다.")
        @Schema(description = "메뉴 URL", example = "/admin/qr")
        private String menuUrl;

        @Size(max = 100, message = "메뉴 아이콘은 100자를 초과할 수 없습니다.")
        @Schema(description = "메뉴 아이콘", example = "fas fa-qrcode")
        private String menuIcon;

        @NotNull(message = "메뉴 레벨은 필수입니다.")
        @Schema(description = "메뉴 레벨", example = "1")
        private Integer menuLevel;

        @Schema(description = "표시 순서", example = "1")
        private Integer displayOrder;

        @NotBlank(message = "메뉴 상태는 필수입니다.")
        @Pattern(regexp = "^(ACTIVE|INACTIVE)$", message = "메뉴 상태는 ACTIVE 또는 INACTIVE만 가능합니다.")
        @Schema(description = "메뉴 상태", example = "ACTIVE")
        private String status;
    }

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "메뉴 응답 DTO")
    public static class Response {
        @Schema(description = "메뉴 ID", example = "1")
        private Long menuId;

        @Schema(description = "상위 메뉴 ID", example = "null")
        private Long parentMenuId;

        @Schema(description = "메뉴 코드", example = "QR_MANAGEMENT")
        private String menuCode;

        @Schema(description = "메뉴 이름", example = "QR 관리")
        private String menuName;

        @Schema(description = "메뉴 URL", example = "/admin/qr")
        private String menuUrl;

        @Schema(description = "메뉴 아이콘", example = "fas fa-qrcode")
        private String menuIcon;

        @Schema(description = "메뉴 레벨", example = "1")
        private Integer menuLevel;

        @Schema(description = "표시 순서", example = "1")
        private Integer displayOrder;

        @Schema(description = "메뉴 상태", example = "ACTIVE")
        private String status;

        @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
        @Schema(description = "생성일시", example = "2024-01-01 10:00:00")
        private LocalDateTime createDate;

        @Schema(description = "하위 메뉴 목록")
        private List<Response> children;

        @Schema(description = "접근 가능 여부", example = "true")
        private Boolean accessible;
    }

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "사용자별 권한 설정 요청 DTO")
    public static class UserPermissionRequest {
        @NotNull(message = "메뉴 ID는 필수입니다.")
        @Schema(description = "메뉴 ID", example = "1")
        private Long menuId;

        @NotBlank(message = "사용자 이메일은 필수입니다.")
        @Email(message = "올바른 이메일 형식이어야 합니다.")
        @Schema(description = "사용자 이메일", example = "<EMAIL>")
        private String userEmail;

        @Pattern(regexp = "^(Y|N)?$", message = "접근 가능 여부는 Y 또는 N만 가능합니다.")
        @Schema(description = "접근 가능 여부 (선택사항, 미입력시 CRUD 권한에 따라 자동 설정)", example = "Y")
        private String isAccessible;

        @Pattern(regexp = "^(Y|N)$", message = "읽기 권한은 Y 또는 N만 가능합니다.")
        @Schema(description = "읽기 권한", example = "Y")
        private String canRead;

        @Pattern(regexp = "^(Y|N)$", message = "쓰기 권한은 Y 또는 N만 가능합니다.")
        @Schema(description = "쓰기(생성) 권한", example = "N")
        private String canWrite;

        @Pattern(regexp = "^(Y|N)$", message = "수정 권한은 Y 또는 N만 가능합니다.")
        @Schema(description = "수정 권한", example = "N")
        private String canUpdate;

        @Pattern(regexp = "^(Y|N)$", message = "삭제 권한은 Y 또는 N만 가능합니다.")
        @Schema(description = "삭제 권한", example = "N")
        private String canDelete;

        @Size(max = 1000, message = "권한 부여 사유는 1000자를 초과할 수 없습니다.")
        @Schema(description = "권한 부여 사유", example = "특별 권한 부여")
        private String permissionNote;

        public MenuEntity.MenuUserPermission toEntity(String createUserEmail) {
            // isAccessible 값이 없으면 CRUD 권한 중 하나라도 Y이면 Y, 모두 N이면 N으로 설정
            String isAccessible = this.isAccessible;
            if (isAccessible == null || isAccessible.trim().isEmpty()) {
                String canRead = this.canRead != null ? this.canRead : "Y";
                String canWrite = this.canWrite != null ? this.canWrite : "N";
                String canUpdate = this.canUpdate != null ? this.canUpdate : "N";
                String canDelete = this.canDelete != null ? this.canDelete : "N";

                // CRUD 권한 중 하나라도 Y이면 접근 가능으로 설정
                isAccessible = ("Y".equals(canRead) || "Y".equals(canWrite) || "Y".equals(canUpdate) || "Y".equals(canDelete)) ? "Y" : "N";
            }

            return MenuEntity.MenuUserPermission.builder()
                    .menuId(this.menuId)
                    .userEmail(this.userEmail)
                    .isAccessible(isAccessible)
                    .canRead(this.canRead != null ? this.canRead : "Y")
                    .canWrite(this.canWrite != null ? this.canWrite : "N")
                    .canUpdate(this.canUpdate != null ? this.canUpdate : "N")
                    .canDelete(this.canDelete != null ? this.canDelete : "N")
                    .permissionNote(this.permissionNote)
                    .createUserEmail(createUserEmail)
                    .updateUserEmail(createUserEmail)
                    .createDate(LocalDateTime.now())
                    .lastUpdateDate(LocalDateTime.now())
                    .useYn("Y")
                    .deleteYn("N")
                    .build();
        }
    }

    @Getter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "메뉴 권한 정보 응답 DTO")
    public static class PermissionResponse {
        @Schema(description = "메뉴 ID", example = "1")
        private Long menuId;

        @Schema(description = "메뉴 이름", example = "QR 관리")
        private String menuName;

        @Schema(description = "역할별 권한 목록")
        private List<RolePermission> rolePermissions;

        @Schema(description = "사용자별 권한 목록")
        private List<UserPermission> userPermissions;

        @Getter
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class RolePermission {
            @Schema(description = "역할 ID", example = "PROJECT_ADMIN")
            private String roleId;

            @Schema(description = "역할 이름", example = "프로젝트 관리자")
            private String roleName;

            @Schema(description = "접근 가능 여부", example = "Y")
            private String isAccessible;

            @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
            @Schema(description = "생성일시", example = "2024-01-01 10:00:00")
            private LocalDateTime createDate;
        }

        @Getter
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public static class UserPermission {
            @Schema(description = "사용자 이메일", example = "<EMAIL>")
            private String userEmail;

            @Schema(description = "사용자 이름", example = "홍길동")
            private String userName;

            @Schema(description = "접근 가능 여부", example = "Y")
            private String isAccessible;

            @Schema(description = "읽기 권한", example = "Y")
            private String canRead;

            @Schema(description = "쓰기(생성) 권한", example = "N")
            private String canWrite;

            @Schema(description = "수정 권한", example = "N")
            private String canUpdate;

            @Schema(description = "삭제 권한", example = "N")
            private String canDelete;

            @Schema(description = "권한 부여 사유", example = "특별 권한 부여")
            private String permissionNote;

            @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "Asia/Seoul")
            @Schema(description = "생성일시", example = "2024-01-01 10:00:00")
            private LocalDateTime createDate;
        }
    }
}
