package kr.wayplus.wayplus_qr.config.external;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(prefix = "qr-quiz-mapping-api")
@Getter
@Setter
public class QrQuizMappingApiProperties {
    private String baseUrl;
    private String secretKey;
    private String headerName;
    private Endpoints endpoints;
    private Timeout timeout;

    @Getter
    @Setter
    public static class Endpoints {
        private String mapping;
        private String unMapping;
    }

    @Getter
    @Setter
    public static class Timeout {
        private int connect;
        private int read;
    }
}
