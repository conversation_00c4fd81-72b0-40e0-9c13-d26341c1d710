package kr.wayplus.wayplus_qr.dto.request;

import jakarta.validation.constraints.NotNull;
import kr.wayplus.wayplus_qr.entity.InquiryStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 문의 상태 변경 요청 DTO (SUPER_ADMIN 전용).
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryStatusUpdateRequestDto {

    @NotNull
    private InquiryStatus newStatus;
}
