package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * QR 코드 스캔 시간 통계 DTO
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QrScanTimeStatsDto {
    private Map<Integer, Long> hourlyDistribution; // 시간대별 스캔 분포
    private Map<String, Long> weekdayDistribution; // 요일별 스캔 분포
}
