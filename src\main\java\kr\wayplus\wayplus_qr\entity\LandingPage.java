package kr.wayplus.wayplus_qr.entity;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Getter
@Setter
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class LandingPage {

    private Long landingPageId;

    private Long projectId;

    private String projectName;

    private String pageTitle;

    private String description;

    private String contentJson;

    private LandingPageStatus status;

    private LocalDateTime validFromDate;

    private LocalDateTime validToDate;

    private String createUserEmail;

    private LocalDateTime createDate;

    private String updateUserEmail;

    private LocalDateTime lastUpdateDate;

    private String deleteUserEmail;

    private LocalDateTime deleteDate;

    private String useYn;

    private String deleteYn;
}
