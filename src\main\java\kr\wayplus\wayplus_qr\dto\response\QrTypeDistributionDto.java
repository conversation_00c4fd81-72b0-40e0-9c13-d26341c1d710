package kr.wayplus.wayplus_qr.dto.response;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

/**
 * QR 코드 타입별 분포 DTO
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class QrTypeDistributionDto {
    private Map<String, Long> typeDistribution; // QR 코드 타입별 개수
    private Map<String, Double> typePercentage; // QR 코드 타입별 비율(%)
}
